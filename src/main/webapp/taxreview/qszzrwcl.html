<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="Cache-Control" CONTENT="no-cache">
	<meta http-equiv="Pragma" content="no-cache">
	<meta name="format-detection" content="telephone=no" />
	<title>欠税追征任务处理</title>
	<link rel="stylesheet" href="css/bootstrap.min.css" />
	<link rel="stylesheet" href="./js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/reset.css" />
	<style>
		* {
			box-sizing: border-box
		}

		.main {
			background-color: #f2f2f2;
			width: 100%;
			min-width: 1200px;
			overflow: hidden;
			min-height: 100vh;
		}

		.myContainer {
			width: 1200px;
			margin: 0 auto;
			padding: 24px;
			background-color: #FFFFFF;
			min-height: 100vh;
		}

		.seachBox {
			border-bottom: 1px solid #e0e1e6;
		}

		.btnBox {
			text-align: right;
			height: 60px;
			line-height: 60px;
		}

		.myThead {
			background-color: #2789EE;
			color: white;
			font-size: 14px;
		}

		.myBtn {
			padding: 6px 15px !important;
			margin-right: 10px;
		}

		.rightBtn {
			margin-right: 24px;
		}
	</style>
</head>
<script src="./js/jquery-3.7.1.min.js"></script>
<script src="./js/bootstrap.min.js"></script>
<script src="./js/layui/layui.js"></script>
<script src="./js/qszzrwcl.js"></script>
<script src="./js/TranscendSign.js"></script>

<body>
<div class="main">
	<div class="myContainer">
		<div class="layui-tab layui-tab-brief tabBox" lay-filter="my-handle">
			<button onclick="location.href='/clf/sfzgzxxt.html'"
					class="layui-btn layui-btn-primary layui-border-blue goback">返回首页</button>
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="tab1">追征企业清单</li>
				<li lay-id="tab2">大额欠税阻止出境企业上报</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<div class="seachBox">
						<form class="form-inline">
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrmc">纳税人名称</label>
										<input type="text" class="form-control" id="nsrmc" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="shxydm">社会信用代码</label>
										<input type="text" class="form-control" id="shxydm" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="qslb">欠税类别</label>
										<select id="qslb" class="filter-select">
											<option value="">全部</option>
											<option value="大额欠税">大额欠税</option>
											<option value="小额欠税">小额欠税</option>
											<option value="一般欠税">一般欠税</option>
										</select>
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="sfhdlhd">是否恒大类恒大</label>
										<select id="sfhdlhd" class="filter-select">
											<option value="">全部</option>
											<option value="是">是</option>
											<option value="否">否</option>
										</select>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="gly">管理员</label>
										<input type="text" class="form-control" id="gly" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="zgswks">主管税务科所</label>
										<input type="text" class="form-control" id="zgswks" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
									<!-- <button type="button" class="btn btn-default">重置</button> -->
									<button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
								</div>
							</div>
						</form>
					</div>
					<div class="btnBox">
						<button onclick="dcFn()" type="button" class="btn btn-primary">导出</button>
						<button onclick="kkjsqdFn()" type="button" class="btn btn-primary">扣款解锁清单</button>
					</div>
					<div class="tableBox">
						<table class="table tableqsList table-hover">
							<thead class="myThead">
							<tr>
								<th class="fixed-left-column">序号</th>
								<th>统计日期</th>
								<th>社会信用代码</th>
								<th>纳税人名称</th>
								<th>纳税人状态</th>
								<th>法定代表人姓名</th>
								<th>法定代表人身份证号码</th>
								<th>欠税类别</th>
								<th>经营情况</th>
								<th>主管税务科所</th>
								<th>街道乡镇</th>
								<th>税收管理员</th>
								<th>欠税余额</th>
								<th>往年陈欠</th>
								<th>本年新欠</th>
								<th>备注</th>
								<th class="fixed-column">操作</th>
							</tr>
							</thead>
							<tbody id="dataList">

							</tbody>
						</table>
					</div>
					<nav aria-label="Page navigation" class="myPagination">
						<ul class="pagination" id="pagination"></ul>
					</nav>
				</div>
				<div class="layui-tab-item">
					<div class="seachBox">
						<form class="form-inline">
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrmc1">纳税人名称</label>
										<input type="text" class="form-control" id="nsrmc1" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrsbh">纳税人状态</label>
										<input type="text" class="form-control" id="nsrzt" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
									<!-- <button type="button" class="btn btn-default">重置</button> -->
									<button onclick="deqszzcjsearch(1)" type="button"
											class="btn btn-primary">查询</button>
								</div>
							</div>
						</form>
					</div>
					<div class="btnBox">
						<button onclick="dedcFn()" type="button" class="btn btn-primary">导出</button>
						<button onclick="zzcjrwsbFn()" type="button" class="btn btn-primary">阻止出境任务上报</button>
					</div>
					<div class="tableBox">
						<table class="table tableqsList table-hover">
							<thead class="myThead">
							<tr>
								<th class="fixed-left-column"><input type="checkbox" id="tableqs"
																	 onclick="checkAll()" /></th>
								<th>序号</th>
								<th>统计日期</th>
								<th>社会信用代码</th>
								<th>纳税人名称</th>
								<th>纳税人状态</th>
								<th>法定代表人姓名</th>
								<th>法定代表人身份证号码</th>
								<th>欠税类别</th>
								<th>经营情况</th>
								<th>主管税务科所</th>
								<th>街道乡镇</th>
								<th>税收管理员</th>
								<th>欠税余额</th>
								<th>往年陈欠</th>
								<th>本年新欠</th>
								<th>税款处理类型</th>
								<th>是否已上报</th>
								<th>是否确认阻止出境</th>
								<th>备注</th>
								<th class="fixed-column">操作</th>
							</tr>
							</thead>
							<tbody id="deqszzDataList">

							</tbody>
						</table>
					</div>
					<nav aria-label="Page navigation" class="myPagination">
						<ul class="pagination" id="pagination1"></ul>
					</nav>
				</div>
			</div>
		</div>
	</div>
	<!-- 追征企业清单-扣款解锁清单 -->
	<div class="modal-mask fjModal kkjsqdModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>扣款解锁清单</h3>
				<span class="close-btn" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;flex: 1;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th><input type="checkbox" id="tableInfo" onclick="checkAllInfo()" /></th>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>税收处理类型</th>
					</tr>
					</thead>
					<tbody id="kkjsqdList">

					</tbody>
				</table>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="qdqrFn()">扣款解锁</button>
			</div>
		</div>
	</div>
	<!-- 追征企业清单-追征任务反馈 -->
	<div class="modal-mask fjModal zzrwfkModal">
		<div class="modal-content" style="width:800px!important;max-height: 300px!important;">
			<div class="modal-header">
				<h3>追征任务反馈</h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="modal-body" style="flex: 1;">
				<form class="layui-form">
					<div class="row">
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">追征任务完成日期</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="zzrwwcrq" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">追征措施</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="zzcs" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">追征结果</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="zzjg" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">追征人员</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="zzry" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="zzrwfk()">保存</button>
			</div>
		</div>
	</div>
	<!-- 大额欠税阻止出境企业上报-追征结果 -->
	<div class="modal-mask fjModal zzjgModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>追征结果</h3>
				<span class="close-btn closeJbrModal" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>追征任务下发日期</th>
						<th>追征任务状态</th>
						<th>追征人员</th>
						<th>追征任务完成日期</th>
						<th>追征措施</th>
						<th>追征结果</th>
					</tr>
					</thead>
					<tbody id="zzjgList">

					</tbody>
				</table>
			</div>
		</div>
	</div>
	<!-- 大额欠税阻止出境企业上报-阻止出境任务反馈 -->
	<div class="modal-mask fjModal dezzcjModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>阻止出境任务反馈</h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="filter-upload-row">
				<label class="upload-button">上传附件<input class="uploadButton" type="file" id="fileInput"
														multiple></label>
			</div>
			<div class="tableBox2 tableBox" style="flex: 1;border-bottom: 1px solid #e0e1e6;margin-bottom: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th class="fixed-left-column">序号</th>
						<th>附件名称</th>
						<th>附件大小</th>
						<th class="fixed-column">操作</th>
					</tr>
					</thead>
					<tbody id="FJFileList">

					</tbody>
				</table>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="rwfkFn()">确认</button>
			</div>
		</div>
	</div>
	<!-- 预览图片-->
	<div id="previewImgModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false"
		 role="dialog" aria-labelledby="exampleModalLabel3" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<span class="modal-title" id="exampleModalLabel3">预览</span>
				</div>
				<div class="modal-body">
					<img id="previewImg" style="display: none;" src="">
					<iframe id="previewPdf" style="display: none;" src="" width="100%" height="600px"></iframe>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>

</div>
</body>

</html>