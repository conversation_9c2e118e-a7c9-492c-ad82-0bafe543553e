<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="Cache-Control" CONTENT="no-cache">
	<meta http-equiv="Pragma" content="no-cache">
	<meta name="format-detection" content="telephone=no" />
	<title>岗责设置</title>
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link rel="stylesheet" href="./js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/szscreset.css">
	</link>
	<style>
		* {
			box-sizing: border-box
		}

		.main {
			background-color: #f2f2f2;
			width: 100%;
			min-width: 1200px;
			overflow: hidden;
			min-height: 100vh;
		}

		.myContainer {
			width: 1200px;
			margin: 0 auto;
			padding: 24px;
			background-color: #FFFFFF;
			min-height: 100vh;
		}

		#ssmc {
			background: #eee;
			cursor: not-allowed;
		}

		.myThead {
			background-color: #2789EE;
			color: white;
			font-size: 14px;
		}
	</style>
</head>

<body>
	<div class="main">
		<div class="myContainer">
			<div class="seachBox">
				<form class="form-inline">
					<div class="row">
						<div class="col-sm-3 col-md-3 col-lg-3">
							<div class="form-group">
								<label for="ssmc">税所名称</label>
								<select id="ssmc" disabled class="filter-select">
									<option value="虎溪街道">虎溪街道</option>
									<option value="曾家镇">曾家镇</option>
									<option value="金凤镇">金凤镇</option>
									<option value="走马镇">走马镇</option>
									<option value="巴福镇">巴福镇</option>
									<option value="石板镇">石板镇</option>
									<option value="白市驿镇">白市驿镇</option>
									<option value="含谷镇">含谷镇</option>
									<option value="香炉山街道">香炉山街道</option>
									<option value="西永街道">西永街道</option>
								</select>
							</div>
						</div>
						<div class="col-sm-3 col-md-3 col-lg-3">
							<div class="form-group">
								<label for="xm">姓名</label>
								<input type="text" class="form-control" id="xm" placeholder="请输入">
							</div>
						</div>
						<div class="col-sm-3 col-md-3 col-lg-3">
							<div class="form-group">
								<label for="qdsx">清单事项</label>
								<input type="text" class="form-control" id="qdsx" placeholder="请输入">
							</div>
						</div>
						<div class="col-sm-3 col-md-3 col-lg-3" style="text-align: right;">
							<!-- <button type="button" class="btn btn-default">重置</button> -->
							<button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
						</div>
					</div>
				</form>
			</div>
			<div class="tableBox">
				<table class="table tableqsList table-hover">
					<thead class="myThead">
						<tr>
							<th>序号</th>
							<th>姓名</th>
							<th>房间号</th>
							<th>管户数量</th>
							<th>职责分工</th>
							<th>职责分组</th>
							<th>清单事项</th>
							<th>其他服务事项</th>
							<th>联系电话</th>
							<th>AB角</th>
						</tr>
					</thead>
					<tbody id="dataList">

					</tbody>
				</table>
			</div>
			<div id="chartBox1" style="width: 600px;height:400px;margin: 0 auto;"></div>
		</div>
	</div>
	<script src="./js/jquery-3.7.1.min.js"></script>
	<script src="./js/bootstrap.min.js"></script>
	<script src="./js/echarts.min.js"></script>
	<script src="./js/layui/layui.js"></script>
	<script src="./js/gzsz.js"></script>
</body>

</html>