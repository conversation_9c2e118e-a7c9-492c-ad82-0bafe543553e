<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="Cache-Control" CONTENT="no-cache">
	<meta http-equiv="Pragma" content="no-cache">
	<meta name="format-detection" content="telephone=no" />
	<title>欠税管理</title>
	<link rel="stylesheet" href="css/bootstrap.min.css" />
	<link rel="stylesheet" href="./js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/reset.css" />
	<style>
		* {
			box-sizing: border-box
		}

		.main {
			background-color: #f2f2f2;
			width: 100%;
			min-width: 1200px;
			overflow: hidden;
			min-height: 100vh;
		}

		.myContainer {
			width: 1200px;
			margin: 0 auto;
			padding: 24px;
			background-color: #FFFFFF;
			min-height: 100vh;
		}

		.seachBox {
			border-bottom: 1px solid #e0e1e6;
		}

		.btnBox {
			text-align: right;
			height: 60px;
			line-height: 60px;
		}

		.myThead {
			background-color: #2789EE;
			color: white;
			font-size: 14px;
		}

		.myBtn {
			padding: 6px 15px !important;
			margin-right: 10px;
		}

		.rightBtn {
			margin-right: 24px;
		}
	</style>
</head>
<script src="./js/jquery-3.7.1.min.js"></script>
<script src="./js/bootstrap.min.js"></script>
<script src="./js/layui/layui.js"></script>
<script src="./js/qsgl.js"></script>
<script src="./js/TranscendSign.js"></script>

<body>
<div class="main">
	<div class="myContainer">
		<div class="layui-tab layui-tab-brief tabBox" lay-filter="my-handle">
			<button onclick="location.href='/clf/sfzgzxxt.html'"
					class="layui-btn layui-btn-primary layui-border-blue goback">返回首页</button>
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="tab1">欠税清单</li>
				<li lay-id="tab2">欠税统计</li>
				<li lay-id="tab3">大额欠税阻止出境</li>
				<li lay-id="tab4">阻止出境人员</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<div class="seachBox">
						<form class="form-inline">
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrmc">纳税人名称</label>
										<input type="text" class="form-control" id="nsrmc" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="shxydm">社会信用代码</label>
										<input type="text" class="form-control" id="shxydm" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="qslb">欠税类别</label>
										<select id="qslb" class="filter-select">
											<option value="">全部</option>
											<option value="大额欠税">大额欠税</option>
											<option value="小额欠税">小额欠税</option>
											<option value="一般欠税">一般欠税</option>
										</select>
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="sfhdlhd">是否恒大类恒大</label>
										<select id="sfhdlhd" class="filter-select">
											<option value="">全部</option>
											<option value="是">是</option>
											<option value="否">否</option>
										</select>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="gly">管理员</label>
										<input type="text" class="form-control" id="gly" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="zgswks">主管税务科所</label>
										<input type="text" class="form-control" id="zgswks" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
									<!-- <button type="button" class="btn btn-default">重置</button> -->
									<button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
								</div>
							</div>
						</form>
					</div>
					<div class="btnBox">
						<button onclick="dcFn()" type="button" class="btn btn-primary">导出</button>
						<button onclick="whzqFn(1)" type="button" class="btn btn-primary">维护征期</button>
						<button onclick="jegshFn()" type="button" class="btn btn-primary">金额格式化</button>
						<button onclick="kkjs()" type="button" class="btn btn-primary">扣款解锁</button>
						<button onclick="zzrwxfFn()" type="button" class="btn btn-primary">追征任务下发</button>
					</div>
					<div class="tableBox">
						<table class="table tableqsList table-hover">
							<thead class="myThead">
							<tr>
								<th class="fixed-left-column"><input type="checkbox" id="tableqs"
																	 onclick="checkAll()" /></th>
								<th>序号</th>
								<th>统计日期</th>
								<th>数据来源</th>
								<th>社会信用代码</th>
								<th>纳税人名称</th>
								<th>纳税人信用等级</th>
								<th>纳税人状态</th>
								<th>法定代表人姓名</th>
								<th>法定代表人身份证号码</th>
								<th>应征发生日期</th>
								<th>欠税类别</th>
								<th>经营情况</th>
								<th>主管税务科所</th>
								<th>街道乡镇</th>
								<th>税收管理员</th>
								<th>欠税余额</th>
								<th>往年陈欠</th>
								<th>本年新欠</th>
								<th>欠税风险等级</th>
								<th>清偿能力得分</th>
								<th>多缴金额</th>
								<th>增值税留抵金额</th>
								<th>税款处理类型</th>
								<!-- <th>扣款解锁是否确认</th> -->
								<th>是否新欠</th>
								<th>是否稽查</th>
								<th>是否恒大类恒大</th>
								<th>催缴措施</th>
								<th>催缴结果</th>
								<th>催缴状态</th>
								<th>备注</th>
								<th class="fixed-column">操作</th>
							</tr>
							</thead>
							<tbody id="dataList">

							</tbody>
						</table>
					</div>
					<nav aria-label="Page navigation" class="myPagination">
						<ul class="pagination" id="pagination"></ul>
					</nav>
				</div>
				<div class="layui-tab-item">
					<div class="btnBox">
						<button onclick="qstjdcFn()" type="button" class="btn btn-primary">导出</button>
					</div>
					<div class="tableBox">
						<table class="table table-hover">
							<thead class="myThead">
							<tr>
								<th></th>
								<th>欠税总额</th>
								<th>陈欠总额</th>
								<th>新欠总额</th>
							</tr>
							</thead>
							<tbody id="qstjdataList">

							</tbody>
						</table>
					</div>
				</div>
				<div class="layui-tab-item">
					<div class="seachBox">
						<form class="form-inline">
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="deqsnsrmc">纳税人名称</label>
										<input type="text" class="form-control" id="deqsnsrmc" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrszt">纳税人状态</label>
										<input type="text" class="form-control" id="nsrszt" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
									<!-- <button type="button" class="btn btn-default">重置</button> -->
									<button onclick="deqssearch(1)" type="button"
											class="btn btn-primary">查询</button>
								</div>
							</div>
						</form>
					</div>
					<div class="btnBox">
						<button onclick="dedcFn()" type="button" class="btn btn-primary">导出</button>
						<button onclick="degdFn()" type="button" class="btn btn-primary">归档</button>
						<button onclick="zzcjmdFn()" type="button" class="btn btn-primary">阻止出境企业名单</button>
					</div>
					<div class="tableBox">
						<table class="table table-hover">
							<thead class="myThead">
							<tr>
								<th class="fixed-left-column"><input type="checkbox" id="tabledeqs"
																	 onclick="checkDeqsAll()" /></th>
								<th>序号</th>
								<th>社会信用代码</th>
								<th>纳税人名称</th>
								<th>纳税人状态</th>
								<th>法定代表人姓名</th>
								<th>法定代表人身份证号码</th>
								<th>经营情况</th>
								<th>主管税务科所</th>
								<th>街道乡镇</th>
								<th>税收管理员</th>
								<th>欠税余额</th>
								<th>往年陈欠</th>
								<th>本年新欠</th>
								<th>欠税风险等级</th>
								<th>清偿能力得分</th>
								<th>多缴金额</th>
								<th>增值税留抵金额</th>
								<th>是否恒大类恒大</th>
								<th>催缴状态</th>
								<th>催缴措施</th>
								<th>催缴结果</th>
								<th>归档状态</th>
								<th>阻止出境状态</th>
								<th>备注</th>
								<th class="fixed-column">操作</th>
							</tr>
							</thead>
							<tbody id="deqsdataList">

							</tbody>
						</table>
					</div>
					<nav aria-label="Page navigation" class="myPagination">
						<ul class="pagination" id="pagination1"></ul>
					</nav>
				</div>
				<div class="layui-tab-item">
					<div class="seachBox">
						<form class="form-inline">
							<div class="row">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrmc2">纳税人名称</label>
										<input type="text" class="form-control" id="nsrmc2" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="form-group">
										<label for="nsrszt2">纳税人状态</label>
										<input type="text" class="form-control" id="nsrszt2" placeholder="请输入">
									</div>
								</div>
								<div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
									<!-- <button type="button" class="btn btn-default">重置</button> -->
									<button onclick="zzcjsearch(1)" type="button"
											class="btn btn-primary">查询</button>
								</div>
							</div>
						</form>
					</div>
					<div class="btnBox">
						<button onclick="zzcjdcFn()" type="button" class="btn btn-primary">导出</button>
					</div>
					<div class="tableBox">
						<table class="table table-hover">
							<thead class="myThead">
							<tr>
								<th class="fixed-left-column">序号</th>
								<th>社会信用代码</th>
								<th>纳税人名称</th>
								<th>纳税人状态</th>
								<th>主管税务科所</th>
								<th>街道乡镇</th>
								<th>税收管理员</th>
								<th>欠税余额</th>
								<th>阻止出境人员名称</th>
								<th>阻止出境开始日期</th>
								<th>阻止出境结束日期</th>
								<th class="fixed-column">操作</th>
							</tr>
							</thead>
							<tbody id="zzcjrydataList">

							</tbody>
						</table>
					</div>
					<nav aria-label="Page navigation" class="myPagination">
						<ul class="pagination" id="pagination2"></ul>
					</nav>
				</div>
			</div>
		</div>
	</div>
	<!-- 欠税清单-弹窗-start -->
	<!-- 维护征期 -->
	<div class="modal-mask fjModal whzqModal">
		<div class="modal-content" style="width:1000px!important;max-height: 500px!important;">
			<div class="modal-header">
				<h3>维护征期</h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="modal-body" style="flex: 1;">
				<form class="layui-form">
					<div class="row" style="margin-bottom: 40px;">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">年份</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="nfIpt" placeholder="请选择年份">
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">1月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq0" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">2月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq1" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">3月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq2" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">4月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq3" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">5月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq4" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">6月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq5" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">7月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq6" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">8月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq7" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">9月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq8" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">10月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq9" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">11月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq10" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-4 col-md-4 col-lg-4">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">12月征期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input yfIpt" id="zq11" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="whzqTrueFn()">保存</button>
			</div>
		</div>
	</div>
	<!-- 追征结果 -->
	<div class="modal-mask fjModal zzjgModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>追征结果</h3>
				<span class="close-btn closeJbrModal" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>追征任务下发日期</th>
						<th>追征任务状态</th>
						<th>追征人员</th>
						<th>追征任务完成日期</th>
						<th>追征措施</th>
						<th>追征结果</th>
					</tr>
					</thead>
					<tbody id="zzjgList">

					</tbody>
				</table>
			</div>
		</div>
	</div>
	<!-- 追征措施 -->
	<div class="modal-mask fjModal zzcsModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>追征措施</h3>
				<span class="close-btn closeQyModal" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>追征任务下发日期</th>
						<th>追征任务状态</th>
						<th>追征人员</th>
						<th>追征任务完成日期</th>
						<th>追征措施</th>
						<th>追征结果</th>
					</tr>
					</thead>
					<tbody id="zzcsList">

					</tbody>
				</table>
			</div>
		</div>
	</div>
	<!-- 欠税明细 -->
	<div class="modal-mask fjModal qsmxModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>欠税明细</h3>
				<span class="close-btn closeQyModal" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;flex: 1;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th>序号</th>
						<th>纳税人名称</th>
						<th>应征发送日期</th>
						<th>征收项目</th>
						<th>征收品目</th>
						<th>细目名称</th>
						<th>应补（退）税额</th>
						<th>税款所属期起</th>
						<th>税款所属期止</th>
						<th>缴款期限</th>
					</tr>
					</thead>
					<tbody id="qsmxList">

					</tbody>
				</table>
			</div>
			<nav aria-label="Page navigation" class="myPagination">
				<ul class="pagination" id="qsmxpagination"></ul>
			</nav>
		</div>
	</div>
	<!-- 编辑 -->
	<div class="modal-mask fjModal bjModal">
		<div class="modal-content" style="width:800px!important;max-height: 500px!important;">
			<div class="modal-header">
				<h3>编辑</h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="modal-body" style="flex: 1;">
				<form class="layui-form">
					<div class="row" style="margin-bottom: 40px;">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">经营情况</label>
								<div class="layui-input-inline">
									<input type="text" style="padding-left: 0!important;" class="layui-input"
										   id="jyqk" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
					<div class="row" style="margin-bottom: 40px;">
						<div class="layui-form-item layui-form-text">
							<label class="layui-form-label">备注</label>
							<div class="layui-input-block textarea">
								<textarea placeholder="请输入内容" class="layui-textarea" id="bjbz"></textarea>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="qsqdBJ()">保存</button>
			</div>
		</div>
	</div>
	<!-- 欠税清单-弹窗-end -->

	<!-- 大额欠税阻止出境-弹窗-start -->
	<!-- 阻止出境企业名单 -->
	<div class="modal-mask fjModal dezzcjmdModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>阻止出境企业名单</h3>
				<span class="close-btn" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;flex: 1;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th class="fixed-left-column"><input type="checkbox" id="dezzcjtable"
															 onclick="checkAllDE()" /></th>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>纳税人状态</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>追征任务处理状态</th>
						<th>追征任务完成日期</th>
						<th>追征人员</th>
						<th>追征措施</th>
						<th>追征结果</th>
						<th>是否确认阻止出境</th>
					</tr>
					</thead>
					<tbody id="dezzqyList">

					</tbody>
				</table>
			</div>
			<nav aria-label="Page navigation" class="myPagination">
				<ul class="pagination" id="paginationZZQY"></ul>
			</nav>
			<div class="action-buttons">
				<button class="btn-default" onclick="deqxzzFn()">取消阻止</button>
				<button class="confirm-btn" onclick="deqrzzFn()">确认阻止</button>
			</div>
		</div>
	</div>
	<!-- 追征结果 -->
	<div class="modal-mask fjModal dezzjgModal">
		<div class="modal-content">
			<div class="modal-header">
				<h3>追征结果</h3>
				<span class="close-btn" onclick="closeModal()">×</span>
			</div>
			<div class="tableBox2 tableBox" style="margin-top: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th>序号</th>
						<th>统计日期</th>
						<th>纳税人名称</th>
						<th>欠税类别</th>
						<th>主管税务科所</th>
						<th>税收管理员</th>
						<th>追征任务下发日期</th>
						<th>追征任务状态</th>
						<th>追征人员</th>
						<th>追征任务完成日期</th>
						<th>追征措施</th>
						<th>追征结果</th>
					</tr>
					</thead>
					<tbody id="dezzjgList">

					</tbody>
				</table>
			</div>
		</div>
	</div>
	<!-- 阻止出境 -->
	<div class="modal-mask fjModal dezzcjModal">
		<div class="modal-content" style="width:1200px!important;">
			<div class="modal-header">
				<h3 id="zzcjTit"></h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="filter-upload-row">
				<label class="upload-button">上传附件<input class="uploadButton" type="file" id="fileInput"
														multiple></label>
			</div>
			<div class="tableBox2 tableBox" style="flex: 1;border-bottom: 1px solid #e0e1e6;margin-bottom: 20px;">
				<table class="table table-hover">
					<thead class="myThead myThead2">
					<tr>
						<th class="fixed-left-column">序号</th>
						<th>附件名称</th>
						<th>附件大小</th>
						<th class="fixed-column">操作</th>
					</tr>
					</thead>
					<tbody id="FJFileList">

					</tbody>
				</table>
			</div>
			<form class="layui-form deForm">
				<div class="row">
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">阻止出境人员姓名</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="zzcjryxm" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">联系电话</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="lxdh" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">身份证号</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="sfzh" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">推送日期</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input yfIpt" id="tsrq" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">税务管理员</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="swgly" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
					<div class="col-sm-4 col-md-4 col-lg-4">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">管理员联系方式</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" id="glylxfs" placeholder="请输入">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12 col-md-12 col-lg-12">
						<div class="layui-form-item layui-form-text">
							<label class="layui-form-label">备注</label>
							<div class="layui-input-block textarea">
								<textarea placeholder="请输入内容" class="layui-textarea" id="debz"></textarea>
							</div>
						</div>
					</div>
				</div>
			</form>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="tsgaFn()">推送公安</button>
			</div>
		</div>
	</div>
	<!-- 大额欠税阻止出境-弹窗-end -->
	<!-- 预览图片-->
	<div id="previewImgModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false"
		 role="dialog" aria-labelledby="exampleModalLabel3" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<span class="modal-title" id="exampleModalLabel3">预览</span>
				</div>
				<div class="modal-body">
					<img id="previewImg" style="display: none;" src="">
					<iframe id="previewPdf" style="display: none;" src="" width="100%" height="600px"></iframe>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>
</div>
</body>

</html>