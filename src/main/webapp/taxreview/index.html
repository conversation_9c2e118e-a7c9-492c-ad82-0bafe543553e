<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Pragma" content="no-cache">
    <meta name="format-detection" content="telephone=no" />
    <title>存量房税费审核</title>
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="./js/layui/css/layui.css" />
    <link rel="stylesheet" href="css/bootstrap.min.css">
    </link>
    <style>
        * {
            box-sizing: border-box
        }

        .main {
            background-color: #f2f2f2;
            width: 100%;
            min-width: 1200px;
            overflow: hidden;
            min-height: 100vh;
        }

        .myContainer {
            width: 1200px;
            margin: 0 auto;
            padding: 24px;
            background-color: #FFFFFF;
            min-height: 100vh;
        }

        .seachBox {
            border-bottom: 1px solid #e0e1e6;
        }

        .btnBox {
            text-align: right;
            height: 60px;
            line-height: 60px;
        }

        .myThead {
            background-color: #2789EE;
            color: white;
            font-size: 14px;
        }

        .myBtn {
            padding: 6px 15px !important;
            margin-right: 10px;
        }

        .rightBtn {
            margin-right: 24px;
        }

        .midGoback {
            height: 32px !important;
            line-height: 32px !important;
            float: left;
            margin-top: 16px;
            font-size: 12px;
        }


        .fjBox {
            /* flex: 1; */
            width: 50%;
            height: 100%;
            position: relative;
            border-right: 1px solid #ccc;
        }

        .fjBox .action-buttons {
            padding: 5px 0;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
        }

        #previewImgModal {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            display: none;
            border-right: 1px solid #ccc;
        }

        #previewImgModal .modal-content {
            height: 100%;
        }

        .preBtns {
            display: inline-block;
            float: right;
        }

        .fjBox-modal-content {
            background: #f6f6f6;
            color: #192236;
            height: 100%;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            background-color: #FFFFFF;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            -webkit-box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2), 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
            box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2), 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            outline: 0;
            position: relative;
        }

        .fjBox-modal-header {
            padding: 0;
            margin: 0;
            height: 40px;
            line-height: 45px;
            font-size: 14px;
            background: #FFFFFF;
            color: #192236;
            font-weight: 600;
            text-indent: 12px;
            border-bottom: 1px solid #e0e1e6;
        }

        .xq-allModal-content {
            background: #f6f6f6;
            color: #192236;
            width: 50%;
            height: 100%;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            background-color: #FFFFFF;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            overflow: hidden;
            outline: 0;
        }

        #xqbtn {
            width: 50%;
        }

        #fjTbBox {
            width: auto;
        }

        #fullModal {
            padding: none;
        }

        #fullModal .modal-dialog {
            width: 100%;
            height: 100%;
            margin: 0;
        }

        #fullModal .modal-content {
            height: 100%;
        }

        #fullModal .modal-body {
            max-height: 94vh;
        }

        .infoTable {
            overflow: hidden;
            overflow-x: scroll;
        }

        .infoTable table th {
            min-width: 140px !important;
        }

        .ellipsis {
            max-width: 150px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .checkLabel {
            text-align: left !important;
            width: auto !important;
        }
    </style>
</head>
<script src="./js/jquery-3.7.1.min.js"></script>
<script src="./js/bootstrap.min.js"></script>

<body>
    <div class="main">
        <div class="myContainer">
            <div class="seachBox">
                <form class="form-inline">
                    <div class="row">
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachacceptanceNum">房管受理编号</label>
                                <input type="text" class="form-control" id="seachacceptanceNum" placeholder="请输入">
                            </div>
                        </div>
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachrealEstateUnitNum">不动产单元号</label>
                                <input type="text" class="form-control" id="seachrealEstateUnitNum" placeholder="请输入">
                            </div>
                        </div>
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachsellerName">卖方姓名</label>
                                <input type="text" class="form-control" id="seachsellerName" placeholder="请输入">
                            </div>
                        </div>
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachbuyerName">买方姓名</label>
                                <input type="text" class="form-control" id="seachbuyerName" placeholder="请输入">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachsellerPhone">卖方联系方式</label>
                                <input type="text" class="form-control" id="seachsellerPhone" placeholder="请输入">
                            </div>
                        </div>
                        <div class="col-sm-3 col-md-3 col-lg-3">
                            <div class="form-group">
                                <label for="seachbuyerPhone">买方联系方式</label>
                                <input type="text" class="form-control" id="seachbuyerPhone" placeholder="请输入">
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
                            <!--<button type="button" class="btn btn-default">重置</button>-->
                            <button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="btnBox">
                <button onclick="location.href='/clf/sfzgzxxt.html'"
                    class="layui-btn layui-btn-primary layui-border-blue midGoback">返回首页</button>
            </div>
            <div class="tableBox">
                <table class="table table-hover">
                    <thead class="myThead">
                        <tr>
                            <!--                    <th class="fixed-left-column"><input type="checkbox" id="table1" onclick="checkAll()"/></th>-->
                            <th class="fixed-left-column">序号</th>
                            <th>申请编号</th>
                            <th>申请时间</th>
                            <th>申请状态</th>
                            <th>房管受理编号</th>
                            <th>不动产单元号</th>
                            <th>楼盘（小区）名称</th>
                            <th>坐落地址</th>

                            <th>房屋类型</th>
                            <th>住宅类型</th>
                            <th>建筑面积</th>
                            <th>申报交易总价</th>
                            <th>申请人</th>
                            <th>申请人证件号码</th>
                            <th>购买人</th>
                            <th>购买人证件号码</th>
                            <th>房管中心办理人</th>
                            <th>初审人员</th>
                            <th>复审人员</th>
                            <th>更新时间</th>
                            <th class="fixed-column">操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataList"></tbody>
                </table>
            </div>
            <nav aria-label="Page navigation" class="myPagination">
                <ul class="pagination" id="pagination"></ul>
            </nav>
        </div>
        <!-- 预览模态框 -->
        <!-- <div id="previewModal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static"
            data-keyboard="false" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <span class="modal-title" id="exampleModalLabel">版式文件预览</span>
                    </div>
                    <div class="modal-body">
                        <iframe id="bswjPreviewPdf" src="" width="100%" height="600px"></iframe>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div> -->
        <!-- 详情模态框 new add 20250707-->
        <div id="xqModal" class="allModal">
            <div class="fjBox">
                <div class="fjBox-modal-content">
                    <div class="fjBox-modal-header">
                        <span>附件</span>
                        <!-- <span class="close-btn">×</span> -->
                    </div>
                    <div class="filter-upload-row">
                        <div class="filter-container">
                            <label for="attachmentType" class="filter-label">附件类型：</label>
                            <select id="attachmentType" class="filter-select">
                                <option value="存量房交易信息采集表">存量房交易信息采集表</option>
                                <option value="完税凭证">完税凭证</option>
                                <option value="存量房交易税费申报表（承受方）">存量房交易税费申报表（承受方）</option>
                                <option value="存量房交易税费申报表（转让方）">存量房交易税费申报表（转让方）</option>
                                <option value="完税发票">完税发票</option>
                                <option value="卖方材料">卖方材料</option>
                                <option value="买方材料">买方材料</option>
                                <option value="房管材料">房管材料</option>
                                <option value="缴费付款码">缴费付款码</option>
                                <option value="其他材料">其他材料</option>


                                <option value="税务证明事项告知承诺书（卖方承诺书）">税务证明事项告知承诺书（卖方承诺书）</option>
                                <option value="税务证明事项告知承诺书（买方承诺书）">税务证明事项告知承诺书（买方承诺书）</option>
                                <option value="存量商业用房评估信息采集表（办公用房）">存量商业用房评估信息采集表（办公用房）</option>
                                <option value="存量商业用房评估信息采集表（车位）">存量商业用房评估信息采集表（车位）</option>
                                <option value="存量商业用房评估信息采集表（公寓）">存量商业用房评估信息采集表（公寓）</option>
                                <option value="存量商业用房评估信息采集表（商服用房）">存量商业用房评估信息采集表（商服用房）</option>
                                <option value="重庆市存量住房信息采集表（个人转让存量住房适用）">重庆市存量住房信息采集表（个人转让存量住房适用）</option>
                            </select>
                        </div>
                        <label class="upload-button">上传附件<input class="uploadButton" type="file" id="fileInput"
                                multiple></label>
                    </div>
                    <div class="tableBox" id="fjTbBox">
                        <table class="table table-hover">
                            <thead class="myThead myThead2">
                                <tr>
                                    <th class="fixed-left-column"><input type="checkbox" id="tablefj"
                                            onclick="checkAllfj()" /></th>
                                    <th>序号</th>
                                    <th>附件名称</th>
                                    <th>附件类型</th>
                                    <!-- <th>附件来源</th> -->
                                    <th>附件大小</th>
                                    <!-- <th>纳税人是否签字</th>
                                    <th>税务是否已确认</th> -->
                                    <!--<th>税务是否需要签字</th>
                                    <th>税务是否需要盖章</th>-->
                                    <th>是否归档</th>
                                    <th class="fixed-column">操作</th>
                                </tr>
                            </thead>
                            <tbody id="FJFileList"></tbody>
                        </table>
                    </div>
                    <div class="action-buttons gdBtn" style="text-align: center;">
                        <button class="confirm-btn gd">归档</button>
                        <!-- <button class="btn-default close-btn">返回</button> -->
                    </div>

                    <div id="previewImgModal">
                        <div style="height: 100%;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <span class="modal-title" id="exampleModalLabel3">预览</span>
                                    <div class="preBtns">
                                        <button type="button" id="fullBtn" class="btn btn-default myBtn">全屏预览</button>
                                        <button type="button" id="prevBtn" class="btn btn-default myBtn">上一个</button>
                                        <button type="button" id="nextBtn" class="btn btn-default myBtn">下一个</button>
                                    </div>
                                </div>
                                <div class="modal-body">
                                    <img id="previewImg"
                                        style="display: none;width: 100%; height: 100%; object-fit: contain;" src="">
                                    <iframe id="previewPdf" style="display: none;" src="" width="100%"
                                        height="100%"></iframe>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" id="preBtn" class="btn btn-default myBtn">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="xq-allModal-content">
                <div class="allModal-header">
                    <span class="allModal-title">详情</span>
                </div>
                <div class="allModal-body">
                    <div class="allModal-info">
                        <div class="row">
                            <div class="row">
                                <div class="titleClass">
                                    <span></span>房管信息
                                </div>
                            </div>
                            <div class="infoTable infoTable1">
                                <table id="propertyTable">
                                    <tr>
                                        <th>受理编号</th>
                                        <td id="fgAcceptanceNum"></td>
                                        <th>不动产单元号</th>
                                        <td id="fgRealEstateUnitNum"></td>
                                    </tr>
                                    <tr>
                                        <th>卖方姓名</th>
                                        <td id="fgSellerName"></td>
                                        <th>卖方证件号码</th>
                                        <td id="fgSellerIdNum"></td>
                                    </tr>
                                    <tr>
                                        <th>买方姓名</th>
                                        <td id="fgBuyerName"></td>
                                        <th>买方证件号码</th>
                                        <td id="fgBuyerIdNum"></td>
                                    </tr>
                                    <tr>
                                        <th>房管中心办理人</th>
                                        <td id="fgzxblr" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <th>初审人员</th>
                                        <td id="csrymc"></td>
                                        <th>复审人员</th>
                                        <td id="fsrymc"></td>
                                    </tr>
                                    <tr>
                                        <th>备注</th>
                                        <td id="fgRemark" colspan="3"></td>
                                    </tr>

                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <div class="row">
                                <div class="titleClass">
                                    <span></span>房屋交易信息
                                </div>
                            </div>
                            <div class="infoTable infoTable1">
                                <table id="propertyTable">
                                    <tr>
                                        <th>不动产权证地址</th>
                                        <td id="houseLocation" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <th>产权证书</th>
                                        <td id="realEstateCertificateNum"></td>
                                        <th>不动产单元号</th>
                                        <td id="realEstateUnitNum"></td>
                                    </tr>
                                    <tr>
                                        <th>建筑面积</th>
                                        <td id="buildingArea"></td>
                                        <th>房屋用途</th>
                                        <td id="housePurpose"></td>
                                    </tr>
                                    <tr>
                                        <th>执证类型</th>
                                        <td id="certificateType"></td>
                                        <th>付款方式</th>
                                        <td id="paymentMethod"></td>
                                    </tr>
                                    <tr>
                                        <th>房屋类型</th>
                                        <td id="houseType"></td>
                                        <th>小区名称</th>
                                        <td id="realEstateName"></td>
                                    </tr>
                                    <tr>
                                        <th>住宅类型</th>
                                        <td id="residentialType"></td>
                                        <th>建筑功能</th>
                                        <td id="buildingImplement"></td>
                                    </tr>
                                    <tr>
                                        <th>套型</th>
                                        <td id="suiteType"></td>
                                        <th>户型</th>
                                        <td id="houseRoom"></td>
                                    </tr>
                                    <tr>
                                        <th>建筑结构</th>
                                        <td id="buildingStructure"></td>
                                        <th>楼层</th>
                                        <td id="floor"></td>
                                    </tr>
                                    <tr>
                                        <th>层高</th>
                                        <td id="floorHeight"></td>
                                        <th>地上总层数</th>
                                        <td id="groundFloors"></td>
                                    </tr>
                                    <tr>
                                        <th>地下总层数</th>
                                        <td id="undergroundFloors"></td>
                                        <th>朝向</th>
                                        <td id="toward"></td>
                                    </tr>
                                    <tr>
                                        <th>物管等级</th>
                                        <td id="pmLevel"></td>
                                        <th>竣工年份</th>
                                        <td id="buildingCompletionYear"></td>
                                    </tr>
                                    <tr>
                                        <th>土地使用权类型</th>
                                        <td id="landUseRightType"></td>
                                        <th>土地使用终止日期</th>
                                        <td id="landUseRightEndDate"></td>
                                    </tr>
                                    <tr>
                                        <th>剩余土地使用权终止日期</th>
                                        <td id="remainingLandUseErm"></td>
                                        <th>配套设施</th>
                                        <td id="supportingFacility"></td>
                                    </tr>
                                    <tr>
                                        <th>房屋形状</th>
                                        <td id="houseShape"></td>
                                        <th>套内面积</th>
                                        <td id="internalArea"></td>
                                    </tr>
                                    <tr>
                                        <th>车位类型</th>
                                        <td id="parkingSpaceType"></td>
                                        <th>交易日期</th>
                                        <td id="transactionDate"></td>
                                    </tr>
                                    <tr>
                                        <th>申报交易总价</th>
                                        <td id="totalPrice"></td>
                                        <th>申报交易单价</th>
                                        <td id="unitPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>房屋其他说明</th>
                                        <td id="houseOtherDescription" colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <th>备注</th>
                                        <td id="remark" colspan="3"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <div class="row">
                                <div class="titleClass">
                                    <span></span>卖方产权人信息
                                </div>
                            </div>
                            <div class="infoTable">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>是否委托办理</th>
                                            <th>是否直系亲属转让</th>
                                            <th>权利人类型</th>
                                            <th>姓名</th>
                                            <th>证件类型</th>
                                            <th>证件号码</th>
                                            <th>婚姻状况</th>
                                            <th>已持有房屋数量</th>
                                            <th>联系电话</th>
                                            <th>共有情况</th>
                                            <th>持有比例</th>
                                            <th>交易比例</th>
                                            <th>是否享受个税减免</th>
                                            <th>家庭成员姓名(关系)</th>
                                            <th>家庭成员身份证号</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sellerBox"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <div class="row">
                                <div class="titleClass">
                                    <span></span>买方产权人信息
                                </div>
                            </div>
                            <div class="infoTable">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>是否委托办理</th>
                                            <th>权利人类型</th>
                                            <th>姓名</th>
                                            <th>证件类型</th>
                                            <th>证件号码</th>
                                            <th>婚姻状况</th>
                                            <th>已持有房屋数量</th>
                                            <th>联系电话</th>
                                            <th>共有情况</th>
                                            <th>持有比例</th>
                                            <th>是否享受契税减免</th>
                                            <th>契税减免项</th>
                                            <th>配偶姓名</th>
                                            <th>配偶身份证号</th>
                                            <th>未成年子女1姓名</th>
                                            <th>未成年子女1身份证号</th>
                                            <th>未成年子女2姓名</th>
                                            <th>未成年子女2身份证号</th>
                                        </tr>
                                    </thead>
                                    <tbody id="buyerBox"></tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                    <div class="allModal-footer" style="z-index: 10000;" id="xqbtn">
                        <button type="button" class="btn btn-primary myBtn rightBtn" id="closeXqModal"
                            data-dismiss="modal">返回</button>
                        <button onclick="showBatchRejectModal('退回')" type="button" class="btn btn-primary myBtn"
                            data-dismiss="modal">驳回</button>
                        <!--<button onclick="showOverModal('受理')" type="button" class="btn btn-primary myBtn" data-dismiss="modal">受理</button>
                <button onclick="showOverModal('审核通过')" type="button" class="btn btn-primary myBtn" data-dismiss="modal">审核通过</button>
                <button onclick="showRejectModal('驳回')" type="button" class="btn btn-primary myBtn" data-dismiss="modal">驳回</button>
                <button onclick="showRejectModal('中止')" type="button" class="btn btn-primary myBtn" data-dismiss="modal">中止</button>
                <button type="button" class="btn btn-primary myBtn rightBtn" id="closeXqModal" data-dismiss="modal">返回</button>-->
                    </div>
                </div>
            </div>
        </div>
        <!-- 全屏预览模态框 -->
        <div id="fullModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog"
            aria-labelledby="exampleModalLabel3" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content" style="display: flex; flex-direction: column;height: 100vh; /* 容器高度设为视口高度，便于观察效果 */
    justify-content: space-around;">
                    <div class="modal-header" style="display: flex;">
                        <span class="modal-title" id="exampleModalLabel3">预览</span>
                        <div class="preBtns" style="margin: 0 auto;">
                            <button type="button" id="fullPrevBtn" class="btn btn-default myBtn">上一个</button>
                            <button type="button" id="fullNextBtn" class="btn btn-default myBtn">下一个</button>
                        </div>
                        <span id="fullClose"
                            style="display: inline-block;width: 30px;text-align: center;cursor: pointer;">x</span>
                    </div>
                    <div class="modal-body" style="flex: 1;">
                        <img id="fullFreviewImg" style="display: none;width: 100%; height: 100%; object-fit: contain;"
                            src="">
                        <iframe id="fullPreviewPdf" style="display: none;" src="" width="100%" height="100%"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <!-- 驳回、中止原因-->
        <div id="rejectModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false"
            role="dialog" aria-labelledby="rejectTit" aria-hidden="true">
            <div class="modal-dialog myModalBody" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <span class="modal-title" id="rejectTit"></span>
                    </div>
                    <div class="modal-body">
                        <form class="form-inline">
                            <div class="row">
                                <div class="col-sm-12 col-md-12 col-lg-12">
                                    <div class="form-group">
                                        <textarea class="form-control myTextArea" id="rejectVal" rows="3"
                                            placeholder="请输入"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default myBtn" data-dismiss="modal">取消</button>
                        <button onclick="rejectTrue()" type="button" class="btn btn-primary myBtn">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 批量退回给 房管、卖方、买方 提示 -->
        <div id="batchRejectModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false"
            role="dialog" aria-labelledby="rejectTit" aria-hidden="true">
            <div class="modal-dialog myModalBody" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <span class="modal-title" id="batchRejectTit"></span>
                    </div>
                    <div class="modal-body">

                        <form class="form-inline">
                            <div class="row">
                                <div class="col-sm-3 col-md-3 col-lg-">
                                    <label style="width: 100%;padding: 0;">退回至：</label>
                                </div>
                                <div class="col-sm-9 col-md-9 col-lg-9">
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="checkbox-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="房管">
                                                房管
                                            </label>
                                            <label class="checkbox-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="买方">
                                                买方
                                            </label>
                                            <label class="checkbox-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="卖方">
                                                卖方
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <form class="form-inline" id="kxgForm" style="display: none;">
                            <div class="row">
                                <div class="col-sm-3 col-md-3 col-lg-3">
                                    <label style="width: 100%;padding: 0;">需修改模块：</label>
                                </div>
                                <div class="col-sm-9 col-md-9 col-lg-9">
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="kxg-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="房屋信息">
                                                房屋信息
                                            </label>
                                            <label class="kxg-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="交易信息">
                                                交易信息
                                            </label>
                                            <label class="kxg-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="卖方信息">
                                                卖方信息
                                            </label>
                                            <!-- <label class="kxg-item">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="附件上传">
                                                附件上传
                                            </label> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <form class="form-inline" id="xcqForm" style="display: none;">
                            <div class="row">
                                <div class="col-sm-3 col-md-3 col-lg-">
                                    <label style="width: 100%;padding: 0;">需重签文件：</label>
                                </div>
                                <div class="col-sm-9 col-md-9 col-lg-9">
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <label class="xcq-item checkLabel">
                                                <input id="isSeller"
                                                    style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="个税减免承诺书">
                                                个税减免承诺书
                                            </label>
                                            <label class="xcq-item checkLabel">
                                                <input id="isBuyer"
                                                    style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="契税减免承诺书">
                                                契税减免承诺书
                                            </label>
                                            <label class="xcq-item checkLabel">
                                                <input style="vertical-align: text-bottom;margin-right: 5px;"
                                                    type="checkbox" value="信息采集表">
                                                信息采集表
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <form class="form-inline">
                            <div class="row">
                                <div class="col-sm-3 col-md-3 col-lg-3">
                                    <label style="width: 100%;padding: 0;">退回原因：</label>
                                </div>
                                <div class="col-sm-9 col-md-9 col-lg-9">
                                    <div class="form-group">
                                        <textarea class="form-control myTextArea" id="batchRejectVal" rows="3"
                                            placeholder="请输入"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default myBtn" data-dismiss="modal">取消</button>
                        <button onclick="rejectTrue()" type="button" class="btn btn-primary myBtn">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 审核通过、已缴费、已办结 提示 -->
        <div id="overModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog"
            aria-labelledby="overTit" aria-hidden="true">
            <div class="modal-dialog myMsgBody" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <span class="modal-title" id="overTit">提示</span>
                    </div>
                    <div class="modal-body">
                        <div id="overMsg"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default myBtn" data-dismiss="modal">取消</button>
                        <button onclick="overTrue()" type="button" class="btn btn-primary myBtn">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 附件管理 0214 -->
        <div class="modal-mask fjModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>附件管理</h3>
                    <span class="close-btn">×</span>
                </div>
                <div class="filter-upload-row">
                    <div class="filter-container">
                        <label for="attachmentType" class="filter-label">附件类型：</label>
                        <select id="attachmentType" class="filter-select">
                            <option value="存量房交易信息采集表">存量房交易信息采集表</option>
                            <option value="完税凭证">完税凭证</option>
                            <option value="存量房交易税费申报表（承受方）">存量房交易税费申报表（承受方）</option>
                            <option value="存量房交易税费申报表（转让方）">存量房交易税费申报表（转让方）</option>
                            <option value="完税发票">完税发票</option>
                            <option value="卖方材料">卖方材料</option>
                            <option value="买方材料">买方材料</option>
                            <option value="房管材料">房管材料</option>
                            <option value="缴费付款码">缴费付款码</option>
                            <option value="其他材料">其他材料</option>


                            <option value="税务证明事项告知承诺书（卖方承诺书）">税务证明事项告知承诺书（卖方承诺书）</option>
                            <option value="税务证明事项告知承诺书（买方承诺书）">税务证明事项告知承诺书（买方承诺书）</option>
                            <option value="存量商业用房评估信息采集表（办公用房）">存量商业用房评估信息采集表（办公用房）</option>
                            <option value="存量商业用房评估信息采集表（车位）">存量商业用房评估信息采集表（车位）</option>
                            <option value="存量商业用房评估信息采集表（公寓）">存量商业用房评估信息采集表（公寓）</option>
                            <option value="存量商业用房评估信息采集表（商服用房）">存量商业用房评估信息采集表（商服用房）</option>
                            <option value="重庆市存量住房信息采集表（个人转让存量住房适用）">重庆市存量住房信息采集表（个人转让存量住房适用）</option>
                        </select>
                    </div>
                    <label class="upload-button">上传附件<input class="uploadButton" type="file" id="fileInput"
                            multiple></label>
                </div>
                <div class="tableBox2 tableBox">
                    <table class="table table-hover">
                        <thead class="myThead myThead2">
                            <tr>
                                <th class="fixed-left-column"><input type="checkbox" id="tablefj"
                                        onclick="checkAllfj()" /></th>
                                <th>序号</th>
                                <th>附件名称</th>
                                <th>附件类型</th>
                                <th>附件来源</th>
                                <th>附件大小</th>
                                <th>纳税人是否签字</th>
                                <th>税务是否已确认</th>
                                <th>是否归档</th>
                                <th class="fixed-column">操作</th>
                            </tr>
                        </thead>
                        <tbody id="FJFileList"></tbody>
                    </table>
                </div>
                <div class="action-buttons">
                    <button class="confirm-btn gd">归档</button>
                    <button class="btn-default close-btn">返回</button>
                </div>
            </div>
        </div>
        <div id="swqzModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog"
            aria-labelledby="attachmentTit" aria-hidden="true">
            <div class="modal-dialog myModalBody" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <span class="modal-title" id="attachmentTit">税务确认</span>
                    </div>
                    <div class="modal-body">
                        <div class="uploadBox">
                            <div class="uploadTit">
                                <span class="uploadName" id="attachmentPushTit">请上传签章后的PDF文件</span>
                                <div class="uploadBtn">
                                    <button type="button" class="btn btn-primary mini">
                                        上传文件 <input class="myUpload" type="file" data-id="1" id="fileInput1"
                                            name="file" />
                                    </button>
                                    <div id="selectfiles" style="font-weight: 400;margin: 10px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
                        <button onclick="swqzTrue()" type="button" class="btn btn-primary myBtn">确认</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="./js/TranscendSign.js"></script>
    <script src="./js/layui/layui.js"></script>
    <!-- 测试用 -->
    <!-- <script src="./js/index.js"></script> -->
    <script>
        var url = location.origin + "/clf";//请求前缀
        var loginUser = null;
        var layer = null;
        layui.use('layer', function () {
            layer = layui.layer;
        });
        //分页
        var currentPage = 1; // 当前页
        function getMyMindMapPage(data, currentPage) {
            var html = "";
            var lastPage;
            var nextPage;
            var showPage = 4;      //每次显示的页数
            var index;
            var x;               //定义后面页数固定

            html += "<ul class='pagination'>";
            html += "<li><span onclick='search(1)'>首页</span></li>";

            lastPage = currentPage;
            if (lastPage <= 1) {
                lastPage == 1;
            } else {
                lastPage--;
            }

            html += "<li><span onclick='search(" + lastPage + ")'>上一页</span></li>";

            if (data <= showPage) {
                for (var i = 1; i <= data; i++) {
                    if (i == currentPage) {
                        html += "<li class='active'><span onclick='search(" + i + ")'>" + i + "</span></li>";
                    } else {
                        html += "<li><span onclick='search(" + i + ")'>" + i + "</span></li>";

                    }
                }
            } else {
                index = currentPage + showPage;
                x = currentPage;
                if (index > data) {
                    index = data + 1;
                    x = index - showPage;
                }

                for (var i = x; i < index; i++) {
                    if (i == currentPage) {
                        html += "<li class='active'><span onclick='search(" + i + ")'>" + i + "</span></li>";
                    } else {
                        html += "<li><span onclick='search(" + i + ")'>" + i + "</span></li>";

                    }
                }
            }
            nextPage = currentPage;
            if (nextPage < data) {
                nextPage++;
            } else if (nextPage == data) {
                nextPage = data;
            }

            html += "<li><span onclick='search(" + nextPage + ")'>下一页</span></li>";
            html += "<li><span onclick='search(" + data + ")'>尾页</span></li>";
            html += "</ul>";
            $("#pagination").html("");
            $("#pagination").append(html);

        }

        //初始进入页面
        $(function () {
            search(currentPage);
            getloginuser();
        })

        //查询
        function search(currentPage) {
            var index = layer.load(0);
            var acceptanceNumVal = $("#seachacceptanceNum").val();
            var realEstateUnitNumVal = $("#seachrealEstateUnitNum").val();
            var sellerNameVal = $("#seachsellerName").val();
            var buyerNameVal = $("#seachbuyerName").val();
            var sellerPhoneVal = $("#seachsellerPhone").val();
            var buyerPhoneVal = $("#seachbuyerPhone").val();

            $.ajax({
                type: "post",//请求类型 get或者post
                url: url + "/getList",//请求地址
                async: true,//异步或者同步，默认是异步
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                //timeout超时时间（毫秒）
                data: {
                    acceptanceNum: acceptanceNumVal,
                    realEstateUnitNum: realEstateUnitNumVal,
                    sellerName: sellerNameVal,
                    buyerName: buyerNameVal,
                    sellerPhone: sellerPhoneVal,
                    buyerPhone: buyerPhoneVal,
                    pageNo: currentPage,
                    pageSize: 10,
                },
                cache: false,//默认: true , 为false不读取缓存
                dataType: "json",
                success: function (data) {
                    layer.close(index);
                    $("#dataList").html('');
                    var p = Math.ceil(data.total / 10);
                    getMyMindMapPage(p, currentPage);
                    var rspData = data.data;
                    for (var i = 0; i < rspData.length; i++) {
                        $("#dataList").append('<tr>' +
                            //'<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].id + '" data-status="' + rspData[i].applyStatus + '" type="checkbox"></input></td>' +
                            '<td class="fixed-left-column">' + ((currentPage - 1) * 10 + i + 1) + '</td>' +
                            '<td>' + rspData[i].applyNum + '</td>' +
                            '<td>' + rspData[i].applyTime + '</td>' +
                            '<td>' + rspData[i].applyStatus + '</td>' +
                            '<td>' + rspData[i].acceptanceNum + '</td>' +
                            '<td>' + rspData[i].realEstateUnitNum + '</td>' +
                            '<td>' + rspData[i].realEstateName + '</td>' +
                            '<td>' + rspData[i].houseLocation + '</td>' +

                            '<td>' + rspData[i].houseType + '</td>' +
                            '<td>' + rspData[i].residentialType + '</td>' +
                            '<td>' + rspData[i].buildingArea + '</td>' +
                            '<td>' + rspData[i].totalPrice + '</td>' +
                            '<td>' + rspData[i].applicant + '</td>' +
                            '<td>' + rspData[i].idNum + '</td>' +
                            '<td>' + rspData[i].name + '</td>' +
                            '<td>' + rspData[i].idNum2 + '</td>' +
                            '<td>' + rspData[i].fgzxblr + '</td>' +
                            '<td>' + rspData[i].csrymc + '</td>' +
                            '<td>' + rspData[i].fsrymc + '</td>' +
                            '<td>' + rspData[i].updateTime + '</td>' +

                            '<td class="fixed-column">' +
                            //'<span class="fileManage" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">附件管理</span>' +
                            '<span class="getDetail" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">详情</span></td>' +
                            '</tr>');
                    };

                },
                error: function () {
                    layer.msg("请求失败");
                }
            })
        }
        //行选中
        var activeIds = [];
        var activeIdsfj = [];
        var fjgdzts = [];
        $(document).on('click', '.tableCheck', function () {
            if ($(this).is(':checked')) {
                $(this).parents('tr').addClass('selected');
                activeIds.push($(this).data('id'));
                if ($("#dataList tr").length == activeIds.length) {
                    document.getElementById("table1").checked = true;
                } else {
                    document.getElementById("table1").checked = false;
                }
            } else {
                $(this).parents('tr').removeClass('selected');
                for (var i = 0; i < activeIds.length; i++) {
                    if (activeIds[i] == $(this).data('id')) {
                        activeIds.splice(i, 1);
                    }
                }
                document.getElementById("table1").checked = false;

            }
        });
        var activeIdsfj = [];
        var fjgdzts = [];
        $(document).on('click', '.tableCheckfj', function () {
            if ($(this).is(':checked')) {
                $(this).parents('tr').addClass('selected');
                activeIdsfj.push($(this).data('id'));
                fjgdzts.push($(this).data('gdzt'));
                if ($("#FJFileList tr").length == activeIdsfj.length) {
                    document.getElementById("tablefj").checked = true;
                } else {
                    document.getElementById("tablefj").checked = false;
                }
            } else {
                $(this).parents('tr').removeClass('selected');
                for (var i = 0; i < activeIdsfj.length; i++) {
                    if (activeIdsfj[i] == $(this).data('id')) {
                        activeIdsfj.splice(i, 1);
                        fjgdzts.splice(i, 1);
                    }
                }
                document.getElementById("tablefj").checked = false;
            }
        });
        function checkAll() {
            var tableck = document.getElementById("table1").checked;
            var checkboxs = $(".tableCheck");
            activeIds = [];
            for (var i = 0; i < checkboxs.length; i++) {
                var checkbox = checkboxs[i];
                if (tableck) {
                    checkbox.checked = true;
                    activeIds.push($(checkbox).data('id'));
                } else {
                    checkbox.checked = false;
                    activeIds = [];
                }
            }
        }
        function checkAllfj() {
            var tableck = document.getElementById("tablefj").checked;
            var checkboxs = $(".tableCheckfj");
            activeIdsfj = [];
            fjgdzts = [];
            for (var i = 0; i < checkboxs.length; i++) {
                var checkbox = checkboxs[i];
                if (tableck) {
                    checkbox.checked = true;
                    activeIdsfj.push($(checkbox).data('id'));
                    fjgdzts.push($(checkbox).data('gdzt'));
                } else {
                    checkbox.checked = false;
                    activeIdsfj = [];
                    fjgdzts = [];
                }
            }
        }
        $(".table tbody").on("click", "tr", function (event) {
            var target = event.target;
            if ($(target).closest(".fixed-column").length > 0) {
                return;
            }
            if ($(target).closest(".fixed-left-column").length > 0) {
                return;
            }
            var checkbox = $(this).find(".tableCheck")[0];
            if (checkbox) {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    for (var i = 0; i < activeIds.length; i++) {
                        if (activeIds[i] == $(checkbox).data('id')) {
                            activeIds.splice(i, 1);
                        }
                    }
                    document.getElementById("table1").checked = false;
                } else {
                    checkbox.checked = true;
                    activeIds.push($(checkbox).data('id'));
                    if ($("#dataList tr").length == activeIds.length) {
                        document.getElementById("table1").checked = true;
                    } else {
                        document.getElementById("table1").checked = false;
                    }
                }
            }
            var checkbox2 = $(this).find(".tableCheckfj")[0];
            if (checkbox2) {
                if (checkbox2.checked) {
                    checkbox2.checked = false;
                    for (var i = 0; i < activeIdsfj.length; i++) {
                        if (activeIdsfj[i] == $(checkbox2).data('id')) {
                            activeIdsfj.splice(i, 1);
                            fjgdzts.splice(i, 1);
                        }
                    }
                    document.getElementById("tablefj").checked = false;
                } else {
                    checkbox2.checked = true;
                    activeIdsfj.push($(checkbox2).data('id'));
                    fjgdzts.push($(checkbox2).data('gdzt'));
                    if ($("#FJFileList tr").length == activeIdsfj.length) {
                        document.getElementById("tablefj").checked = true;
                    } else {
                        document.getElementById("tablefj").checked = false;
                    }
                }
            }
            event.stopPropagation();
        });

        var activeFjList = [];
        $('.uploadButton').change(function () {
            var fileInput = $('#fileInput')[0]; // 获取文件输入框
            var file = fileInput.files[0]; // 获取选中的第一个文件
            var attachmentName = $("#attachmentType").val();
            if (!file) {
                layer.msg("请先选择一个文件！");
                return;
            }
            const formData = new FormData();
            formData.append("file", file);
            formData.append("documentType", attachmentName);
            formData.append("ownership", '4');
            formData.append("reciid", activeId);
            formData.append("configId", 0);

            $.ajax({
                url: url + "/real/estate-file/upload",
                type: "POST",
                data: formData,
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                processData: false,  // 不处理数据
                contentType: false,  // 不设置 Content-Type
                success: function (response) {
                    if (response.code == 0) {
                        layer.msg("上传成功");
                        loadFjlist(activeId);
                        $('#fileInput').val('');
                    }
                },
                error: function () {
                    layer.msg("上传失败，请重试！");
                }
            });
        });

        function getloginuser() {
            $.ajax({
                type: "get",
                url: url + "/getLoginUser",
                async: true,
                headers: {
                    'Authorization': 'Bearer ' + 'test1',
                },
                cache: false,//默认: true , 为false不读取缓存
                dataType: "json",
                success: function (data) {
                    loginUser = data;
                }
            });
        }

        // function getDisplayText(values, options) {
        //     return values
        //         .map(value => {
        //             var matched = options.find(option => option.value === value);
        //             return matched ? matched.text : "未知选项";
        //         })
        //         .join("；");
        // }
        function getDisplayText(values, options) {
            if (typeof values === 'string') {
                values = values.split('');
            }
            return values
                .map(value => {
                    var matched = options.find(option => option.value === value.trim()); // 去除可能的空格
                    return matched ? matched.text : null;
                }).filter(text => text !== null).join("；");
        }
        //改造详情
        var activeId = "";
        var propertyData = {};
        var xqModal = document.getElementById("xqModal");
        $(document).on('click', '.getDetail', function () {
            $("#previewImgModal").css("display", "none");
            $(".gdBtn").css("display", "block");
            propertyData = {};
            $('#sellerBox').html("");
            $('#buyerBox').html("");
            $("#attachmentType").val('存量房交易信息采集表');
            $('#fileInput').val('');
            document.getElementById("tablefj").checked = false;
            activeIdsfj = [];
            fjgdzts = [];
            var index = layer.load(0);
            activeId = $(this).data('id');
            var id = $(this).data('id');  // 获取当前点击的元素的data-id
            $.ajax({
                type: "post",
                url: url + "/real/estate-collection-info/sellerGet",
                async: true,
                headers: {
                    'Authorization': 'Bearer ' + 'test1',
                },
                data: {
                    id: id,
                },
                cache: false,//默认: true , 为false不读取缓存
                dataType: "json",
                success: function (res) {
                    layer.close(index);
                    //大概的报文格式
                    if (res.code == 0) {
                        var data = res.data;
                        var applyStatus = data.applyStatus;
                        var updater = data.updater;
                        var xqbtnhtml = "";
                        if (applyStatus == "房管中心已受理") {
                            xqbtnhtml += "<button onclick=\"shoulli()\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">受理</button>";
                        }
                        if (applyStatus == "税务初审" && loginUser == updater) {
                            xqbtnhtml += "<button onclick=\"showOverModal('审核通过')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">审核通过</button>" +
                                "<button onclick=\"showBatchRejectModal('退回')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">退回</button>" +
                                "<button onclick=\"showRejectModal('中止')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">中止</button>";
                        }
                        if (applyStatus == "税务初审通过") {
                            xqbtnhtml += "<button onclick=\"showRejectModal('退回')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">退回</button>";
                        }


                        // if (applyStatus == "受理中" && loginUser == updater) {
                        //     xqbtnhtml += "<button onclick=\"showOverModal('审核通过')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">审核通过</button>" +
                        //         "<button onclick=\"showRejectModal('驳回')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">退回</button>" +
                        //         "<button onclick=\"showRejectModal('中止')\" type=\"button\" class=\"btn btn-primary myBtn\" data-dismiss=\"modal\">中止</button>";
                        // }
                        xqbtnhtml += "<button type=\"button\" class=\"btn btn-primary myBtn rightBtn\" onclick=\"closeXqModal()\" data-dismiss=\"modal\">返回</button>";
                        $("#xqbtn").html(xqbtnhtml);
                        if (data.houseInfo || data.businessInfo || data.officeInfo || data.parkingInfo || data.apartmentInfo) {
                            var houseData = data.houseInfo ? data.houseInfo : data.businessInfo ? data.businessInfo : data.officeInfo ? data.officeInfo : data.parkingInfo ? data.parkingInfo : data.apartmentInfo ? data.apartmentInfo : {};
                            propertyData = {
                                fgAcceptanceNum: data.rcmt.acceptanceNum,//受理编号
                                fgRealEstateUnitNum: data.rcmt.realEstateUnitNum,//受理编号
                                fgSellerName: data.rcmt.sellerName,//受理编号
                                fgSellerIdNum: data.rcmt.sellerIdNum,//受理编号
                                fgBuyerName: data.rcmt.buyerName,//受理编号
                                fgBuyerIdNum: data.rcmt.buyerIdNum,//受理编号
                                fgzxblr: data.rcmt.fgzxblr,
                                csrymc: data.rcmt.csrymc,
                                fsrymc: data.rcmt.fsrymc,
                                fgRemark: data.rcmt.remark,//受理编号

                                houseLocation: houseData.houseLocation,//产权地址
                                realEstateCertificateNum: houseData.realEstateCertificateNum,//产权证书
                                realEstateUnitNum: houseData.realEstateUnitNum,//不动产单元号
                                buildingArea: houseData.buildingArea,//建筑面积
                                housePurpose: houseData.housePurpose,//房屋用途
                                certificateType: houseData.certificateType,//执证类型
                                paymentMethod: houseData.paymentMethod,//付款方式
                                houseType: houseData.houseType,//房屋类型
                                realEstateName: houseData.realEstateName,//小区名称
                                residentialType: houseData.residentialType,//住宅类型
                                buildingImplement: houseData.buildingImplement,//建筑功能
                                suiteType: houseData.suiteType,//套型
                                houseRoom: (houseData.houseRoom ? houseData.houseRoom + "室" : "") + (houseData.houseHall ? houseData.houseHall + "厅" : "") + (houseData.houseKitchen ? houseData.houseKitchen + "厨" : "") + (houseData.houseBathroom ? houseData.houseBathroom + "卫" : ""),//户型
                                buildingStructure: houseData.buildingStructure,//建筑结构
                                floor: houseData.floor,//楼层
                                floorHeight: houseData.floorHeight,//层高
                                groundFloors: houseData.groundFloors,//地上总层数
                                undergroundFloors: houseData.undergroundFloors,//地下总层数
                                toward: houseData.toward,//朝向
                                pmLevel: houseData.pmLevel,//物管等级
                                buildingCompletionYear: houseData.buildingCompletionYear,//竣工年份
                                landUseRightType: houseData.landUseRightType,//土地使用权类型
                                landUseRightEndDate: houseData.landUseRightEndDate,//土地使用终止日期
                                remainingLandUseErm: houseData.remainingLandUseErm,//剩余土地使用权终止日期
                                supportingFacility: houseData.supportingFacility,//配套设施
                                houseShape: houseData.houseShape,//房屋形状
                                internalArea: houseData.internalArea,//套内面积
                                parkingSpaceType: houseData.parkingSpaceType,//车位类型
                                transactionDate: houseData.transactionDate,//交易日期
                                totalPrice: houseData.totalPrice,//申报交易总价
                                unitPrice: houseData.unitPrice,//申报交易单价
                                houseOtherDescription: houseData.houseOtherDescription,//房屋其他说明
                                remark: houseData.remark//备注
                            };
                        }
                        fillTable(propertyData);//渲染房屋交易表格

                        //渲染卖方表格
                        if (data.sellerInfo && data.sellerInfo.length > 0) {
                            data.sellerInfo.forEach((seller) => {
                                $('#sellerBox').append(
                                    `<tr>
                                <td>${seller.sfwtbl}</td>
                                <td>${seller.sfzxqszr}</td>
                                <td>${seller.obligeeType}</td>
                                <td>${seller.name}</td>
                                <td>${seller.idType}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.maritalStatus}</td>
                                <td>${seller.houseNumber}</td>
                                <td>${seller.phone}</td>
                                <td>${seller.shareSituation}</td>
                                <td>${seller.holdingRatio}</td>
                                <td>${seller.jybl}</td>
                                <td>${seller.sfxsgsjm}</td>
                                <td>${seller.jtcyxmgx}</td>
                                <td>${seller.jtcysfzh}</td>
                            </tr>`
                                )
                            })
                        }
                        var qsjmxOptions = [
                            {
                                "text": "个人购买家庭(成员范围包括购房人、配偶以及未成年子女，下同)唯一住房，面积为140平方米及以下",
                                "value": "1"
                            },
                            {
                                "text": "个人购买家庭唯一住房，面积为140平方米以上",
                                "value": "2"
                            },
                            {
                                "text": "个人购买家庭第二套改善性住房，面积为140平方米及以下",
                                "value": "3"
                            },
                            {
                                "text": "个人购买家庭第二套改善性住房，面积为140平方米以上",
                                "value": "4"
                            },
                            {
                                "text": "个人首次购买140平方米及以下改造安置住房",
                                "value": "5"
                            },
                            {
                                "text": "个人首次购买140平方米以上符合普通住房标准的改造安置住房",
                                "value": "6"
                            },
                        ];
                        //渲染买方表格
                        if (data.buyerInfo && data.buyerInfo.length > 0) {
                            data.buyerInfo.forEach((seller) => {
                                $('#buyerBox').append(
                                    `<tr>
                            <td>${seller.sfwtbl}</td>
                            <td>${seller.obligeeType}</td>
                            <td>${seller.name}</td>
                            <td>${seller.idType}</td>
                            <td>${seller.idNum}</td>
                            <td>${seller.maritalStatus}</td>
                            <td>${seller.houseNumber}</td>
                            <td>${seller.phone}</td>
                            <td>${seller.shareSituation}</td>
                            <td>${seller.holdingRatio}</td>
                            <td>${seller.sfxsqsjm}</td>
                            <td class="ellipsis" title="${seller.qsjmx ? getDisplayText(seller.qsjmx, qsjmxOptions) : ''}">${seller.qsjmx ? getDisplayText(seller.qsjmx, qsjmxOptions) : ''}</td>
                            <td>${seller.poxm}</td>
                            <td>${seller.posfzh}</td>
                            <td>${seller.wcnznxm1}</td>
                            <td>${seller.wcnznsfzh1}</td>
                            <td>${seller.wcnznxm2}</td>
                            <td>${seller.wcnznsfzh2}</td>
                            </tr>`
                                )
                            })
                        }
                        //附件列表
                        loadFjlist(activeId);
                        xqModal.style.right = "0";
                    }
                },
            })

        });
        // 关闭详情模态框
        function closeXqModal() {
            xqModal.style.right = "-100%";  // 向右滑出视野
        }
        //渲染详情-房屋交易信息表格  在上方详情接口返回值后  调用该方法传值
        function fillTable(data) {
            document.getElementById("fgAcceptanceNum").textContent = (data.fgAcceptanceNum);
            document.getElementById("fgRealEstateUnitNum").textContent = (data.fgRealEstateUnitNum);
            document.getElementById("fgSellerName").textContent = (data.fgSellerName);
            document.getElementById("fgSellerIdNum").textContent = (data.fgSellerIdNum);
            document.getElementById("fgBuyerName").textContent = (data.fgBuyerName);
            document.getElementById("fgBuyerIdNum").textContent = (data.fgBuyerIdNum);
            document.getElementById("fgzxblr").textContent = (data.fgzxblr);
            document.getElementById("csrymc").textContent = (data.csrymc);
            document.getElementById("fsrymc").textContent = (data.fsrymc);
            document.getElementById("fgRemark").textContent = (data.fgRemark);

            document.getElementById('houseLocation').textContent = data.houseLocation;
            document.getElementById('realEstateCertificateNum').textContent = data.realEstateCertificateNum;
            document.getElementById('realEstateUnitNum').textContent = data.realEstateUnitNum;
            document.getElementById('buildingArea').textContent = data.buildingArea;
            document.getElementById('housePurpose').textContent = data.housePurpose;
            document.getElementById('certificateType').textContent = data.certificateType;
            document.getElementById('paymentMethod').textContent = data.paymentMethod;
            document.getElementById('houseType').textContent = data.houseType;
            document.getElementById('realEstateName').textContent = data.realEstateName;
            document.getElementById('residentialType').textContent = data.residentialType;
            document.getElementById('buildingImplement').textContent = data.buildingImplement;
            document.getElementById('suiteType').textContent = data.suiteType;
            document.getElementById('houseRoom').textContent = data.houseRoom;
            document.getElementById('buildingStructure').textContent = data.buildingStructure;
            document.getElementById('floor').textContent = data.floor;
            document.getElementById('floorHeight').textContent = data.floorHeight;
            document.getElementById('groundFloors').textContent = data.groundFloors;
            document.getElementById('undergroundFloors').textContent = data.undergroundFloors;
            document.getElementById('toward').textContent = data.toward;
            document.getElementById('pmLevel').textContent = data.pmLevel;
            document.getElementById('buildingCompletionYear').textContent = data.buildingCompletionYear;
            document.getElementById('landUseRightType').textContent = data.landUseRightType;
            document.getElementById('landUseRightEndDate').textContent = data.landUseRightEndDate;
            document.getElementById('remainingLandUseErm').textContent = data.remainingLandUseErm;
            document.getElementById('supportingFacility').textContent = data.supportingFacility;
            document.getElementById('houseShape').textContent = data.houseShape;
            document.getElementById('internalArea').textContent = data.internalArea;
            document.getElementById('parkingSpaceType').textContent = data.parkingSpaceType;
            document.getElementById('transactionDate').textContent = data.transactionDate;
            document.getElementById('totalPrice').textContent = data.totalPrice;
            document.getElementById('unitPrice').textContent = data.unitPrice;
            document.getElementById('houseOtherDescription').textContent = data.houseOtherDescription;
            document.getElementById('remark').textContent = data.remark;
        }
        //退回 中止
        var rejectType = null;
        var isBatchReject = false;
        var checkboxSelected = null;
        var checkboxMkSelected = null;
        var checkboxCQSelected = null;
        function showRejectModal(type) {// 0 退回 1 中止
            checkboxSelected = null;
            checkboxMkSelected = null;
            checkboxCQSelected = null;
            isBatchReject = false;
            $("#rejectVal").val("");
            rejectType = type;
            if (type == "退回") {
                $("#rejectTit").text("退回原因");
                $("#rejectVal").attr("placeholder", "请输入退回原因");
            } else {
                $("#rejectTit").text("中止原因");
                $("#rejectVal").attr("placeholder", "请输入中止原因");
            }
            $('#rejectModal').modal('show');
        }
        function showBatchRejectModal() {
            isBatchReject = true;
            rejectType = "退回";
            $("#batchRejectVal").val("");
            $("#batchRejectTit").text("退回");
            $("#batchRejectVal").attr("placeholder", "请输入退回原因");
            $('#batchRejectModal').modal('show');
        }
        function getSelectedValues() {
            const checkboxes = document.querySelectorAll('.checkbox-item input:checked');
            const selected = Array.from(checkboxes).map(input => input.value);
            return selected;
        }
        $(document).on('change', '.checkbox-item input', function () {
            const selectedValues = getSelectedValues();
            if (selectedValues.includes("卖方")) {
                $('#kxgForm').show();
                $('#isBuyer').prop('disabled', true);
                $('#isBuyer').prop('checked', false);
            } else {
                $('#kxgForm').find('input[type="checkbox"]:checked').prop('checked', false);
                $('#kxgForm').hide();
            }
            if (selectedValues.includes("买方")) {
                $('#isSeller').prop('disabled', true);
                $('#isSeller').prop('checked', false);
            }
            if (selectedValues.includes("卖方") && selectedValues.includes("买方")) {
                $('#isBuyer').prop('disabled', false);
                $('#isSeller').prop('disabled', false);
            }
            if (selectedValues.includes("卖方") || selectedValues.includes("买方")) {
                $('#xcqForm').show();
            } else {
                $('#xcqForm').find('input[type="checkbox"]:checked').prop('checked', false);
                $('#xcqForm').hide();
            }
        });
        function getMKSelectedValues() {
            const checkboxes = document.querySelectorAll('kxg-item input:checked');
            const selected = Array.from(checkboxes).map(input => input.value);
            return selected;
        }
        function getCQSelectedValues() {
            const checkboxes = document.querySelectorAll('.xcq-item input:checked');
            const selected = Array.from(checkboxes).map(input => input.value);
            return selected;
        }
        function rejectTrue() {
            if (isBatchReject) {
                checkboxSelected = getSelectedValues();
                checkboxMkSelected = getMKSelectedValues();
                checkboxCQSelected = getCQSelectedValues();
                if (checkboxSelected.length === 0) {
                    alert("请至少选择一个退回对象");
                    return;
                }
                if ($("#batchRejectVal").val() == "" && rejectType == "退回") {
                    layer.msg("请输入退回原因");
                    return;
                }
                if (selectedValues.includes("卖方")) {
                    if (checkboxMkSelected.length === 0) {
                        alert("请至少选择一个需修改模块");
                        return;
                    }
                }

            } else {
                if ($("#rejectVal").val() == "" && rejectType == "退回") {
                    layer.msg("请输入退回原因");
                    return;
                }
                if ($("#rejectVal").val() == "" && rejectType == "中止") {
                    layer.msg("请输入中止原因");
                    return;
                }
            }
            var index = layer.load(0);
            $.ajax({
                url: url + "/real/estate-file/reject",  // 接口
                type: "POST",
                data: {
                    id: activeId,
                    rejectTarget: isBatchReject ? checkboxSelected.join(',') : "",
                    xxgmk: isBatchReject ? checkboxMkSelected.join(',') : "",
                    xcqwj: isBatchReject ? checkboxCQSelected.join(',') : "",
                    rejectReason: isBatchReject ? $("#batchRejectVal").val() : $("#rejectVal").val(),
                    type: rejectType // 0 退回 1 中止
                },
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                success: function (response) {
                    layer.close(index);
                    if (response.code == 0) {
                        layer.msg("操作成功");
                        $('#rejectModal').modal('hide');
                        location.reload();
                    } else {
                        layer.msg(response.msg);
                    }
                }
            })
        }

        // //审核通过
        function showOverModal() {
            $("#overMsg").text("是否确认审核通过？");
            $('#overModal').modal('show');
        }
        function overTrue() {
            var index = layer.load(0);
            $.ajax({
                url: url + "/real/estate-file/over",  // 接口
                type: "POST",
                data: {
                    id: activeId,
                    type: "审核通过"
                },
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                success: function (response) {
                    layer.close(index);
                    if (response.code == 0) {
                        layer.msg("操作成功");
                        $('#overModal').modal('hide');
                        location.reload();
                    }
                }
            })
        }
        function shoulli() {//受理
            if (!loginUser) {
                layer.msg("未获取到登陆信息，请重新登陆尝试！");
                return;
            }
            layer.confirm("是否要受理该事项？", {
                btn: ["确认", "取消"], btn1: function (index, layero) {
                    var index = layer.load(0);
                    $.ajax({
                        url: url + "/real/estate-file/shouli",  // 接口
                        type: "POST",
                        data: {
                            id: activeId,
                            type: "受理中"
                        },
                        headers: {
                            'Authorization': 'Bearer ' + 'test1',//token
                        },
                        success: function (response) {
                            layer.close(index);
                            if (response.code == 0) {
                                layer.msg("操作成功");
                                $('#overModal').modal('hide');
                                location.reload();
                            }
                        }
                    })
                }
            });
        }

        var qianzhistr = "存量房交易税费申报表（承受方）,存量房交易税费申报表（转让方）,存量房交易信息采集表,税务证明事项告知承诺书（卖方承诺书）,税务证明事项告知承诺书（买方承诺书）,存量商业用房评估信息采集表（办公用房）,存量商业用房评估信息采集表（车位）,存量商业用房评估信息采集表（公寓）,存量商业用房评估信息采集表（商服用房）,重庆市存量住房信息采集表（个人转让存量住房适用）";
        var qianzhangstr = "存量房交易税费申报表（承受方）,存量房交易税费申报表（转让方）,完税凭证,存量房交易信息采集表,税务证明事项告知承诺书（卖方承诺书）,税务证明事项告知承诺书（买方承诺书）,存量商业用房评估信息采集表（办公用房）,存量商业用房评估信息采集表（车位）,存量商业用房评估信息采集表（公寓）,存量商业用房评估信息采集表（商服用房）,重庆市存量住房信息采集表（个人转让存量住房适用）";
        //附件管理
        function loadFjlist(id) {
            $("#FJFileList").html("");
            $("#previewImg").attr("src", "");
            $("#previewPdf").attr("src", "");
            $.ajax({
                url: url + "/fileList",  // 接口
                type: "POST",
                data: {
                    reciid: activeId
                },
                headers: {
                    'Authorization': 'Bearer ' + 'test1'
                },
                success: function (response) {
                    // let responseData = JSON.parse(response);
                    // activeFjList = JSON.parse(responseData.data);
                    // var rspData = JSON.parse(responseData.data);
                    activeFjList = response.data;
                    var rspData = response.data;
                    // <td>${rspData[i].ownership == "1" ? "卖方" : rspData[i].ownership == "2" ? "买方" : rspData[i].ownership == "3" ? "不动产" : rspData[i].ownership == "4" ? "大厅税务" : rspData[i].ownership}</td>
                    //  <td>${rspData[i].nsrsfqz ? rspData[i].nsrsfqz : qianzhistr.indexOf(rspData[i].documentType) > -1 ? "否" : "-"}</td>
                    //             <td>${rspData[i].swsfyqr ? rspData[i].swsfyqr : qianzhangstr.indexOf(rspData[i].documentType) > -1 ? "否" : "-"}</td>
                    if (responseData.code == 0) {
                        if (rspData.length == 0) {
                            $("#FJFileList").html("<tr><td style='text-align: center;' colspan='10'>暂无数据</td></tr>");
                        } else {
                            for (var i = 0; i < rspData.length; i++) {
                                var html =
                                    `<tr><td class="fixed-left-column" style='width: 32px;'><input class="tableCheckfj" data-id="${rspData[i].id}" data-gdzt="${rspData[i].gdzt}" type="checkbox"></input></td>
                                <td>${i + 1}</td>
                                <td>${rspData[i].wjm}</td>
                                <td>${rspData[i].wjzllx}</td>
                                <td>${rspData[i].wjdx}</td>
                                <td>${rspData[i].gdzt ? rspData[i].gdzt : "否"}</td>
                                <td class="fixed-column" style='width: 220px;'>
                                <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-index="${i}">预览</span>
                                <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].fjbh}" data-ywbh="${rspData[i].ywbh}">下载</span>`;
                                // if (rspData[i].ownership == "4") {
                                //     html += `<span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].fjbh}" data-ywbh="${rspData[i].ywbh}">删除</span>`;
                                // }
                                // if (qianzhistr.indexOf(rspData[i].wjzllx) > -1 && (rspData[i].nsrsfqz == "否" || rspData[i].nsrsfqz == "")) {
                                //     html += `<span class="infoTS" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].fjbh}" data-ywbh="${rspData[i].ywbh}">推送</span>`;
                                // }
                                // if (qianzhangstr.indexOf(rspData[i].wjzllx) > -1 && (rspData[i].swsfyqr == "否" || rspData[i].swsfyqr == "")) {
                                //     html += `<span class="infoQR" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].fjbh}" data-ywbh="${rspData[i].ywbh}" data-documentType="${rspData[i].wjzllx}">确认</span>`;
                                // }
                                html += `</td></tr>`
                                $("#FJFileList").append(html);
                            }
                        }
                    } else {
                        layer.msg(responseData.msg);
                    }
                }
            });
        }

        //$(document).on('click', '.fileManage', function () {
        //   $("#attachmentType").val('存量房交易信息采集表');
        //    $('#fileInput').val('');
        //    document.getElementById("tablefj").checked = false;
        //    activeIdsfj = [];
        //    fjgdzts = [];
        //    $(".fjModal").show();
        //    activeId = $(this).data('id');
        //    loadFjlist(activeId);
        //});
        //$('.close-btn').on('click', function (e) {
        //    $(".fjModal").hide();
        //});

        $(document).on('click', '.gd', function () {
            if (activeIdsfj.length == 0) {
                layer.msg("请选择数据");
                return;
            }

            for (var i = 0; i < fjgdzts.length; i++) {
                if (fjgdzts[i] == "已归档") {
                    layer.msg("请选择未归档数据");
                    return;
                }
            }
            layer.confirm("是否要对选中" + activeIdsfj.length + "个文件进行归档？", {
                btn: ["确认", "取消"], btn1: function (index, layero) {
                    var index = layer.load(0);
                    $.ajax({
                        url: url + "/real/estate-file/gd",  // 接口
                        type: "POST",
                        data: {
                            id: activeId,
                            fjids: activeIdsfj.join()
                        },
                        headers: {
                            'Authorization': 'Bearer ' + 'test1',//token
                        },
                        success: function (response) {
                            layer.close(index);
                            if (response.code == 0) {
                                layer.msg("操作成功");
                                activeIdsfj = [];
                                fjgdzts = [];
                                loadFjlist(activeId);
                            } else {
                                layer.msg(response.msg)
                            }
                        }
                    });
                }
            });
        });

        //删除附件
        $(document).on('click', '.infoSC', function () {
            var id = $(this).data('id');
            layer.confirm("是否要删除该文件？", {
                btn: ["确认", "取消"], btn1: function (index, layero) {
                    var index = layer.load(0);
                    $.ajax({
                        url: url + "/real/estate-file/delete",  // 接口
                        type: "post",
                        data: {
                            id: id
                        },
                        headers: {
                            'Authorization': 'Bearer ' + 'test1',//token
                        },
                        success: function (response) {
                            layer.close(index);
                            if (response.code == 0) {
                                layer.msg("操作成功");
                                activeIdsfj = [];
                                fjgdzts = [];
                                loadFjlist(activeId);
                            } else {
                                layer.msg("请求失败：" + response.msg);
                            }
                        }
                    });
                }
            });
        });

        //签章配置
        //签章配置
        /*    var qzconfig = {
                "存量房交易信息采集表":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量房交易税费申报表（承受方）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量房交易税费申报表（转让方）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "税务证明事项告知承诺书（卖方承诺书）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量商业用房评估信息采集表（办公用房）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量商业用房评估信息采集表（车位）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量商业用房评估信息采集表（公寓）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "存量商业用房评估信息采集表（商服用房）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "重庆市存量住房信息采集表（个人转让存量住房适用）":{"type":"gjzqz","qzid":"cqgxjscykfq-ssywzyz","keyword":"那么准确"},
                "税务证明事项告知承诺书（买方承诺书）":{"type":"jdwzqz","qzid":"cqgxjscykfq-ssywzyz","page":"1","pdfx":"100","pdfy":"100"}
            };
            //税务确认
            $(document).on('click', '.infoQR', function () {
                layer.confirm("是否要对该文件进行确认？",{btn:["确认","取消"],btn1:function (index,layero) {
                        var index = layer.load(0);
                        var id = $(this).data('id');
                        var documentType =  $(this).data('documentType');
                        var qz = qzconfig[documentType];
                        if(!qz){
                            layer.msg("该文书未配置签章信息!");
                            return;
                        }
                        qz.id=id;
                        $.ajax({
                            url: url + "/real/estate-file/swqr",  // 接口
                            type: "post",
                            data: qz,
                            headers: {
                                'Authorization': 'Bearer ' + 'test1',//token
                            },
                            success: function (response) {
                                layer.close(index);
                                if (response.code == 0) {
                                    layer.msg("操作成功");
                                    loadFjlist(activeId);
                                }else{
                                    layer.msg("请求失败："+response.msg);
                                }
                            }
                        });
                    }});
            });*/
        $(document).on('click', '.infoQR', function () {
            $('#fileInput1').val('');
            fileId = $(this).data('id');
            $('#swqzModal').modal('show');
        });
        $(document).on('change', '#fileInput1', function () {
            $("#selectfiles").html("");
            for (var i = 0; i < this.files.length; i++) {
                const file = this.files[i];
                $("#selectfiles").append("当前选择附件：" + file.name + "<br/>附件大小：" + file.size + " kb");
            }
        });
        function swqzTrue() {
            var fileInput = $('#fileInput1')[0]; // 获取文件输入框
            var file = fileInput.files[0]; // 获取选中的第一个文件

            const formData = new FormData();
            formData.append("file", file);
            formData.append("id", fileId);
            $.ajax({
                url: url + "/real/estate-file/swqr",  // 接口
                type: "POST",
                data: formData,
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                processData: false,  // 不处理数据
                contentType: false,  // 不设置 Content-Type
                success: function (response) {
                    if (response.code == 0) {
                        layer.msg("操作成功");
                        $('#swqzModal').modal('hide');
                        loadFjlist(activeId);
                    } else {
                        layer.msg("请求失败：" + response.msg);
                    }
                },
                error: function () {
                    layer.msg("上传失败，请重试！");
                }
            });
        }

        //签字开始
        var positionObj = {
            "存量房交易税费申报表（承受方）": [
                { type: 1, x: 180, y: 200, w: 150, h: 100, keyword: '…', start: 'left', index: 7, flip: 1 },
                { type: 1, x: 180, y: 80, w: 150, h: 100, keyword: '…', start: 'left', index: 3, flip: 1 }
            ],
            "存量房交易税费申报表（转让方）": [//存量房交易税费申报表（转让方） 只能单字查找关键字 关键字：…
                { type: 1, x: 180, y: 200, w: 150, h: 100, keyword: '…', start: 'left', index: 7, flip: 1 },
                { type: 1, x: 180, y: 80, w: 150, h: 100, keyword: '…', start: 'left', index: 3, flip: 1 }
            ],
            "存量房交易信息采集表": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '转让方签字（盖章）确认', start: 'right', index: 0, flip: 1 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '受让方签字（盖章）确认', start: 'right', index: 0, flip: 1 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '税务人员签字审核', start: 'right', index: 0, flip: 1 }
            ],
            "税务证明事项告知承诺书（卖方承诺书）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '纳税人签字', start: 'right', index: 0, flip: 0 }
            ],
            "税务证明事项告知承诺书（买方承诺书）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '纳税人签字', start: 'right', index: 0, flip: 0 }
            ],
            "存量商业用房评估信息采集表（办公用房）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '转让方签字', start: 'right', index: 0, flip: 0 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '受让方签字', start: 'right', index: 0, flip: 0 },
                { type: 3, x: 0, y: -30, w: 120, h: 60, keyword: '签字按印', start: 'right', index: 0, flip: 0 }
            ],
            "存量商业用房评估信息采集表（车位）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '转让方签字', start: 'right', index: 0, flip: 0 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '受让方签字', start: 'right', index: 0, flip: 0 },
                { type: 3, x: 0, y: -30, w: 120, h: 60, keyword: '签字按印', start: 'right', index: 0, flip: 0 }
            ],
            "存量商业用房评估信息采集表（公寓）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '转让方签字', start: 'right', index: 0, flip: 0 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '受让方签字', start: 'right', index: 0, flip: 0 }
            ],
            "存量商业用房评估信息采集表（商服用房）": [
                { type: 1, x: 0, y: -50, w: 120, h: 60, keyword: '转让方签字', start: 'right', index: 0, flip: 0 },
                { type: 1, x: 0, y: -30, w: 120, h: 60, keyword: '受让方签字', start: 'right', index: 0, flip: 0 },
                { type: 3, x: 0, y: -30, w: 120, h: 60, keyword: '签字按印', start: 'right', index: 0, flip: 0 }
            ],
            "重庆市存量住房信息采集表（个人转让存量住房适用）": [
                { type: 1, x: 0, y: -40, w: 120, h: 60, keyword: '转让方签字', start: 'right', index: 0, flip: 0 },
                { type: 1, x: 0, y: -40, w: 120, h: 60, keyword: '承受方签字', start: 'right', index: 0, flip: 0 }
            ]
        }
        //签字结束
        var allIdx = null;
        function qzcallback(rjson) {
            layer.close(allIdx);
            if (rjson.code != 0) {
                layer.msg(rjson.info);
                return;
            }
            if (rjson.retData != null) {//处理base64位的图片存放到设备
                if (rjson.retData.pdfBase64 != null) {
                    var rest = convertBase64UrlToBlob(rjson.retData.pdfBase64);
                    const formData = new FormData();
                    formData.append("file", rest);
                    formData.append("id", fileId);
                    $.ajax({
                        url: url + "/real/estate-file/ts",  // 接口
                        type: "POST",
                        data: formData,
                        headers: {
                            'Authorization': 'Bearer ' + 'test1',//token
                        },
                        processData: false,  // 不处理数据
                        contentType: false,  // 不设置 Content-Type
                        success: function (response) {
                            if (response.code == 0) {
                                layer.msg("操作成功");
                                loadFjlist(activeId);
                            } else {
                                layer.msg("请求失败：" + response.msg);
                            }
                        },
                        error: function () {
                            layer.msg("上传失败，请重试！");
                        }
                    });
                }
            }
        }

        //推送
        var fileId = null;
        $(document).on('click', '.infoTS', function () {
            fileId = $(this).data('id');
            layer.confirm("是否需要推送该文件到纳税人签章？", {
                btn: ["确认", "取消"], btn1: function (index, layero) {
                    var index = layer.load(0);
                    $.ajax({
                        url: url + "/previewqk",  // 接口
                        type: "post",
                        data: {
                            id: fileId
                        },
                        headers: {
                            'Authorization': 'Bearer ' + 'test1',//token
                        },
                        success: function (response) {
                            layer.close(index);
                            if (response.code == 0) {
                                var base64String = response.data[0].content;
                                var documentType = response.data[0].documentType;
                                if (positionObj[documentType]) {
                                    allIdx = layer.load(0);
                                    TRANSCEND_SIGNDEVICE.startPdf(qzcallback, getSignConfig(1, base64String, JSON.stringify(positionObj[documentType]))); // 开始签名
                                } else {
                                    layer.msg("当前文书未配置签字信息！");
                                }
                            } else {
                                layer.msg("请求失败：" + response.msg);
                            }
                        }
                    });
                }
            });
        });
        var fjylIndex = 0;
        $(document).on('click', '.infoYL', function () {
            fjylIndex = $(this).data('index');
            showPreview();
        });
        function showPreview() {
            $(".gdBtn").css("display", "none");
            let type = activeFjList[fjylIndex].wjlx;
            var currurl = url + "/preview?id=" + activeFjList[fjylIndex].fjbh + "&ywbh=" + activeFjList[fjylIndex].ywbh;
            if (type == 'png' || type == 'jpg' || type == 'jpeg') {
                $("#previewPdf").attr("src", "");
                $("#previewPdf").css("display", "none")
                $("#previewImg").attr("src", currurl);
                $("#previewImg").css("display", "block");
                $("#previewImgModal").css("display", "block");
            } else if (type == 'pdf') {
                $("#previewImg").attr("src", "");
                $("#previewImg").css("display", "none");
                $("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
                $("#previewPdf").css("display", "block");
                $("#previewImgModal").css("display", "block");
            } else {
                layer.msg("文件[" + activeFjList[fjylIndex].name + "]暂不支持预览！");
            }
        }

        $(document).on('click', '#prevBtn', function () {
            if (fjylIndex == 0) {
                layer.msg("已经是第一个了！");
                return;
            }
            fjylIndex--;
            showPreview()
        })
        $(document).on('click', '#nextBtn', function () {
            if (fjylIndex == activeFjList.length - 1) {
                layer.msg("已经是最后一个了！");
                return;
            }
            fjylIndex++;
            showPreview()
        })

        $(document).on('click', '#preBtn', function () {
            $("#previewImg").attr("src", "");
            $("#previewPdf").attr("src", "");
            $("#previewImgModal").css("display", "none");
            $(".gdBtn").css("display", "block");
        });

        $(document).on('click', '#fullBtn', function () {
            showFullPreview()
        })

        $(document).on('click', '#fullPrevBtn', function () {
            if (fjylIndex == 0) {
                layer.msg("已经是第一个了！");
                return;
            }
            fjylIndex--;
            showFullPreview()
        })

        $(document).on('click', '#fullNextBtn', function () {
            if (fjylIndex == activeFjList.length - 1) {
                layer.msg("已经是最后一个了！");
                return;
            }
            fjylIndex++;
            showFullPreview()
        })

        function showFullPreview() {
            var currurl = url + "/preview?id=" + activeFjList[fjylIndex].fjbh + "&ywbh=" + activeFjList[fjylIndex].ywbh;
            var activeFjType = activeFjList[fjylIndex].wjlx;
            if (activeFjType == 'png' || activeFjType == 'jpg' || activeFjType == 'jpeg') {
                $("#fullPreviewPdf").attr("src", "");
                $("#fullPreviewPdf").css("display", "none")
                $("#fullFreviewImg").attr("src", currurl);
                $("#fullFreviewImg").css("display", "block");
                $('#fullModal').modal('show');
            } else if (activeFjType == 'pdf') {
                $("#fullFreviewImg").attr("src", "");
                $("#fullFreviewImg").css("display", "none");
                $("#fullPreviewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
                $("#fullPreviewPdf").css("display", "block");
                $('#fullModal').modal('show');
            } else {
                layer.msg("文件暂不支持预览！");
            }
        }

        $(document).on('click', '#fullClose', function () {
            $('#fullModal').modal('hide');
            showPreview();
        })


        $(document).on('click', '.infoXZ', function () {
            var id = $(this).data('id');
            var ywbh = $(this).data('ywbh');
            const aTag = document.createElement('a'); // 创建 a 标签
            aTag.href = url + "/download?id=" + id + "&ywbh=" + ywbh;  // 设置图片的 URL
            document.body.appendChild(aTag); // 将 a 标签添加到页面中
            aTag.click(); // 触发点击事件，开始下载
            document.body.removeChild(aTag); // 下载后移除 a 标签
        });
    </script>
</body>

</html>