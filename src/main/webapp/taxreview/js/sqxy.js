var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
    elem: '#ID-laydate-type-month',
    type: 'month'
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
	// 		$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ sqrq: "2025-04-01", slqd: "线下受理", nsrmc: "--", nsrsbh: "--", lxfs: "--", sqlx: "发票", sqjbr: "--", sqxyr: "--", bjqx: "2025-04-06", bjjg: "已受理", hffs: "线上", hfjg: "--", yjjy: "--" },
				{ sqrq: "2025-04-02", slqd: "线下受理", nsrmc: "--", nsrsbh: "--", lxfs: "--", sqlx: "发票", sqjbr: "--", sqxyr: "--", bjqx: "2025-04-08", bjjg: "已受理", hffs: "线上", hfjg: "--", yjjy: "--" },
				{ sqrq: "2025-04-03", slqd: "线下受理", nsrmc: "--", nsrsbh: "--", lxfs: "--", sqlx: "发票", sqjbr: "--", sqxyr: "--", bjqx: "2025-04-10", bjjg: "已受理", hffs: "线上", hfjg: "--", yjjy: "--" },
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].sqrq + '</td>' +
            '<td>' + rspData[i].slqd + '</td>' +
            '<td>' + rspData[i].nsrmc + '</td>' +
            '<td>' + rspData[i].nsrsbh + '</td>' +
            '<td>' + rspData[i].lxfs + '</td>' +
            '<td>' + rspData[i].sqlx + '</td>' +
            '<td>' + rspData[i].sqjbr + '</td>' +
            '<td>' + rspData[i].sqxyr + '</td>' +
			'<td>' + rspData[i].bjqx + '</td>' +
			'<td>' + rspData[i].bjjg + '</td>' +
			'<td>' + rspData[i].hffs + '</td>' +
			'<td>' + rspData[i].hfjg + '</td>' +
			'<td>' + rspData[i].yjjy + '</td>' +
            '</tr>');
    }

	var chartBox1 = echarts.init(document.getElementById('chartBox1'));

    // 统计诉求日期和办结日期的数量
    var sqrqStats = rspData.reduce((acc, item) => {
        acc[item.sqrq] = (acc[item.sqrq] || 0) + 1;
        return acc;
    }, {});

    var bjqxStats = rspData.reduce((acc, item) => {
        acc[item.bjqx] = (acc[item.bjqx] || 0) + 1;
        return acc;
    }, {});

    // 提取横轴和纵轴数据
    var xAxisData = Array.from(new Set([...Object.keys(sqrqStats), ...Object.keys(bjqxStats)])).sort(); // 合并并排序日期
    var sqrqData = xAxisData.map(date => sqrqStats[date] || 0); // 诉求统计数量
    var bjqxData = xAxisData.map(date => bjqxStats[date] || 0); // 办结统计数量

    // 配置双线图的选项
    var option = {
        title: {
            text: '诉求与响应统计趋势',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                let tooltip = `${params[0].axisValue}<br/>`;
                params.forEach(item => {
                    tooltip += `${item.seriesName}: ${item.data} 次<br/>`;
                });
                return tooltip;
            }
        },
        legend: {
            data: ['诉求统计数量', '响应统计数量'],
            bottom: '0'
        },
        xAxis: {
            type: 'category',
            data: xAxisData, // 横轴显示日期
            axisLabel: {
                rotate: 45, // 旋转标签，避免重叠
                interval: 0 // 强制显示所有标签
            }
        },
        yAxis: {
            type: 'value',
            name: '统计数量 (次)',
            axisLabel: {
                formatter: '{value} 次' // 格式化纵轴单位
            }
        },
        series: [
            {
                name: '诉求统计数量',
                type: 'line',
                data: sqrqData, // 数据为诉求统计数量
                itemStyle: {
                    color: '#73C0DE' // 设置折线颜色
                },
                lineStyle: {
                    width: 2 // 设置折线宽度
                },
                symbol: 'circle', // 数据点样式
                symbolSize: 8 // 数据点大小
            },
            {
                name: '响应统计数量',
                type: 'line',
                data: bjqxData, // 数据为响应统计数量
                itemStyle: {
                    color: '#FF7F50' // 设置折线颜色
                },
                lineStyle: {
                    width: 2 // 设置折线宽度
                },
                symbol: 'circle', // 数据点样式
                symbolSize: 8 // 数据点大小
            }
        ]
    };

    // 渲染双线图
    chartBox1.setOption(option);
}