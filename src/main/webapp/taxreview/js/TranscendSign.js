/**
 * V1.39
 * 与PC端服务的通讯接口JS封装
 *
 */
var TRANSCEND_SIGNDEVICE = new function () {
    var autoCancel = true;
    // var devSubID = 0;
    console.log("Android SignDevice Javascript SDK Version: 1.39");
    /**     version: 1.38     **/
    var cbs = {};
    var _CONNECT = function (data, callback) {
        var _wsImpl = window.WebSocket || window.MozWebSocket;
        if (_wsImpl == null) {
            callback({ code: 700, info: "你的浏览器不支持WebSocket" });
            return;
        }
        if (window.wst != null) {
            return;  //TODO 消息入栈
        }
        console.log("connecting...")
        window.wst = new _wsImpl('ws://127.0.0.1:7797/');
        wst.onmessage = function (evt) {
            var rdata = evt.data;
            console.log("收到数据，长度：" + rdata.length);
            rdata = eval("rdata=" + rdata);
            var tflag = rdata._tflag;
            delete rdata._tflag;
            cbs[tflag](rdata);
            if (rdata.retData == null || rdata.retData._more != 1)//后续没有数据了
                delete cbs[tflag];
        };
        wst.onopen = function () {
            console.log("connected!!")
            window.ws = wst;
            delete window.wst;
            if (data == null) return;
            _SEND(data, callback);
            data = null;
            callback = null;
        }
        wst.onclose = function () {
            console.log("closed!!");
            if (callback != null) callback({ code: 701, info: "PC端服务中间件未运行", retData: { act: data.act } });
            delete window.ws;
            delete window.wst;
            for (var i in cbs) {
                try {
                    cbs[i]({ code: 702, info: "PC端服务中间件已关闭" });
                } catch (e) { }
            }
            cbs = {};
        }
    };

    var _SEND = function (data, callback) {
        console.log("发送数据：" + new Date().getTime());
        if (window.ws == null) {
            _CONNECT(data, callback);
            return;
        }
        var tflag = new Date().getTime() % 100000000;
        if (data.method == null) {
            data.autoCancel = autoCancel;
            if (typeof (devSubID) != 'undefined' && data.targetDevSubID == null)
                data.targetDevSubID = devSubID;

            data = { method: "callSignDevice", msg: JSON.stringify(data) };
        }
        data._tflag = tflag;
        if (callback != null)
            cbs[tflag] = callback;
        try {
            // $("#msg").append("<span style='color:darkgreen;'>发送数据："+JSON.stringify(data)+"</span>").append("<br>");
            ws.send(JSON.stringify(data));
        } catch (e) {
            callback({ code: 700, info: "PC端服务中间件未运行" });
        }
    };

    //终端升级

    this.deviceUpdate = function (callback, url, md5) {
        /*
        参数：
            url     string
            md5 `   string
        函数：
            当传入url的时候，表示是终端在线升级
            不传入url，表示是使用本地文件文件进行升级
            md5值 只有当是网络升级时才有效，当没传入MD5时，会尝试从下载路径中提取MD5值 [0-9A-Fa-f]{32} ，如果没传入，也没有提取到MD5值，则会略过文件的MD5校验
         */
        _SEND({ method: 'deviceUpdate', url: url == null ? "" : url, md5: md5 == null ? "" : md5 }, callback);
    }
    //保存64位的文件
    this.saveBase64 = function (callback, path, base64) {
        /*
        参数：
            path    string
            base64  string
        函数：
            将base64参数进行base64解码，然后存放在PC上的path位置(绝对路径）
         */
        _SEND({ method: "saveBase64", path: path, base64: base64 }, callback);
    };
    //获取版本 - 可用于检查终端连接状态
    this.getVersion = function (callback) {
        _SEND({ act: 'version', setSysTime: new Date().getTime() }, callback);
    };
    //取消操作 - 取消所有的弹出界面
    this.cancelOperation = function (callback, closeCamera) {
        /*
        参数： closeCamera 可以不传
            closeCamera boolean   true - 同时关闭摄像头    false-不关闭摄像头【默认值】
         */
        _SEND({ act: 'cancelOperation', closeCamera: closeCamera }, callback);
    };

    //开始服务评价
    this.startEvaluate = function (callback, config) {
        /*
         参数：  config - 可以不传
            config = {
                act     :   string   'startEvaluate'    [默认值] - 普通评价
                note    :   int       [可选值]  1-{"不满意", "基本满意", "满意", "比较满意", "非常满意"}   默认是：{"非常不满意", "不满意", "基本满意", "满意", "非常满意"}
                timeout :   int     超时时间，单位：秒(可选 - 默认值0)
                level   :   int     0-5,  0表示默认不选中，   1-5 表示默认选中1-5颗星  默认值是5
            }

            config = {
                act         :   string  'startEvaluate37'      政务一体化好差评
                workName    :   string  业务名称
                userName    :   string  员工姓名
                userNO      :   string  员工工号
                userPosition:   string  员工岗位
                timeout     :   int     超时时间，单位：秒(可选 - 默认值0)
                dataFlag    :   int     使用的评价规则 (这个是定制入参)
                level   :   int     0-5,  0表示不选中，   1-5 表示默认选中1-5颗星   默认值是5
            }

            config = {
                act         :   string  'startEvaluate37'      出入境好差评
                page        :   int     固定值：2
                business    :   string  业务名称
                detail      :   string  评价规则(可选 - 有默认值), 字符串注意转义："[{ \"generalStars\": 5, \"tagList\": [{ \"tagId\": \"7176177890787235\", \"tagName\": \"可以先受理后补材料\" }, { \"tagId\": \"7176177890787335\", \"tagName\": \"一窗受理一次办结\" }, { \"tagId\": \"7176177890787435\", \"tagName\": \"不用提交证明\" }] }, { \"generalStars\": 4, \"tagList\": [{ \"tagId\": \"6176177900787340\", \"tagName\": \"填写一张表单就可以完成申报\" }, { \"tagId\": \"6176177900787341\", \"tagName\": \"在线提交材料窗口核验\" }, { \"tagId\": \"6176177900787342\", \"tagName\": \"一张清单告知全部申报材料\" }] }, { \"generalStars\": 3, \"tagList\": [{ \"tagId\": \"6176177900787344\", \"tagName\": \"一次性告知需要补正的材料\" }, { \"tagId\": \"6176177900787345\", \"tagName\": \"提供申报材料样本\" }, { \"tagId\": \"6176177900787346\", \"tagName\": \"在承诺的时间内办结\" }] }, { \"generalStars\": 2, \"tagList\": [{ \"tagId\": \"6176177900787347\", \"tagName\": \"没有提供材料样本\" }, { \"tagId\": \"6176177900787348\", \"tagName\": \"没有提供材料清单\" }, { \"tagId\": \"6176177900787349\", \"tagName\": \"未在承诺时间内办结\" }] }, { \"generalStars\": 1, \"tagList\": [{ \"tagId\": \"6176177900787350\", \"tagName\": \"在办事指南之外增加新的审批条件\" }, { \"tagId\": \"6176177900787351\", \"tagName\": \"需提供办事指南之外的申报材料\" }, { \"tagId\": \"7176177890787535\", \"tagName\": \"无理由超过法定办理时间\" }] }]"
                timeout     :   int     超时时间，单位：秒(可选 - 默认值0)
                level   :   int     0-5,  0表示不选中，   1-5 表示默认选中1-5颗星  默认值是5
            }
         */
        if (config == null) config = {};
        if (config.act == null) config.act = 'startEvaluate';
        // if(config.act=="startEvaluate37")
        //     config.detail =  "[{ \"generalStars\": 5, \"tagList\": [{ \"tagId\": \"7176177890787235\", \"tagName\": \"A可以先受理后补材料\" }, { \"tagId\": \"7176177890787335\", \"tagName\": \"A一窗受理一次办结\" }, { \"tagId\": \"7176177890787435\", \"tagName\": \"A不用提交证明\" }] }, { \"generalStars\": 4, \"tagList\": [{ \"tagId\": \"6176177900787340\", \"tagName\": \"A填写一张表单就可以完成申报\" }, { \"tagId\": \"6176177900787341\", \"tagName\": \"A在线提交材料窗口核验\" }, { \"tagId\": \"6176177900787342\", \"tagName\": \"A一张清单告知全部申报材料\" }] }, { \"generalStars\": 3, \"tagList\": [{ \"tagId\": \"6176177900787344\", \"tagName\": \"A一次性告知需要补正的材料\" }, { \"tagId\": \"6176177900787345\", \"tagName\": \"A提供申报材料样本\" }, { \"tagId\": \"6176177900787346\", \"tagName\": \"A在承诺的时间内办结\" }] }, { \"generalStars\": 2, \"tagList\": [{ \"tagId\": \"6176177900787347\", \"tagName\": \"A没有提供材料样本\" }, { \"tagId\": \"6176177900787348\", \"tagName\": \"A没有提供材料清单\" }, { \"tagId\": \"6176177900787349\", \"tagName\": \"A未在承诺时间内办结\" }] }, { \"generalStars\": 1, \"tagList\": [{ \"tagId\": \"6176177900787350\", \"tagName\": \"A在办事指南之外增加新的审批条件\" }, { \"tagId\": \"6176177900787351\", \"tagName\": \"A需提供办事指南之外的申报材料\" }, { \"tagId\": \"7176177890787535\", \"tagName\": \"A无理由超过法定办理时间\" }] }]"
        _SEND(config, callback);
    };
    //打开网页
    this.openWeb = function (callback, config) {
        /*
         参数：
                config = {
                    url         :   string  要打开的网页
                    timeout     :   int     超时时间，单位：秒(可选 - 默认值0   永不超时)
                    scale       :   int     网页缩放比例（可选 - 默认值100）
                    javaScript  :   string  网页加载完毕后，要运行的JS代码：(可选)
                                                比如： $('.ty_head').hide();$('.foot').hide();window.scrollTo(0, 100);
                                                    上面的js是隐藏页面上的头和尾，并且页面向上滚动100
                    closeBefore :   string  跳转前的关闭规则(可选)
                    closeDone   :   string  跳转后的关闭规则(可选)
                                                关闭规则目前支持：
                                                    all=                //所有跳转
                                                    start=value           //跳转的url.startsWith(value)
                                                    end=value             //跳转的url.endsWith(value)
                                                    mid=value             //跳转的url.contains(value)
                                            【目前只支持了倒计时超时关闭 和 网页跳转自动关闭】
                }
         */
        config.act = "openWeb";
        _SEND(config, callback);
    }

    /**
     *      config：    return {
     *                 pdf:pdfbase64  //PDF 文件的base64编码1190 1680
     *                 ,signAreas:[   //签捺区域  type:1-签名  2-捺印  3-签名捺印    pageIndex:页码从0开始   后面是每页的位置
     *                    {type:3, pageIndex:0, x:100, y:300, w:200, h:100, tips:'请张三李四签名'},
     *                   {type:3, pageIndex:0, x:250, y:450, w:200, h:100, tips:'请四签名'}
     *                 ]
     *             };
     * @param callback
     * @param config
     */
    //PDF 签捺
    this.startPdf = function (callback, config) {
        /*   ================  注意 目前支持了竖向的PDF(旋转方向是0度的)和横向的PDF(旋转方向是90度的） ===============================
        参数：
            config = {
                pdf     :   string      PDF文件的base64编码 (不换行的base64，有=补位)
                delay   :   object      [可选参数]  阅读时间配置
                            {
                                time    :   int     强制阅读时间，单位秒，  如 10
                                tip     :   string  强制阅读时的提示文字， 如 '请仔细阅读文件内容，签捺功能将在%d秒后开启'
                             }
                signAreas:  list object [可选参数]  当readonly!=1时，必传此参数
                            //签捺区域  type:1-签名  2-捺印  3-签名捺印    pageIndex:页码从0开始   后面是每页的签捺区域位置, 具备两种定位方式：
                            //支持两个定位方式同时使用
                            [
                            //注意： group  string   签名分组，同一个分组的签字位置只需要签一次  【注意：group 如果有值, 那么这些相同group的type参数需要一致】
                            //一个group可以同时包含绝对定位和相对定位的
                                //  一、绝对定位：(PDF软件认为PDF的大小是 595.32 x 841.92)
                                //        安卓设备认为标准PDF的每页大小是 1190x1689,坐标0x0在左上角
                                //        tips  签名的提示
                                {type:3, pageIndex:6, x:750, y:600, w:200, h:100, tips:'签名的提示', group:'a'}
                                ,{type:1, pageIndex:6, x:350, y:550, w:200, h:200}
                                //  二、相对定位：
                                //        安卓设备采用pdf标准, 及认为标准PDF大小是595x842
                                //        相对位置签名时，不需要pageIndex了，可以使用文字左下角或者右下角作为起点坐标0x0
                                //        start 参数如果不传，默认是right
                                //        目前只支持未旋转竖版 和 旋转90的横版 PDF
                                //        tips  签名的提示
                                //        index    int,     keyword的匹配下标   默认值是-0
                                //        group        string   签名分组，同一个分组的签字位置只需要签一次
                                //                     【注意：group 如果有值, 那么这些相同group的type参数需要一致】
                                ,{type:1, x:350, y:550, w:200, h:200, keyword:'关键字', start:'left|right', tips:'签名提示', index:0}
                            ]
                debug    :   int         [可选参数]  1-显示PDF页面转换后的高度和宽度    0-默认值
                readonly :   int         [可选参数]  1-只读PDF, 不进行PDF合成，合成由后台完成    0-默认值  当readonly=0时，必须传入参数signAreas
                readBtnText: int         [可选参数]  当readonly=1时有效， 自定义一个阅读按钮，显示在界面正下方，如'张三签字'
                imgs     :   string      [可选参数]  当readonly=1时有效，图片base64编码串,多张图片用英文;分隔， 如果传入此参数，则会忽略掉上面的pdf的参数
                                                    这里支持http路径，前提是设备需要有联网（如果加载URL图片出错，不会返回操作错误，所以不建议传入URL）
                signAnFinger:int          [可选参数]  当readonly=1时有效，   1-只签名  2-只要指纹  3-签名指纹（分开回传）  单人签捺
                signList ：  list object  [可选参数]  当readonly=1时有效
                              [
                                //readBtnText: 按钮上显示的文字
                                //signAnFinger:1-只签名   2-只要指纹   3-签名+指纹
                                //dialog: 0-全屏[默认值]   1-弹出式
                                //------在dialog=1的情况下，以下参数有效：
                                //      width      :  int       签名区域的宽度
                                //      height     :  int       签名区域的高度（弹出框的高度是这个高度+200, 传入1280*600弹出框就会全屏)
                                //      tips:      :  string    提示文字（上面的name  idno会被忽略掉）
                                //      textSize   :  int       提示文字的大小
                                {readBtnText:'张三签字', signAnFinger:1, dialog:1, width:600, height:500, tips:'弹出式签字提示'}
                                ,{readBtnText:'李四捺印', signAnFinger:2}
                                ,{readBtnText:'王五签字捺印', signAnFinger:3}
                              ]
                callPress:  int         [可选参数] 当有signList的时候, 1-表示点击用户按钮时回调到PC端[默认值]   0-不回调
                timeout  :  int         [可选参数] 超时时间单位：秒
                signPng  :  int         [可选参数] 0-不会传签名图片[默认值]   1-回传签名图片   此参数只有在合成PDF的情况下才有效

            }
         函数：
            针对单人签名，如果签名文件长度可能会变，   可通过word模板生成合同然后转PDF， 实现可变签名合成位置（技术路线经过实验的，暂时未开发，如确实有需要，可以提出）
            WORD转PDF的可选方案有：wps  spire  pageoffice
         */
        config.act = 'startPdf';
        _SEND(config, callback);
    };

    //显示图片  有客户用这个显示收款码
    this.showPicture = function (callback, picture) {
        /*
        参数：
            picture     :   string  图片的base64编码
                                    也可以是一个网络地址，网络地址需要https://或者http://打头, 如果加载图片失败，会返回错误信息（前提是终端设备要有网络）
            或
            picture = {
                picture:    string  图片的base64编码
                timeout:    int     超时时间, 单位秒
            }
         */
        var config = picture;
        if (typeof (picture) == 'string') {
            config = { picture: picture };
        }
        config.act = 'showPicture';
        _SEND(config, callback);
    }

    //显示二维码
    this.showQrCode = function (callback, config) {
        /*
        参数：
            config = {
                qrInfo1     :   string      二维码1
                qrName1     :   string      二维码描述1
                qrInfo2     :   string      [可选参数]二维码2
                qrName2     :   string      [可选参数]二维码描述2
                timeout     :   int         [可选参数]超时时间，单位秒
            }
         */
        config.act = "startQrCodeShow";
        _SEND(config, callback);
    }

    //设置自动取消
    this.setAutoCancel = function (val) {
        /*
            这个JS接口是专门给测试网页使用的，  系统对接时，其实直接修改本JS文件最上面的autoCancel的值就行了
         */
        if (val != null)
            autoCancel = val;
        return autoCancel;
    };


};

function getSignConfig(type, pdfbase64, signAreasArr) {
    // console.log(signAreasArr)
    if (type == 1) //PDF签捺
        return {
            pdf: pdfbase64  //PDF 文件的base64编码1190 1680
            , signAreas: JSON.parse(signAreasArr) //签捺区域  type:1-签名  2-捺印  3-签名捺印    pageIndex:页码从0开始   后面是每页的位置
            // [  
            //     {type:3, pageIndex:0, x:100, y:300, w:200, h:100, tips:'请张三李四签名'},
            //     {type:3, pageIndex:0, x:250, y:450, w:200, h:100, tips:'请四签名'}
            // ]
        };

    if (type == 9)//横向PDF 关键字签名
        return {
            pdf: pdfH  //PDF 文件的base64编码1190 1680
            , signAreas: [   //签捺区域  type:1-签名  2-捺印  3-签名捺印    pageIndex:页码从0开始   后面是每页的位置
                { type: 3, x: 0, y: -30, w: 200, h: 120, keyword: '单位经办人签字：', start: 'left' }
                , { type: 1, pageIndex: 0, x: 350, y: 550, w: 200, h: 200 }
            ]
        };
    if (type == 10)//竖向向PDF 关键字签名
        return {
            pdf: pdfV  //PDF 文件的base64编码1190 1680
            , signAreas: [   //签捺区域  type:1-签名  2-捺印  3-签名捺印    pageIndex:页码从0开始   后面是每页的位置
                { type: 3, x: 0, y: -30, w: 100, h: 60, keyword: '单位经办人签字：', start: '' }
                , { type: 1, pageIndex: 0, x: 350, y: 550, w: 200, h: 200 }
            ]
        };
}

$(function () {
    $("#autoCancel").prop("checked", TRANSCEND_SIGNDEVICE.setAutoCancel());
    $(".dtp").find("div").click(function (i) {
        $(".dtp").find("div").removeClass("dtpSelect");

        var ref = $(this).addClass("dtpSelect").attr("ref");
        $(".dpan").hide();
        $(".dpan[pname=" + ref + "]").show();
        resizeConf();
    }).eq(0).click();
    resizeConf();

})

function callback(rjson) {
    if (rjson.retData != null) {//处理base64位的图片存放到设备
        if (rjson.retData.pdfBase64 != null) {
            //TRANSCEND_SIGNDEVICE.saveBase64(function () {}, "D:/"+new Date().getTime()+".pdf", rjson.retData.pdfBase64);
            var rest = convertBase64UrlToBlob(rjson.retData.pdfBase64);
            //uploadCallBack(rest);//回调的文件流
            var url = window.URL.createObjectURL(rest);
            window.open(url)
            rjson.retData.pdfBase64 = rjson.retData.pdfBase64.substring(1, 30) + "... 这里是Base64编码的PDF文件";
        }

    }
    $("#msg").append("<span style='color:darkred;'>收到数据：" + JSON.stringify(rjson) + "</span>").append("<br>");
    $("#msg").scrollTop($("#msg").scrollTop() + $("#msg").height());

    if (rjson != null && rjson.retData != null) {
        if (rjson.retData.act == "getConf" && rjson.code == 0) {//获取参数成功了
            var f = $("form");
            f.find("input").each(function () {
                var i = $(this);
                var key = i.attr("name");
                i.val(rjson.retData[key]);
            });
            f.find("select").each(function () {
                var i = $(this);
                var key = i.attr("name");
                if (rjson.retData[key] != null)
                    i.val(rjson.retData[key]);
            });
            //$("#moreCamera").show();
            $("#dConf").show();
        } else if (rjson.retData.act == "saveConf" && rjson.code == 0) {
            hideConfDiv();
        }
    }
}
function hideConfDiv() {
    $("#moreCamera").hide();
    $("#dConf").hide();
}



function resizeConf() {
    $("#dConf").css("left", $(window).width() - 555);
    $("#msg").css("height", $(window).height() - $("#maxTopDiv").height() - 75);
    $("#msg").scrollTop($("#msg").scrollTop() + $("#msg").height());
}
function saveConf() {
    var fd = $("form").serializeArray();
    var d = {};
    for (var i = 0; i < fd.length; i++)
        d[fd[i].name] = fd[i].value;
    TRANSCEND_SIGNDEVICE.saveConf(d, callback);
}
function convertBase64UrlToBlob(base64) {
    var type = 'data:application/pdf;base64'.match(/:(.*?);/)[1];//提取base64头的type如 'image/png'
    var bytes = window.atob(base64);//去掉url的头，并转换为byte (atob:编码 btoa:解码)
    //处理异常,将ascii码小于0的转换为大于0
    var ab = new ArrayBuffer(bytes.length);//通用的、固定长度(bytes.length)的原始二进制数据缓冲区对象
    var ia = new Uint8Array(ab);
    for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
    }
    return new Blob([ab], { type: type });
}

