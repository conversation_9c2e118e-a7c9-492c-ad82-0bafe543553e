var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
    elem: '#ID-laydate-type-month',
    type: 'month'
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
	// 		$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ sryf: "2025-04", yjrkje: "100万元", sjrkje: "90万元", srwcqk: "20%", srwcpcl: "30%", pcyyfx: "--", xyzqzsd: "--", xyzqjsd: "--"},
				{ sryf: "2025-05", yjrkje: "300万元", sjrkje: "100万元", srwcqk: "20%", srwcpcl: "30%", pcyyfx: "--", xyzqzsd: "--", xyzqjsd: "--"},
				{ sryf: "2025-06", yjrkje: "80万元", sjrkje: "50万元", srwcqk: "20%", srwcpcl: "30%", pcyyfx: "--", xyzqzsd: "--", xyzqjsd: "--"},
				{ sryf: "2025-07", yjrkje: "50万元", sjrkje: "20万元", srwcqk: "20%", srwcpcl: "30%", pcyyfx: "--", xyzqzsd: "--", xyzqjsd: "--"},
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].sryf + '</td>' +
            '<td>' + rspData[i].yjrkje + '</td>' +
            '<td>' + rspData[i].sjrkje + '</td>' +
            '<td>' + rspData[i].srwcqk + '</td>' +
            '<td>' + rspData[i].srwcpcl + '</td>' +
            '<td>' + rspData[i].pcyyfx + '</td>' +
            '<td>' + rspData[i].xyzqzsd + '</td>' +
            '<td>' + rspData[i].xyzqjsd + '</td>' +
            '</tr>');
    }

	var chartBox1 = echarts.init(document.getElementById('chartBox1'));

    // 准备折线图数据
    var lineData = rspData.map(item => ({
        name: item.sryf, // 收入月份
        value: parseFloat(item.sjrkje.replace('万元', '')) // 实际入库金额（去掉单位并转为数字）
    }));

    // 提取横轴和纵轴数据
    var xAxisData = lineData.map(item => item.name); // 横轴：收入月份
    var seriesData = lineData.map(item => item.value); // 纵轴：实际入库金额

    // 配置折线图的选项
    var option = {
        title: {
            text: '实际入库金额趋势',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: '{b}<br/>{a}: {c} 万元'
        },
        xAxis: {
            type: 'category',
            data: xAxisData, // 横轴显示收入月份
            axisLabel: {
                rotate: 45, // 旋转标签，避免重叠
                interval: 0 // 强制显示所有标签
            }
        },
        yAxis: {
            type: 'value',
            name: '实际入库金额 (万元)',
            axisLabel: {
                formatter: '{value} 万元' // 格式化纵轴单位
            }
        },
        series: [
            {
                name: '实际入库金额',
                type: 'line',
                data: seriesData, // 数据为实际入库金额
                itemStyle: {
                    color: '#73C0DE' // 设置折线颜色
                },
                lineStyle: {
                    width: 2 // 设置折线宽度
                },
                symbol: 'circle', // 数据点样式
                symbolSize: 8 // 数据点大小
            }
        ]
    };

    // 渲染折线图
    chartBox1.setOption(option);
}