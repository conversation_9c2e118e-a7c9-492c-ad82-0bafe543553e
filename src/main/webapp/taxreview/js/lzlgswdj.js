var url = location.origin+"/clf";//请求前缀
// var url = "http://10.1.5.194:9527/sfzz-prod-api";//请求前缀
var loginUser = null;//登录用户
//金额格式化状态
var formatMoneyState = false;
var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
	elem: '#djrwwcrq',
	type: 'date',
});
var currentPage = 1; // 当前页

function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
//初始进入页面
$(function () {
	search(currentPage);
	// getloginuser();
})
//获取用户信息
function getloginuser() {
	$.ajax({
		type: "get",
		url: url + "/getLoginUser",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',
		},
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (data) {
			loginUser = data;
		}
	});
}
//查询
function search(currentPage) {
	var dwzcmc = $("#dwzcmc").val();
	var tyshxydm = $("#tyshxydm").val();
	var fddbr = $("#fddbr").val();
	var kzztdjlx = $("#kzztdjlx").val();
	var index = layer.load(0);
	$.ajax({
		type: "post",
		url: url + "/lzlg/lzlgswdj/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				dwzcmc: dwzcmc,
				tyshxydm: tyshxydm,
				fddbr: fddbr,
				kzztdjlx: kzztdjlx,
				pageNo: currentPage,
				pageSize: 10,
			}
		,
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (res) {
			layer.close(index);
			$("#dataList").html('');
			let rspData = res.data;
			if (rspData && rspData.length > 0) {
				$("#pagination").css("display", "block");
				getMyMindMapPage(res.total, currentPage, "#pagination", 10, search);
				loadList(rspData);
			} else {
				$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
				$("#pagination").css("display", "none");
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function loadList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#dataList").append('<tr>' +
			'<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].uuid + '"  data-type="' + rspData[i].djrwzt + '" type="checkbox"></input></td>' +
			'<td>' + (i + 1) + '</td>' +
			'<td>' + rspData[i].tjrq + '</td>' +
			'<td>' + rspData[i].dwzcmc + '</td>' +
			'<td>' + rspData[i].tyshxydm + '</td>' +
			'<td>' + rspData[i].nsrzt + '</td>' +
			'<td>' + rspData[i].zcd + '</td>' +
			'<td>' + rspData[i].jyfw + '</td>' +
			'<td>' + rspData[i].fddbr + '</td>' +
			'<td>' + rspData[i].lxfs + '</td>' +
			'<td>' + rspData[i].kzztdjlx + '</td>' +
			'<td>' + rspData[i].ygsl + '</td>' +
			'<td>' + rspData[i].yyqx + '</td>' +
			'<td>' + rspData[i].ccwp + '</td>' +
			'<td>' + rspData[i].zgswks + '</td>' +
			'<td>' + rspData[i].jdxz + '</td>' +
			'<td>' + rspData[i].ssgly + '</td>' +
			'<td>' + rspData[i].djrwxfrq + '</td>' +
			'<td>' + rspData[i].djrwwcrq + '</td>' +
			'<td>' + rspData[i].djrwzt + '</td>' +
			'<td>' + rspData[i].sfjxswdj + '</td>' +
			'<td>' + rspData[i].djjg + '</td>' +
			'<td>' + rspData[i].bz + '</td>' +
			'<td class="fixed-column">' + 
			(rspData[i].djrwzt=='已完成'?
			'<span style="cursor: no-allowed;margin-right: 10px;color: #ccc;font-size: 12px;" data-id="' + rspData[i].uuid + '">登记任务反馈</span>':'<span onclick="djrwfkFn(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">登记任务反馈</span>') +
			'</td></tr>');
	};
}
//导出
function dcFn() {
	var dwzcmc = $("#dwzcmc").val();
	var tyshxydm = $("#tyshxydm").val();
	var fddbr = $("#fddbr").val();
	var kzztdjlx = $("#kzztdjlx").val();
	var jsonData = JSON.stringify({
		dwzcmc: dwzcmc,
		tyshxydm: tyshxydm,
		fddbr: fddbr,
		kzztdjlx: kzztdjlx
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/lzlg/lzlgswdj/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//登记任务反馈
let activeIds = [];
let activeDatas = [];
//单个
function djrwfkFn(evt) {
	activeIds = [];
	activeDatas = [];
	$(".djrwfkModal").show();
	let element = evt.target;
	activeIds.push($(element).attr("data-id"));
}
//全选
function checkAll() {
	var tableck = document.getElementById("tableqs").checked;
	var checkboxs = $(".tableCheck");
	activeIds = [];
	activeDatas = [];
	for (var i = 0; i < checkboxs.length; i++) {
		var checkbox = checkboxs[i];
		if (tableck) {
			checkbox.checked = true;
			activeIds.push($(checkbox).data('id'));
			activeDatas.push($(checkbox).data('type'));
		} else {
			checkbox.checked = false;
			activeIds = [];
			activeDatas = [];
		}
	}
}
$(document).on('click', '#dataList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.tableCheck');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.tableCheck', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		activeIds.push($(this).data('id'));
		activeDatas.push($(this).data('type'));
		if ($("#dataList tr").length == activeIds.length) {
			document.getElementById("tableqs").checked = true;
		} else {
			document.getElementById("tableqs").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < activeIds.length; i++) {
			if (activeIds[i] == $(this).data('id')) {
				activeIds.splice(i, 1);
				activeDatas.splice(i, 1);
			}
		}
		document.getElementById("tableqs").checked = false;
	}
});
//批量
function djrwfkAllFn() {
	if (activeIds.length == 0) {
		layer.msg("请选择数据");
		return;
	}
	for (var i = 0; i < activeDatas.length; i++) {
		if (activeDatas[i] == "已完成") {
			layer.msg("请选择登记任务状态为处理中的数据");
			return;
		}
	}
	$(".djrwfkModal").show();
}
function djrwfk() {
	let rqsData = {
		bz: activeIds.join(','),
		djrwwcrq: $("#djrwwcrq").val(),
		djrwzt: $("#djrwzt").val(),
		djjg: $("#djjg").val(),
		djry: $("#djry").val(),
	}
	layer.confirm("确认要反馈吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/lzlg/lzlgswdj/djrwfk",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
					rqsData
				,
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						$(".djrwfkModal").hide();
						activeIds = [];
						search(currentPage);
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//关闭弹窗
function closeModal() {
	$(".djrwfkModal").hide();
	activeIds = [];
}