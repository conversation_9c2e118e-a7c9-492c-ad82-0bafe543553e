var url = "http://**********:9527/sfzz-prod-api";//请求前缀
//分页
var currentPage = 1; // 当前页
function getMyMindMapPage(data, currentPage) {
    var html = "";
    var lastPage;
    var nextPage;
    var showPage = 4;      //每次显示的页数
    var index;
    var x;               //定义后面页数固定

    html += "<ul class='pagination'>";
    html += "<li><span onclick='search(1)'>首页</span></li>";

    lastPage = currentPage;
    if (lastPage <= 1) {
        lastPage == 1;
    } else {
        lastPage--;
    }

    html += "<li><span onclick='search(" + lastPage + ")'>上一页</span></li>";

    if (data <= showPage) {
        for (var i = 1; i <= data; i++) {
            if (i == currentPage) {
                html += "<li class='active'><span onclick='search(" + i + ")'>" + i + "</span></li>";
            } else {
                html += "<li><span onclick='search(" + i + ")'>" + i + "</span></li>";

            }
        }
    } else {
        index = currentPage + showPage;
        x = currentPage;
        if (index > data) {
            index = data + 1;
            x = index - showPage;
        }

        for (var i = x; i < index; i++) {
            if (i == currentPage) {
                html += "<li class='active'><span onclick='search(" + i + ")'>" + i + "</span></li>";
            } else {
                html += "<li><span onclick='search(" + i + ")'>" + i + "</span></li>";

            }
        }
    }
    nextPage = currentPage;
    if (nextPage < data) {
        nextPage++;
    } else if (nextPage == data) {
        nextPage = data;
    }

    html += "<li><span onclick='search(" + nextPage + ")'>下一页</span></li>";
    html += "<li><span onclick='search(" + data + ")'>尾页</span></li>";
    html += "</ul>";
    $("#pagination").append(html);

}

//初始进入页面
$(function () {
    //search(currentPage)  //有接口后 删除下方代码
    let data = {
        total: 20,
        data: [{
            "id": 1,
            "applyNum": "申请编号",
            "applyTime": "申请时间",
            "applyStatus": "申请状态",
            "acceptanceNum": "房管受理编号",
            "realEstateUnitNum": "不动产单元号",
            "realEstateName": "楼盘（小区）名称",
            "houseLocation": "坐落地址",
        }, {
            "id": 2,
            "applyNum": "申请编号",
            "applyTime": "申请时间",
            "applyStatus": "申请状态2",
            "acceptanceNum": "房管受理编号",
            "realEstateUnitNum": "不动产单元号",
            "realEstateName": "楼盘（小区）名称",
            "houseLocation": "坐落地址",
        },]
    }
    getMyMindMapPage(data.total, currentPage)
    let rspData = data.data;
    console.log(rspData);
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].id + '" data-status="' + rspData[i].applyStatus + '" type="checkbox"></input></td>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].applyNum + '</td>' +
            '<td>' + rspData[i].applyTime + '</td>' +
            '<td>' + rspData[i].applyStatus + '</td>' +
            '<td>' + rspData[i].acceptanceNum + '</td>' +
            '<td>' + rspData[i].realEstateUnitNum + '</td>' +
            '<td>' + rspData[i].realEstateName + '</td>' +
            '<td>' + rspData[i].houseLocation + '</td>' +
            '<td class="fixed-column">'+
            //<span class="preview" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">版式文件预览</span>' +
            // '<span class="getMaterial" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">房管信息</span>' +
            '<span class="fileManage" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">附件管理</span>' +
            '<span class="getDetail" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">详情</span></td>' +
            '</tr>');
             //0214  操作列加类名
    };
})

//查询
function search(currentPage) {
    var acceptanceNumVal = $("#seachacceptanceNum").val();
    var realEstateUnitNumVal = $("#seachrealEstateUnitNum").val();
    var sellerNameVal = $("#seachsellerName").val();
    var buyerNameVal = $("#seachbuyerName").val();
    var sellerPhoneVal = $("#seachsellerPhone").val();
    var buyerPhoneVal = $("#seachbuyerPhone").val();

    $.ajax({
        type: "post",//请求类型 get或者post
        url: url + "/getList",//请求地址
        async: true,//异步或者同步，默认是异步
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        //timeout超时时间（毫秒）
        data: {
            acceptanceNum: acceptanceNumVal,
            realEstateUnitNum: realEstateUnitNumVal,
            sellerName: sellerNameVal,
            buyerName: buyerNameVal,
            sellerPhone: sellerPhoneVal,
            buyerPhone: buyerPhoneVal,
            pageNo: currentPage,
            pageSize: 10,
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (data) {
            $("#dataList").html('');
            getMyMindMapPage(data.total, currentPage)
            let rspData = data.data;
            console.log(rspData);
            for (var i = 0; i < rspData.length; i++) {
                $("#dataList").append('<tr>' +
                    '<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].id + '" data-status="' + rspData[i].applyStatus + '" type="checkbox"></input></td>' +
                    '<td>' + (i + 1) + '</td>' +
                    '<td>' + rspData[i].applyNum + '</td>' +
                    '<td>' + rspData[i].applyTime + '</td>' +
                    '<td>' + rspData[i].applyStatus + '</td>' +
                    '<td>' + rspData[i].acceptanceNum + '</td>' +
                    '<td>' + rspData[i].realEstateUnitNum + '</td>' +
                    '<td>' + rspData[i].realEstateName + '</td>' +
                    '<td>' + rspData[i].houseLocation + '</td>' +
                    '<td class="fixed-column">' +
                    // <span class="preview" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">版式文件预览</span>' +
                    // '<span class="getMaterial" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">房管信息</span>' +
                    '<span class="fileManage" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">附件管理</span>' +
                    '<span class="getDetail" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">详情</span></td>' +
                    '</tr>');
                    //0214  操作列加类名  注释版式文件预览和房管信息
            };

        },//请求成功的回调函数，请求回的数据是这个函数的第一个参数
        error: function () {
            alert("请求失败");
        },//请求失败的回调函数

    })
}
//行选中
var activeIds = [];
var activeStatus = [];
$(document).on('click', '.tableCheck', function () {
    if ($(this).is(':checked')) {
        $(this).parents('tr').addClass('selected');
        activeIds.push($(this).data('id'));
        activeStatus.push($(this).data('status'));
    } else {
        $(this).parents('tr').removeClass('selected');
        for (var i = 0; i < activeIds.length; i++) {
            if (activeIds[i] == $(this).data('id')) {
                activeIds.splice(i, 1);
            }
        }
        for (var i = 0; i < activeStatus.length; i++) {
            if (activeStatus[i] == $(this).data('item')) {
                activeStatus.splice(i, 1);
            }
        }
    }
});

//版式文件预览
$(document).on('click', '.preview', function () {
    var id = $(this).data('id');  // 获取当前点击的元素的data-id
    console.log(id)
    previewFile(id);  //查询预览信息
});
function previewFile(id) {
    // AJAX请求获取预览内容
    $.ajax({
        url: '/getPreview/' + id,  // 预览接口的路径
        type: 'GET',
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            $('#bswjPreviewPdf').attr("src", response.data.fileUrl);//需要个返回流的接口+response.data.fileUrl  预览
            $('#previewModal').modal('show');
        },
        error: function (error) {
            alert('预览失败');
        }
    });
}

//房管信息
var clModal = document.getElementById("clModal");
var closeClModal = document.getElementById("closeClModal");
$(document).on('click', '.getMaterial', function () {
    $(".uploadList").html('');
    $('.form-inline')[0].reset();
    var id = $(this).data('id');  // 获取当前点击的元素的data-id
    $.ajax({
        type: "get",
        url: url + "/real/estate-rcmt/get?id=1",//id
        async: true,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        data: {
            id: id,
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (res) {
            //大概的报文格式
            // {
            //     "id": 6,
            //     "reciid": -6,
            //     "hallWindowNum": "",
            //     "acceptanceNum": "1",
            //     "realEstateUnitNum": "2",
            //     "sellerName": "3",
            //     "sellerIdNum": "3",
            //     "buyerName": "3",
            //     "buyerIdNum": "3",
            //     "pushStatus": "未推送",
            //     "remark": "",
            //     "createTime": 1736681395000,
            //     "fileList": [
            //         {
            //             "id": 1570,
            //             "configId": 0,
            //             "reciid": -6,
            //             "documentType": "业务单",
            //             "ownership": "3",
            //             "name": "666.jpg",
            //             "path": "39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "url": "http://**********:48080/admin-api/infra/file/24/get/39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "type": "image/jpeg",
            //             "size": 13478,
            //             "createTime": 1736681385000
            //         },
            //         {
            //             "id": 1571,
            //             "configId": 0,
            //             "reciid": -6,
            //             "documentType": "简易合同",
            //             "ownership": "3",
            //             "name": "666.jpg",
            //             "path": "39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "url": "http://**********:48080/admin-api/infra/file/24/get/39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "type": "image/jpeg",
            //             "size": 13478,
            //             "createTime": 1736681388000
            //         },
            //         {
            //             "id": 1572,
            //             "configId": 0,
            //             "reciid": -6,
            //             "documentType": "身护房",
            //             "ownership": "3",
            //             "name": "666.jpg",
            //             "path": "39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "url": "http://**********:48080/admin-api/infra/file/24/get/39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
            //             "type": "image/jpeg",
            //             "size": 13478,
            //             "createTime": 1736681392000
            //         }
            //     ]
            // }
            if (res.code == 0) {
                var data = res.data;
                $("#acceptanceNum").val(data.acceptanceNum);
                $("#realEstateUnitNum").val(data.realEstateUnitNum);
                $("#sellerName").val(data.sellerName);
                $("#sellerIdNum").val(data.sellerIdNum);
                $("#buyerName").val(data.buyerName);
                $("#buyerIdNum").val(data.buyerIdNum);
                $("#remark").val(data.remark);
                if (res.data.fileList && res.data.fileList.length > 0) {
                    res.data.fileList.forEach((file) => {
                        const modifiedDocumentType = file.documentType;
                        const currId = modifiedDocumentType == '业务单' ? '1' : modifiedDocumentType == '简易合同' ? '2' : modifiedDocumentType == '身护房' ? '3' : modifiedDocumentType == '材料其他' ? '4' : '';
                        let uploadFrom = "#lstInfo" + currId;
                        let fileType = file.type.split('/')[0];
                        $(uploadFrom).append(
                            '<div class="lstInfo col-sm-6 col-md-6 col-lg-6">' +
                            '<div class="lstInfoBox">' +
                            '<i class="glyphicon glyphicon-duplicate"></i>' +
                            '<span class="fileInfoName">' + file.name + '</span>' +
                            '<div class="btnList">' +
                            '<i onclick="handlePreview(\'' + file.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
                            '<i onclick="handleDownload(\'' + file.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
                            // '<i onclick="handleDelete("ywdList", fIndex)" class="glyphicon glyphicon-trash"></i>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        )
                    })

                }
                clModal.style.right = "0";
            }
        },
    })
});
// 关闭材料模态框
// closeClModal.onclick = function () {
//     clModal.style.right = "-100%";  // 向右滑出视野
// };

// //详情
// var xqModal = document.getElementById("xqModal");
// var closeXqModal = document.getElementById("closeXqModal");
// $(document).on('click', '.getDetail', function () {
//     $('.form-inline')[0].reset();
//     $("#sellerBox").html('');
//     $("#houseBox").html('');
//     $(".uploadList").html('');
//     var id = $(this).data('id');  // 获取当前点击的元素的data-id

//     $.ajax({
//         type: "get",
//         url: url + "/real/estate-collection-info/sellerGet?id=54",//id
//         async: true,
//         headers: {
//             'Authorization': 'Bearer ' + 'test1',//token
//         },
//         data: {
//             id: id,
//         },
//         cache: false,//默认: true , 为false不读取缓存
//         dataType: "json",
//         success: function (res) {
//             //大概的报文格式
//             if (res.code == 0) {
//                 var data = res.data;
//                 // 大概的报文格式
//                 // {
//                 //     "id": 17,
//                 //     "applyNum": "20250112184402853",
//                 //     "applicant": "",
//                 //     "applyTime": 1736678643000,
//                 //     "idType": "身份证（香港）",
//                 //     "idNum": "1111",
//                 //     "phone": "1",
//                 //     "realEstateUnitNum": "1",
//                 //     "applyStatus": "未提交",
//                 //     "applyRemark": null,
//                 //     "sellerStatus": "已提交",
//                 //     "buyerStatus": "0",
//                 //     "shareSituation": "单独所有",
//                 //     "houseType": "商服用房",
//                 //     "remark": "",
//                 //     "createTime": 1736678643000,
//                 //     "sellerInfo": [
//                 //         {
//                 //             "id": 15,
//                 //             "reciid": 17,
//                 //             "obligeeType": "个人",
//                 //             "name": "hcy",
//                 //             "idType": "身份证（香港）",
//                 //             "idNum": "1111",
//                 //             "phone": "1",
//                 //             "maritalStatus": "已婚",
//                 //             "houseNumber": "1",
//                 //             "holdingRatio": "",
//                 //             "sort": 0,
//                 //             "createTime": 1736678643000
//                 //         }
//                 //     ],
//                 //     "buyerInfo": null,
//                 //     "houseInfo": null,
//                 //     "businessInfo": {
//                 //         "id": 2,
//                 //         "reciid": 17,
//                 //         "houseType": "商服用房",
//                 //         "evaluatePartition": "1",
//                 //         "houseLocation": "1",
//                 //         "pmName": "1",
//                 //         "realEstateCertificateNum": "1",
//                 //         "realEstateUnitNum": "1",
//                 //         "propertyRightOwner": "1",
//                 //         "idType": "",
//                 //         "idNum": "",
//                 //         "phone": "",
//                 //         "buildingArea": "1",
//                 //         "floor": "1",
//                 //         "buildingStructure": "钢混、框架",
//                 //         "landUseRightType": "出让",
//                 //         "landUseRightEndDate": "2028-12-31T16:00:00.000Z",
//                 //         "remainingLandUseErm": "1",
//                 //         "houseShape": "规则多边形（矩形、梯形）",
//                 //         "transactionDate": "YYYY-01-02",
//                 //         "declareTransactionTotalPrice": "1",
//                 //         "declareTransactionUnitPrice": "1",
//                 //         "houseOtherDescription": "1",
//                 //         "createTime": 1736678643000
//                 //     },
//                 //     "officeInfo": null,
//                 //     "apartmentInfo": null,
//                 //     "parkingInfo": null,
//                 //     "fileList": [
//                 //         {
//                 //             "id": 1555,
//                 //             "configId": 0,
//                 //             "reciid": 17,
//                 //             "documentType": "身份证0",
//                 //             "ownership": "1",
//                 //             "name": "666.jpg",
//                 //             "path": "39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
//                 //             "url": "http://**********:48080/admin-api/infra/file/24/get/39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
//                 //             "type": "image/jpeg",
//                 //             "size": 13478,
//                 //             "createTime": 1736678315000
//                 //         },
//                 //         {
//                 //             "id": 1556,
//                 //             "configId": 0,
//                 //             "reciid": 17,
//                 //             "documentType": "房产证0",
//                 //             "ownership": "1",
//                 //             "name": "666.jpg",
//                 //             "path": "39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
//                 //             "url": "http://**********:48080/admin-api/infra/file/24/get/39d7187876dba674289f05008ef8df114440d4f23153f9ef642a8b8710ad3011.jpg",
//                 //             "type": "image/jpeg",
//                 //             "size": 13478,
//                 //             "createTime": 1736678627000
//                 //         }
//                 //     ]
//                 // }
//                 //主信息
//                 $("#houserealEstateUnitNum").val(data.realEstateUnitNum);
//                 $("#houseremark").val(data.remark);

//                 //买方人员信息
//                 if (data.buyerInfo && data.buyerInfo.length > 0) {
//                     data.buyerInfo.forEach((seller) => {
//                         $('#buyerBox').append(
//                             '<div class="sellerBox">' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="obligeeType">权利人类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.obligeeType + '" id="obligeeType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="name">姓名</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.name + '" id="name" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="idType">证件类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.idType + '" id="idType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="idNum">证件号码</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.idNum + '" id="idNum" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="maritalStatus">婚姻状况</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.maritalStatus + '" id="maritalStatus" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="houseNumber">已持有房屋数量（套）</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.houseNumber + '" id="houseNumber" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="phone">联系电话</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.phone + '" id="phone" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="shareSituation">共有情况</label>' +
//                             '<input type="text" disabled class="form-control" value="' + data.shareSituation + '" id="shareSituation" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             (data.shareSituation == '按份共有' ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label for="holdingRatio">持有比例</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + seller.holdingRatio + '" id="holdingRatio" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             '</div>'
//                         )
//                     })
//                 }

//                 //卖方人员信息
//                 if (data.sellerInfo && data.sellerInfo.length > 0) {
//                     data.sellerInfo.forEach((seller) => {
//                         $('#sellerBox').append(
//                             '<div class="sellerBox">' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="obligeeType">权利人类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.obligeeType + '" id="obligeeType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="name">姓名</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.name + '" id="name" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="idType">证件类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.idType + '" id="idType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="idNum">证件号码</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.idNum + '" id="idNum" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="maritalStatus">婚姻状况</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.maritalStatus + '" id="maritalStatus" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="houseNumber">已持有房屋数量（套）</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.houseNumber + '" id="houseNumber" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="phone">联系电话</label>' +
//                             '<input type="text" disabled class="form-control" value="' + seller.phone + '" id="phone" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="shareSituation">共有情况</label>' +
//                             '<input type="text" disabled class="form-control" value="' + data.shareSituation + '" id="shareSituation" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             (data.shareSituation == '按份共有' ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label for="holdingRatio">持有比例</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + seller.holdingRatio + '" id="holdingRatio" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             '</div>'
//                         )
//                     })
//                 }

//                 //房屋信息
//                 if (data.houseInfo || data.businessInfo || data.officeInfo || data.parkingInfo || data.apartmentInfo) {
//                     let houseData = data.houseInfo ? data.houseInfo : data.businessInfo ? data.businessInfo : data.officeInfo ? data.officeInfo : data.parkingInfo ? data.parkingInfo : data.apartmentInfo ? data.apartmentInfo : {};
//                     $("#houseLocation").val(houseData.houseLocation);
//                     $("#realEstateCertificateNum").val(houseData.realEstateCertificateNum);
//                     $("#buildingArea").val(houseData.buildingArea);
//                     $("#housePurpose").val(houseData.housePurpose);
//                     $("#houseBox").append(
//                         '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                         '<div class="form-group">' +
//                         '<label for="certificateType">执证类型</label>' +
//                         '<input type="text" disabled class="form-control" value="' + houseData.certificateType + '" id="certificateType" placeholder="请输入">' +
//                         '</div>' +
//                         '</div>' +
//                         '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                         '<div class="form-group">' +
//                         '<label for="paymentMethod">付款方式</label>' +
//                         '<input type="text" disabled class="form-control" value="' + houseData.paymentMethod + '" id="paymentMethod" placeholder="请输入">' +
//                         '</div>' +
//                         '</div>' +
//                         '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                         '<div class="form-group">' +
//                         '<label for="houseType">房屋类型</label>' +
//                         '<input type="text" disabled class="form-control" value="' + data.houseType + '" id="houseType" placeholder="请输入">' +
//                         '</div>' +
//                         '</div>' +
//                         (data.houseType == '个人住宅' ?
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="realEstateName">楼盘（小区）名称</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.realEstateName + '" id="realEstateName" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="suiteType">套型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.suiteType + '" id="suiteType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="residentialType">住宅类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.residentialType + '" id="residentialType" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="groundFloors">房屋建筑地上总层数</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.groundFloors + '" id="groundFloors" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="undergroundFloors">房屋建筑地下总层数</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.undergroundFloors + '" id="undergroundFloors" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="pmLevel">物管等级</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.pmLevel + '" id="pmLevel" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="toward">房屋朝向（可多选）</label>' +
//                             '<input type="text" disabled class="form-control" value="' + JSON.parse(houseData.toward) + '" id="toward" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="supportingFacility">配套设施（可多选）</label>' +
//                             '<input type="text" disabled class="form-control" value="' + JSON.parse(houseData.supportingFacility) + '" id="supportingFacility" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="buildingCompletionYear">房屋建成(竣工)年份</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.buildingCompletionYear + '" id="buildingCompletionYear" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="row hxBox">' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="houseRoom">户型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseRoom + '室' + '" id="houseRoom" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-2 col-md-2 col-lg-2">' +
//                             '<div class="form-group">' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseHall + '厅' + '" id="houseHall" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-2 col-md-2 col-lg-2">' +
//                             '<div class="form-group">' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseBathroom + '卫' + '" id="houseBathroom" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-2 col-md-2 col-lg-2">' +
//                             '<div class="form-group">' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseKitchen + '厨' + '" id="houseKitchen" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>申报交易总价</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.declareTransactionTotalPrice + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' : '') +
//                         (data.houseType != '个人住宅' ?
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label for="evaluatePartition">评估分区</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.evaluatePartition + '" id="evaluatePartition" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' : '') +
//                         ((data.houseType == '商服用房' || data.houseType == '办公用房' || data.houseType == '公寓') ?
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>房屋坐落</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseLocation + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>物业名称</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.pmName + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>产权所有人</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.propertyRightOwner + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>建筑面积(平)</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.buildingArea + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>所在楼层</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.floor + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>建筑结构</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.buildingStructure + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>土地使用权类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.landUseRightType + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>土地使用权终止日期</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.landUseRightEndDate + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>剩余土地使用年限</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.remainingLandUseErm + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             (data.houseType == '公寓' ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label>层高</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + houseData.floorHeight + '" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             ((data.houseType == '办公用房' || data.houseType == '公寓') ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label>建成年份(年)</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + houseData.yearBuilt + '" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             ((data.houseType == '商服用房') ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label>房屋形状</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + houseData.houseShape + '" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             ((data.houseType == '公寓') ?
//                                 '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                                 '<div class="form-group">' +
//                                 '<label>建筑功能</label>' +
//                                 '<input type="text" disabled class="form-control" value="' + houseData.buildingImplement + '" placeholder="请输入">' +
//                                 '</div>' +
//                                 '</div>' : '') +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>交易日期</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.transactionDate + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>申报交易总价</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.declareTransactionTotalPrice + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>申报交易单价</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.declareTransactionUnitPrice + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>房屋其他说明</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseOtherDescription + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' : '') +
//                         ((data.houseType == '车位') ?
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>物业名称</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.pmName + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>产权所有人</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.propertyRightOwner + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>建筑面积(平)</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.buildingArea + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>套内面积(平)</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.internalArea + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>车位类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.parkingSpaceType + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>所在楼层</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.floor + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>土地使用权类型</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.landUseRightType + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>土地使用权终止日期</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.landUseRightEndDate + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>剩余土地使用年限</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.remainingLandUseErm + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>交易日期</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.transactionDate + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>申报交易总价</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.declareTransactionTotalPrice + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>申报交易单价</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.declareTransactionUnitPrice + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' +
//                             '<div class="col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="form-group">' +
//                             '<label>房屋其他说明</label>' +
//                             '<input type="text" disabled class="form-control" value="' + houseData.houseOtherDescription + '" placeholder="请输入">' +
//                             '</div>' +
//                             '</div>' : '')

//                     )

//                 }

//                 //附件（买方） 字段待确认
//                 if (data.buyerFileList && data.buyerFileList.length > 0) {
//                     res.data.buyerFileList.forEach((file) => {
//                         const modifiedDocumentType = file.documentType;
//                         const currId = modifiedDocumentType == '身份证' ? '11' : modifiedDocumentType == '房产证' ? '22' : modifiedDocumentType == '附件-其他' ? '33' : modifiedDocumentType == '契税减免' ? '44' : modifiedDocumentType == '税收优惠-其他' ? '55' : '';
//                         let uploadFrom = "#xqlstInfo" + currId;
//                         let fileType = file.type.split('/')[0];
//                         $(uploadFrom).append(
//                             '<div class="lstInfo col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="lstInfoBox">' +
//                             '<i class="glyphicon glyphicon-duplicate"></i>' +
//                             '<span class="fileInfoName">' + file.name + '</span>' +
//                             '<div class="btnList">' +
//                             '<i onclick="handlePreview(\'' + file.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
//                             '<i onclick="handleDownload(\'' + file.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
//                             // '<i onclick="handleDelete("ywdList", fIndex)" class="glyphicon glyphicon-trash"></i>' +
//                             '</div>' +
//                             '</div>' +
//                             '</div>'
//                         )
//                     })
//                 }

//                 //附件（卖方）
//                 if (data.fileList && data.fileList.length > 0) {
//                     res.data.fileList.forEach((file) => {
//                         const modifiedDocumentType = file.documentType;
//                         const currId = modifiedDocumentType == '身份证' ? '1' : modifiedDocumentType == '房产证' ? '2' : modifiedDocumentType == '附件-其他' ? '3' : modifiedDocumentType == '个税减免' ? '4' : modifiedDocumentType == '税收优惠-其他' ? '5' : '';
//                         let uploadFrom = "#xqlstInfo" + currId;
//                         let fileType = file.type.split('/')[0];
//                         $(uploadFrom).append(
//                             '<div class="lstInfo col-sm-6 col-md-6 col-lg-6">' +
//                             '<div class="lstInfoBox">' +
//                             '<i class="glyphicon glyphicon-duplicate"></i>' +
//                             '<span class="fileInfoName">' + file.name + '</span>' +
//                             '<div class="btnList">' +
//                             '<i onclick="handlePreview(\'' + file.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
//                             '<i onclick="handleDownload(\'' + file.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
//                             // '<i onclick="handleDelete("ywdList", fIndex)" class="glyphicon glyphicon-trash"></i>' +
//                             '</div>' +
//                             '</div>' +
//                             '</div>'
//                         )
//                     })

//                 }

//                 xqModal.style.right = "0";
//             }
//         },
//     })

// });
// // 关闭详情模态框
// closeXqModal.onclick = function () {
//     xqModal.style.right = "-100%";  // 向右滑出视野
// };

// //驳回 中止
// var rejectType = null;
// function showRejectModal(type) {// 0 驳回 1 中止
//     if (activeIds.length == 0) {
//         alert("请选择数据");
//         return;
//     }
//     if (activeIds.length > 1) {
//         alert("只能选择选择一条数据");
//         return;
//     }
//     $("#rejectVal").val("");
//     rejectType = type;
//     if (type == 0) {
//         $("#rejectTit").text("驳回原因");
//         $("#rejectVal").attr("placeholder", "请输入驳回原因");
//     } else {
//         $("#rejectTit").text("中止原因");
//         $("#rejectVal").attr("placeholder", "请输入中止原因");
//     }
//     $('#rejectModal').modal('show');
// }
// function rejectTrue() {
//     if ($("#rejectVal").val() == "" && rejectType == 0) {
//         alert("请输入驳回原因");
//         return;
//     }
//     if ($("#rejectVal").val() == "" && rejectType == 1) {
//         alert("请输入中止原因");
//         return;
//     }
//     $.ajax({
//         url: url + "/real/estate-file/reject",  // 接口
//         type: "POST",
//         data: {
//             id: activeIds.join(","),
//             rejectReason: $("#rejectVal").val(),
//             type: rejectType // 0 驳回 1 中止
//         },
//         headers: {
//             'Authorization': 'Bearer ' + 'test1',//token
//         },
//         success: function (response) {
//             if (response.code == 0) {
//                 alert("操作成功");
//                 $('#rejectModal').modal('hide');
//                 location.reload();
//             } else {
//                 alert(response.msg);
//             }
//         }
//     })
// }

// //审核通过 0 、已缴费 1、已办结 2
// var overType = null;
// function showOverModal(type) {
//     if (activeIds.length == 0) {
//         alert("请选择数据");
//         return;
//     }
//     overType = type;
//     var isApplyStatusConsistent = activeStatus.every(item => item === activeStatus[0]);
//     if (!isApplyStatusConsistent) {
//         alert("请选择申请状态相同的数据");
//         return;
//     }
//     if (type == 0) {
//         $("#overMsg").text("请确认所选数据是否审核通过？");
//     } else if (type == 1) {
//         $("#overMsg").text("请确认所选数据是否已缴费？");
//     } else if (type == 2) {
//         $("#overMsg").text("请确认所选数据是否已办结？");
//     } else if (type == 3) {
//         $("#overMsg").text("请确认是否对所选数据进行归档？");
//     }
//     $('#overModal').modal('show');
// }
// function overTrue() {
//     $.ajax({
//         url: url + "/real/estate-file/over",  // 接口
//         type: "POST",
//         data: {
//             id: activeIds.join(","),
//             type: overType // 审核通过 0 、已缴费 1、已办结 2 归档 3
//         },
//         headers: {
//             'Authorization': 'Bearer ' + 'test1',//token
//         },
//         success: function (response) {
//             if (response.code == 0) {
//                 alert("操作成功");
//                 $('#overModal').modal('hide');
//                 location.reload();
//             }
//         }
//     })
// }

//附件管理 房屋采集表 0、 税务承诺书 1、 税费申报表 2 、缴费付款码 3 、完税凭证 4、 完税发票 5
var attachmentType = null;
var attachmentName = null;
function showAttachmentModal(type) {
    if (activeIds.length == 0) {
        alert("请选择数据");
        return;
    }
    if (activeIds.length > 1) {
        alert("只能选择选择一条数据");
        return;
    }
    attachmentType = type;
    if (type == 0) {
        attachmentName = "房屋采集表";
        $("#attachmentPushTit").text("房屋采集表推送");
        $("#attachmentTrueTit").text("房屋采集表确认");
    } else if (type == 1) {
        attachmentName = "税务承诺书";
        $("#attachmentPushTit").text("税务承诺书推送");
        $("#attachmentTrueTit").text("税务承诺书确认");
    } else if (type == 2) {
        attachmentName = "税费申报表";
        $("#attachmentPushTit").text("税费申报表推送");
        $("#attachmentTrueTit").text("税费申报表确认");
    } else if (type == 3) {
        attachmentName = "缴费付款码";
        $("#attachmentPushTit").text("缴费付款码推送");
        $("#attachmentTrueTit").text("缴费付款码确认");
    } else if (type == 4) {
        attachmentName = "完税凭证";
        $("#attachmentPushTit").text("完税凭证推送");
        $("#attachmentTrueTit").text("完税凭证确认");
    } else if (type == 5) {
        attachmentName = "完税发票";
        $("#attachmentPushTit").text("完税发票推送");
        $("#attachmentTrueTit").text("完税发票确认");
    }
    $("#attachmentModal").modal("show");
    $("#pushUploadList1").html("");

    //通过id获取附件信息
    $.ajax({
        url: url + "/real/estate-file/attachmentList",
        type: "get",
        data: {
            id: activeIds.join(","),
            type: attachmentType // 房屋采集表 0、 税务承诺书 1、 税费申报表 2 、缴费付款码 3 、完税凭证 4、 完税发票 5
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            let rspData = response.data;
            if (response.code == 0) {
                //待推送的列表
                if (rspData.fileList && rspData.fileList.length > 0) {
                    rspData.fileList.forEach(function (file) {
                        let fileType = file.type.split('/')[0];
                        $("#pushUploadList1").append(
                            '<div data-id="' + response.data.id + '" class="lstInfo col-sm-12 col-md-12 col-lg-12">' +
                            '<div class="lstInfoBox">' +
                            '<i class="glyphicon glyphicon-duplicate"></i>' +
                            '<span class="fileInfoName">' + file.name + '</span>' +
                            '<div class="btnList">' +
                            '<i onclick="handlePreview(\'' + file.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
                            '<i onclick="handleDownload(\'' + file.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
                            '<i onclick="handleDelete(\'' + file.id + '\')" class="glyphicon glyphicon-trash"></i>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        )
                    })
                }
                //确认后的列表 fileListBack
                if (rspData.fileListBack && rspData.fileListBack.length > 0) {
                    rspData.fileListBack.forEach(function (file) {
                        let fileType = file.type.split('/')[0];
                        $("#pushUploadList2").append(
                            '<div data-id="' + response.data.id + '" class="lstInfo col-sm-12 col-md-12 col-lg-12">' +
                            '<div class="lstInfoBox">' +
                            '<i class="glyphicon glyphicon-duplicate"></i>' +
                            '<span class="fileInfoName">' + file.name + '</span>' +
                            '<div class="btnList">' +
                            '<i onclick="handlePreview(\'' + file.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
                            '<i onclick="handleDownload(\'' + file.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
                            '<i onclick="handleDelete(\'' + file.id + '\')" class="glyphicon glyphicon-trash"></i>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        )
                    })
                }
            }
        }
    })
}
function attachmentTrue() {
    //$("#pushUploadList1") 下 $(".lstInfo") 的data-id
    var infofileList = [];
    $("#pushUploadList1").find(".lstInfo").each(function () {
        infofileList.push($(this).data("id"));
    });
    $.ajax({
        url: url + "/real/estate-file/attachment",  // 接口
        type: "post",
        data: {
            fileList: infofileList,
            id: activeIds.join(","),
            type: attachmentType // 房屋采集表 0、 税务承诺书 1、 税费申报表 2 、缴费付款码 3 、完税凭证 4、 完税发票 5
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            if (response.code == 0) {
                alert("操作成功");
                // $("#attachmentModal").modal("hide");
                // location.reload();
            }
        }
    })
}

//上传
$(document).ready(function () {
    $('.uploadButton').change(function () {
        var currButton = $(this);
        var fileInputId = '#fileInput' + currButton.data('id');
        var fileInput = $(fileInputId)[0]; // 获取文件输入框
        var file = fileInput.files[0]; // 获取选中的第一个文件
        if (!file) {
            alert("请先选择一个文件！");
            return;
        }
        const formData = new FormData();
        formData.append("file", file);
        formData.append("documentType", attachmentName);
        formData.append("ownership", '4');
        $.ajax({
            url: url + "/real/estate-file/upload",  // 接口
            type: "POST",
            data: formData,
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            processData: false,  // 不处理数据
            contentType: false,  // 不设置 Content-Type
            success: function (response) {
                if (response.code == 0 && response.data.id) {
                    let uploadFrom = "#pushUploadList" + currButton.data('id');
                    let fileType = response.data.type.split('/')[0];
                    $(uploadFrom).append(
                        '<div data-id="' + response.data.id + '" class="lstInfo col-sm-12 col-md-12 col-lg-12">' +
                        '<div class="lstInfoBox">' +
                        '<i class="glyphicon glyphicon-duplicate"></i>' +
                        '<span class="fileInfoName">' + file.name + '</span>' +
                        '<div class="btnList">' +
                        '<i onclick="handlePreview(\'' + response.data.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
                        '<i onclick="handleDownload(\'' + response.data.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
                        '<i onclick="handleDelete(\'' + response.data.id + '\')" class="glyphicon glyphicon-trash"></i>' +
                        '</div>' +
                        '</div>' +
                        '</div>'
                    )
                }
            },
            error: function () {
                alert("上传失败，请重试！");
            }
        });
    });
});
var activeFjList = [];
var activeFjType = "";
var activeFjUrl = "";
var fjylIndex = 0;
$(document).on('click', '.infoYL', function () {
            fjylIndex = $(this).data('index');
            var index = $(this).data('index');
            var type = activeFjList[index].type;
            
            showPreview(index,type)
        });
        function showPreview(index,type) {
            console.log(index,type);
            activeFjType = type;
            activeFjUrl = activeFjList[index].url;
            var currurl = activeFjList[index].url;
            // var currurl = url + "/preview?id=" +activeFjList[index].id;
            if (type == 'png' || type == 'jpg' || type == 'jpeg') {
                $("#previewPdf").attr("src", "");
                $("#previewPdf").css("display", "none")
                $("#previewImg").attr("src", currurl);
                $("#previewImg").css("display", "block");
                $("#previewImgModal").css("display", "block");
            } else if (type == 'pdf') {
                $("#previewImg").attr("src", "");
                $("#previewImg").css("display", "none");
                $("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
                $("#previewPdf").css("display", "block");
                $("#previewImgModal").css("display", "block");
            } else {
                layer.msg("文件["+activeFjList[index].name+"]暂不支持预览！");
            }
        }
        $(document).on('click', '#prevBtn', function () {
            if(fjylIndex == 0){
                layer.msg("已经是第一个了！");
                return;
            }
            fjylIndex--;
            let type = activeFjList[fjylIndex].type;
            showPreview(fjylIndex,type)
        })
        $(document).on('click', '#nextBtn', function () {
            if(fjylIndex == activeFjList.length-1){
                layer.msg("已经是最后一个了！");
                return;
            }
            fjylIndex++;
            let type = activeFjList[fjylIndex].type;
            showPreview(fjylIndex,type)
        })

        $(document).on('click', '#fullBtn', function () {
            if (activeFjType == 'png' || activeFjType == 'jpg' || activeFjType == 'jpeg') {
                $("#fullFreviewPdf").attr("src", "");
                $("#fullFreviewPdf").css("display", "none")
                $("#fullFreviewImg").attr("src", activeFjUrl);
                $("#fullFreviewImg").css("display", "block");
                 $('#fullModal').modal('show');
            } else if (activeFjType == 'pdf') {
                $("#fullFreviewImg").attr("src", "");
                $("#fullFreviewImg").css("display", "none");
                $("#fullFreviewPdf").attr("src", activeFjUrl);//需要个返回流的接口+currurl  预览pdf
                $("#fullFreviewPdf").css("display", "block");
                $('#fullModal').modal('show');
            } else {
                layer.msg("文件暂不支持预览！");
            }
        })
         $(document).on('click', '#fullClose', function () {
            $('#fullModal').modal('hide');
         })
        
        
        $(document).on('click', '#preBtn', function () {
            $("#previewImg").attr("src", "");
            $("#previewPdf").attr("src", "");
            $("#previewImgModal").css("display", "none");
        });

//上传的图片预览
function handlePreview(currurl,isImg) {
    if(isImg == 'image'){
        $("#previewImg").attr("src", currurl);
        $("#previewImg").css("display","block");
        $("#previewPdf").css("display","none")
    }else{
        $("#previewPdf").attr("src", "http://**********:9527/sfzz-prod-api/real/estate-file/preViewPdf?id=1845");//需要个返回流的接口+currurl  预览pdf
        $("#previewPdf").css("display","block")
        $("#previewImg").css("display","none");
    }
    $('#previewImgModal').modal('show');
}
//下载文件
function handleDownload(currUrl) {
    console.log(currName)
    const aTag = document.createElement('a'); // 创建 a 标签
    aTag.href = currUrl;  // 设置图片的 URL
    document.body.appendChild(aTag); // 将 a 标签添加到页面中
    aTag.click(); // 触发点击事件，开始下载
    document.body.removeChild(aTag); // 下载后移除 a 标签
}


//new add  0214

//签字开始
var activeDocumentType = "";
var pdfUrl = 'https://gxsw.wzheiying.com:444/sfzz-admin-api/infra/file/24/get/16220a27384337b6b62dc7cae41282912eeb9456a7d8680707bfd1fbeba0dd2b.pdf';
var positionObj = {
    "存量房交易税费申报表（转让方）": [//存量房交易税费申报表（转让方） 只能单字查找关键字 关键字：…
        {type:1, x:180, y:200, w:150, h:100, keyword:'…', start:'left',index:7},
        {type:1, x:280, y:200, w:150, h:100, keyword:'…', start:'left',index:7},
        {type:1, x:380, y:200, w:150, h:100, keyword:'…', start:'left',index:7}
    ],
    "存量房交易信息采集表":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字（盖章）确认', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字（盖章）确认', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'税务人员签字审核', start:'right',index:0}
    ],
    "办公用房":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "车位":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "个人":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'承受方签字', start:'right',index:0}
    ],
    "公寓":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0}
    ],
    "商服用房":[  
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "存量房交易税费申报表（转让方）":[  
        {type:1, x:150, y:200, w:120, h:60, keyword:'…', start:'right',index:7},
        {type:1, x:250, y:200, w:120, h:60, keyword:'…', start:'right',index:7}
    ]
}
function sign(pdfUrl,documentType,signAreasArrs) {// pdfUrl 要签名的文件流地址   documentType 传对应要签名文件的  documentType   signAreasArr  取上方positionObj对应的数组（需要确认下咋匹配）
    activeDocumentType = documentType;
    fetch(pdfUrl)
    .then(response => response.blob())  // 获取文件流（Blob） 
    .then(blob => {
        const reader = new FileReader();
        reader.onloadend = () => {
            const base64String = reader.result.split(',')[1];  // 提取 Base64 字符串
            if(signAreasArrs){
                var signAreasArr = signAreasArrs;
            }else{
                var signAreasArr =JSON.stringify($("#signAreasArr").val());//测试用
            }
            
            TRANSCEND_SIGNDEVICE.startPdf(callback, getSignConfig(1,base64String,signAreasArr)); // 开始签名
        };
        reader.readAsDataURL(blob);  // 将 Blob 转为 Base64
    })
    .catch(error => {
        console.error('Error fetching the file:', error);
    });
}

function uploadCallBack(backFile){ //上传成功回调过来的新 pdf
    const formData = new FormData();
    formData.append("file", backFile);
    formData.append("documentType", activeDocumentType);
    formData.append("ownership", '4');//改对应的归属
    $.ajax({
        url: url + "/real/estate-file/upload",  // 接口
        type: "POST",
        data: formData,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        processData: false,  // 不处理数据
        contentType: false,  // 不设置 Content-Type
        success: function (response) {
            if (response.code == 0 && response.data.id) {
                let uploadFrom = "#pushUploadList" + currButton.data('id');
                let fileType = response.data.type.split('/')[0];
                $(uploadFrom).append(
                    '<div data-id="' + response.data.id + '" class="lstInfo col-sm-12 col-md-12 col-lg-12">' +
                    '<div class="lstInfoBox">' +
                    '<i class="glyphicon glyphicon-duplicate"></i>' +
                    '<span class="fileInfoName">' + file.name + '</span>' +
                    '<div class="btnList">' +
                    '<i onclick="handlePreview(\'' + response.data.url + '\', \'' + fileType + '\')" class="glyphicon glyphicon-zoom-in"></i>' +
                    '<i onclick="handleDownload(\'' + response.data.url + '\')" class="glyphicon glyphicon-arrow-down"></i>' +
                    '<i onclick="handleDelete(\'' + response.data.id + '\')" class="glyphicon glyphicon-trash"></i>' +
                    '</div>' +
                    '</div>' +
                    '</div>'
                )
            }
        },
        error: function () {
            alert("上传失败，请重试！");
        }
    });
}
//签字结束


//改造详情
var activeId = "";
var xqModal = document.getElementById("xqModal");
var closeXqModal = document.getElementById("closeXqModal");
$(document).on('click', '.getDetail', function () {
    console.log("222")
    activeId = $(this).data('id');
    var id = $(this).data('id');  // 获取当前点击的元素的data-id
    $.ajax({
        type: "get",
        url: url + "/real/estate-collection-info/sellerGet?id=54",//id
        async: true,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        data: {
            id: id,
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (res) {
            //大概的报文格式
            if (res.code == 0) {
                var data = res.data;
                //data.propertyData //假设返回值
                const propertyData = {
                    propertyAddress: "北京市朝阳区某小区",
                    district: "朝阳区",
                    street: "某街道",
                    buildingName: "某小区",
                    houseType: "住宅",
                    transferObject: "张三",
                    transferPurpose: "出售",
                    unitType: "两室一厅",
                    houseLayout: "两室一厅",
                    buildingStructure: "钢筋混凝土钢筋混凝土钢筋混凝土钢筋混凝土",
                    buildingArea: "120平方米",
                    buildingNumber: "1号楼",
                    floor: "5层",
                    totalFloorsAbove: "10层",
                    totalFloorsBelow: "2层",
                    orientation: "南",
                    propertyManagement: "某物业公司",
                    elevator: "有",
                    completionYear: "2010",
                    landAcquisitionMethod: "出让",
                    transferMethod: "买卖",
                    landUsageEndDate: "2070-01-01",
                    floorHeight: "3米",
                    houseShape: "矩形",
                    buildingFunction: "居住",
                    interiorArea: "80平方米",
                    parkingType: "地下地下地下地下地下地下地下地下地下地下地下地下地下地下地下地下",
                    contractAmount: "1000000",
                    contractDate: "2023-10-01"
                };
                // fillTable(propertyData);//渲染房屋交易表格

                //渲染卖方表格
                if (data.sellerInfo && data.sellerInfo.length > 0) {
                    data.sellerInfo.forEach((seller) => {
                        $('#sellerBox').append(
                            `<tr>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                            </tr>`
                        )
                    })
                }
                //渲染买方表格
                if (data.buyerInfo && data.buyerInfo.length > 0) {
                    data.buyerInfo.forEach((seller) => {
                        $('#buyerBox').append(
                            `<tr>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                                <td>${seller.name}</td>
                                <td>${seller.idNum}</td>
                                <td>${seller.phone}</td>
                            </tr>`
                        )
                    })
                }


                $("#FJFileList").html("");
                var rspFjData = [
                    {
                        id: "1",
                        type: "jpg",
                        url: "https://picsum.photos/200/300",
                        name: "图片1.jpg",
                    },
                    {
                        id: "2",
                        type: "pdf",
                        url: "http://127.0.0.1:5500/taxreview/pdf/税费经济分析复盘报告.pdf",
                        name: "taxes.pdf",
                    },
                    {
                        id: "3",
                        type: "doc",
                        url: "https://picsum.photos/200/300",
                        name: "3.doc",
                    },
                    {
                        id: "4",
                        type: "xls",
                        url: "https://picsum.photos/200/300",
                        name: "4.xls",
                    },
                    {
                            id: "5",
                        type: "jpg",
                        url: "https://picsum.photos/600/600",
                        name: "5.jpg",
                    },
                ];
                activeFjList = rspFjData;
                for (var i = 0; i < rspFjData.length; i++) {
                    $("#FJFileList").append(
                        `<tr>
                            <td class="fixed-left-column" style='width: 32px;'><input class="tableCheck" data-id="' + rspData[i].id + '" type="checkbox"></input></td>
                            <td>0</td>
                            <td>1</td>
                            <td>2</td>
                            <td>3</td>
                            <td>4</td>
                            <td>5</td>
                            <td>6</td>
                            <td>刷卡机啊德哈</td>
                            <td>8</td>
                            <td class="fixed-column" style='width: 220px;'>
                                <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-index="${i}" data-type='${rspFjData[i].type}' data-url='${rspFjData[i].url}'>预览</span>
                                <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">下载</span>
                                <span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">删除</span>
                                <span class="infoTS" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">推送</span>
                                <span class="infoQR" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">确认</span>
                            </td>
                        </tr>`
                    )
                }

                xqModal.style.right = "0";
            }
        },
    })

});
// 关闭详情模态框
// closeXqModal.onclick = function () {
//     xqModal.style.right = "-100%";  // 向右滑出视野
// };
//渲染详情-房屋交易信息表格  在上方详情接口返回值后  调用该方法传值
function fillTable(data) {
    document.getElementById('propertyAddress').textContent = data.propertyAddress;
    document.getElementById('district').textContent = data.district;
    document.getElementById('street').textContent = data.street;
    document.getElementById('buildingName').textContent = data.buildingName;
    document.getElementById('houseType').textContent = data.houseType;
    document.getElementById('transferObject').textContent = data.transferObject;
    document.getElementById('transferPurpose').textContent = data.transferPurpose;
    document.getElementById('unitType').textContent = data.unitType;
    document.getElementById('houseLayout').textContent = data.houseLayout;
    document.getElementById('buildingStructure').textContent = data.buildingStructure;
    document.getElementById('buildingArea').textContent = data.buildingArea;
    document.getElementById('buildingNumber').textContent = data.buildingNumber;
    document.getElementById('floor').textContent = data.floor;
    document.getElementById('totalFloorsAbove').textContent = data.totalFloorsAbove;
    document.getElementById('totalFloorsBelow').textContent = data.totalFloorsBelow;
    document.getElementById('orientation').textContent = data.orientation;
    document.getElementById('propertyManagement').textContent = data.propertyManagement;
    document.getElementById('elevator').textContent = data.elevator;
    document.getElementById('completionYear').textContent = data.completionYear;
    document.getElementById('landAcquisitionMethod').textContent = data.landAcquisitionMethod;
    document.getElementById('transferMethod').textContent = data.transferMethod;
    document.getElementById('landUsageEndDate').textContent = data.landUsageEndDate;
    document.getElementById('floorHeight').textContent = data.floorHeight;
    document.getElementById('houseShape').textContent = data.houseShape;
    document.getElementById('buildingFunction').textContent = data.buildingFunction;
    document.getElementById('interiorArea').textContent = data.interiorArea;
    document.getElementById('parkingType').textContent = data.parkingType;
    document.getElementById('contractAmount').textContent = data.contractAmount;
    document.getElementById('contractDate').textContent = data.contractDate;
}
//驳回 中止
var rejectType = null;
function showRejectModal(type) {// 0 驳回 1 中止
    $("#rejectVal").val("");
    rejectType = type;
    if (type == 0) {
        $("#rejectTit").text("驳回原因");
        $("#rejectVal").attr("placeholder", "请输入驳回原因");
    } else {
        $("#rejectTit").text("中止原因");
        $("#rejectVal").attr("placeholder", "请输入中止原因");
    }
    $('#rejectModal').modal('show');
}
function rejectTrue() {
    if ($("#rejectVal").val() == "" && rejectType == 0) {
        alert("请输入驳回原因");
        return;
    }
    if ($("#rejectVal").val() == "" && rejectType == 1) {
        alert("请输入中止原因");
        return;
    }
    $.ajax({
        url: url + "/real/estate-file/reject",  // 接口
        type: "POST",
        data: {
            id: activeId,
            rejectReason: $("#rejectVal").val(),
            type: rejectType // 0 驳回 1 中止
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            if (response.code == 0) {
                alert("操作成功");
                $('#rejectModal').modal('hide');
                location.reload();
            } else {
                alert(response.msg);
            }
        }
    })
}

$(document).on('click', '.fileManage', function () {
    $(".fjModal").show();
    // $.ajax({
    //     url: url + "/real/estate-file/file-manage",  // 接口
    //     type: "POST",
    //     data: {
    //         id: activeId
    //     },
    //     headers: {
    //         'Authorization': 'Bearer ' + 'test1',//token
    //     },
    //     success: function (response) {
                // let rspData = response.data;
    //         if (response.code == 0) {
        $("#FJFileList").html("");
    //             if (rspData.length == 0) {
                    // $("#FJFileList").html("<tr><td style='text-align: center;' colspan='10'>暂无数据</td></tr>");
    //             }else{
                for (var i = 0; i < 20; i++) {
                    $("#FJFileList").append(
                        `<tr>
                            <td class="fixed-left-column" style='width: 32px;'><input class="tableCheck" data-id="' + rspData[i].id + '" type="checkbox"></input></td>
                            <td>0</td>
                            <td>1</td>
                            <td>2</td>
                            <td>3</td>
                            <td>4</td>
                            <td>5</td>
                            <td>6</td>
                            <td>刷卡机啊德哈</td>
                            <td>8</td>
                            <td class="fixed-column" style='width: 220px;'>
                                <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">预览</span>
                                <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">下载</span>
                                <span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">删除</span>
                                <span class="infoTS" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">推送</span>
                                <span class="infoQR" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">确认</span>
                            </td>
                        </tr>`
                    )
                }

    //         }
    //     }
    // })
});
$('.close-btn').on('click', function(e) {
    $(".fjModal").hide();
});

// //审核通过 
function showOverModal() {
    $("#overMsg").text("是否确认审核通过？");
    $('#overModal').modal('show');
}
function overTrue() {
    $.ajax({
        url: url + "/real/estate-file/over",  // 接口
        type: "POST",
        data: {
            id: activeId
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            if (response.code == 0) {
                alert("操作成功");
                $('#overModal').modal('hide');
                location.reload();
            }
        }
    })
}