var url = location.origin+"/clf";//请求前缀
// var url = "http://10.1.5.194:9527/sfzz-prod-api";//请求前缀
var loginUser = null;//登录用户
var activeIds = [];
var activeDatas = [];
var activeInfoIds = [];
//当前页数据
var layer = null;
layui.use('layer',function(){
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
	elem: '#zzrwwcrq',
});
var currentPage = 1; // 当前页
var deqszzcjPage = 1;
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}

//获取用户信息
function getloginuser(){
	$.ajax({
		type: "get",
		url: url + "/getLoginUser",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',
		},
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (data) {
			loginUser = data;
		}
	});
}
//获取链接上参数
function getQueryString(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	var r = window.location.search.substr(1).match(reg);
	if (r != null) return unescape(r[2]);
	return null;
}
//切换监听
var element = layui.element;
//初始进入页面
$(function () {
	var tabIndex = getQueryString("index");
	if (tabIndex !== null) {
		tabIndex = parseInt(tabIndex, 10);
		layui.element.tabChange('my-handle', 'tab' + tabIndex);
	} else {
		search(currentPage);
	}
	// getloginuser();
});
element.on('tab(my-handle)', function(data){
	if (data.index == 0) {
		activeIds = [];
		activeDatas = [];
		search(currentPage);
	} else if (data.index == 1) {
		activeIds = [];
		activeDatas = [];
		deqszzcjsearch(deqszzcjPage)
	}
});
//tab-追征企业清单-start
//查询
function search(currentPage) {
	console.log("0");
	var index = layer.load(0);
	var nsrmc = $("#nsrmc").val();
	var shxydm = $("#shxydm").val();
	var qslb = $("#qslb").val();
	var sfhdlhd = $("#sfhdlhd").val();
	var gly = $("#gly").val();
	var zgswks = $("#zgswks").val();
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nsrmc: nsrmc,
				shxydm: shxydm,
				qslb: qslb,
				sfhdlhd: sfhdlhd,
				ssgly: gly,
				zgswks: zgswks,
				pageNo: currentPage,
				pageSize: 10,
			}
		,
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (res) {
			layer.close(index);
			$("#dataList").html('');
			let rspData = res.data;
			if(rspData && rspData.length > 0) {
				$("#pagination").css("display", "block");
				loadList(rspData);
				getMyMindMapPage(res.total, currentPage, "#pagination", 10,search);
			}else{
				$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>');
				$("#pagination").css("display", "none");
			}

		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function loadList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#dataList").append('<tr>' +
			'<td class="fixed-left-column">' + (i + 1) + '</td>' +
			'<td>' + rspData[i].tjrq + '</td>' +
			'<td>' + rspData[i].shxydm + '</td>' +
			'<td>' + rspData[i].nsrmc + '</td>' +
			'<td>' + rspData[i].nsrzt + '</td>' +
			'<td>' + rspData[i].fddbrxm + '</td>' +
			'<td>' + rspData[i].fddbrsfzhm + '</td>' +
			'<td>' + rspData[i].qslb + '</td>' +
			'<td>' + rspData[i].jyqk + '</td>' +
			'<td>' + rspData[i].zgswks + '</td>' +
			'<td>' + rspData[i].jdxz + '</td>' +
			'<td>' + rspData[i].ssgly + '</td>' +
			'<td>' + rspData[i].qsye + '</td>' +
			'<td>' + rspData[i].wncq + '</td>' +
			'<td>' + rspData[i].bnxq + '</td>' +
			'<td>' + rspData[i].bz + '</td>' +
			'<td class="fixed-column">'+
			(rspData[i].zzrwzt == '未处理' ?
				'<span onclick="zzrwfkFn(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">追征任务反馈</span>' : '<span style="cursor:not-allowed;margin-right: 10px;color: #ccc;font-size: 12px;">追征任务反馈</span>' ) +
			'</td></tr>');
	};
}
//导出
function dcFn() {
	var nsrmc = $("#nsrmc").val();
	var shxydm = $("#shxydm").val();
	var qslb = $("#qslb").val();
	var sfhdlhd = $("#sfhdlhd").val();
	var gly = $("#gly").val();
	var zgswks = $("#zgswks").val();
	var jsonData = JSON.stringify({
		nsrmc: nsrmc,
		shxydm: shxydm,
		qslb: qslb,
		sfhdlhd: sfhdlhd,
		ssgly: gly,
		zgswks: zgswks
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsglzzrwqdb/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//全选
function checkAllInfo(){
	var tableck = document.getElementById("tableInfo").checked;
	var checkboxs = $(".tableCheckInfo");
	activeInfoIds=[];
	for(var i = 0;i<checkboxs.length;i++){
		var checkbox = checkboxs[i];
		if(tableck){
			checkbox.checked = true;
			activeInfoIds.push($(checkbox).data('id'));
		}else{
			checkbox.checked = false;
			activeInfoIds=[];
		}
	}
}
$(document).on('click', '#kkjsqdList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.tableCheckInfo');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.tableCheckInfo', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		activeInfoIds.push($(this).data('id'));
		if($("#kkjsqdList tr").length==activeInfoIds.length){
			document.getElementById("tableInfo").checked = true;
		}else{
			document.getElementById("tableInfo").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < activeInfoIds.length; i++) {
			if (activeInfoIds[i] == $(this).data('id')) {
				activeInfoIds.splice(i, 1);
			}
		}
		document.getElementById("tableInfo").checked = false;
	}
});
//扣款解锁清单
function kkjsqdFn() {
	$(".kkjsqdModal").show();
	var index = layer.load(0);
	let rqsData = {};
	$("#kkjsqdList").html('')
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdb/kkjsqd",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if(rspData && rspData.length>0){
					let kkjsqdhtml= '';
					for (var i = 0; i < rspData.length; i++) {
						kkjsqdhtml += `
							<tr>
								<td><input class="tableCheckInfo" data-id="${rspData[i].uuid}"  type="checkbox"></input></td>
								<td>${i + 1}</td>
								<td>${ rspData[i].tjrq }</td>
								<td>${ rspData[i].nsrmc }</td>
								<td>${ rspData[i].qslb }</td>
								<td>${ rspData[i].zgswks }</td>
								<td>${ rspData[i].ssgly }</td>
								<td>${ rspData[i].skcllx }</td>
							</tr>
						`
					}
					$("#kkjsqdList").append(kkjsqdhtml);
				}else{
					$("#kkjsqdList").html(`<tr><td colspan="8">暂无数据</td></tr>`);
				}

			}else{
				layer.msg("请求失败："+res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//扣款解锁清单-清单确认
function qdqrFn() {
	if(activeInfoIds.length == 0){
		layer.msg('请选择要操作的数据');
		return;
	}
	var index = layer.load(0);
	let rqsData = {
		bz: activeInfoIds.join(",")
	};
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdb/kkjsqdqr",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				layer.msg("操作成功");
				$(".kkjsqdModal").hide();
			}else{
				layer.msg("请求失败："+res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//追征任务反馈
var editId = null;
function zzrwfkFn(evt) {
	let element = evt.target;
	editId = $(element).attr("data-id");
	$(".zzrwfkModal").show();
}
//追征任务反馈-确认
function zzrwfk() {
	var index = layer.load(0);
	let rqsData = {
		uuid: editId,
		zzrwwcrq: $("#zzrwwcrq").val(),
		zzcs: $("#zzcs").val(),
		zzjg: $("#zzjg").val(),
		zzry: $("#zzry").val(),
	};
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/zzrwfk",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				layer.msg("操作成功");
				search(currentPage);
				$(".zzrwfkModal").hide();
			}else{
				layer.msg("请求失败："+res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//tab-追征企业清单-end







//tab-大额欠税阻止出境企业上报-start
function deqszzcjsearch(deqszzcjPage) {
	console.log("1");
	activeIds = [];
	activeDatas = [];
	var index = layer.load(0);
	var nsrmc1 = $("#nsrmc1").val();
	var nsrszt = $("#nsrzt").val();
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nsrmc: nsrmc1,
				nsrszt: nsrszt,
				pageNo: deqszzcjPage,
				pageSize: 10,
			}
		,
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (res) {
			layer.close(index);
			$("#deqszzDataList").html('');
			let rspData = res.data;
			if(rspData && rspData.length>0){
				$("#pagination1").css("display", "block");
				getMyMindMapPage(res.total, deqszzcjPage, "#pagination1", 10,deqszzcjsearch)
				loadDeqsList(rspData);
			}else{
				$("#deqszzDataList").html('<tr><td colspan="19" style="text-align: center;">暂无数据</td></tr>');
				$("#pagination1").css("display", "none");
			}

		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function loadDeqsList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#deqszzDataList").append('<tr>' +
			'<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].zzrwqduuid + '"  data-zztype="' + rspData[i].sfqrzzcj + '"  type="checkbox"></input></td>' +
			'<td>' + (i + 1) + '</td>' +
			'<td>' + rspData[i].tjrq + '</td>' +
			'<td>' + rspData[i].shxydm + '</td>' +
			'<td>' + rspData[i].nsrmc + '</td>' +
			'<td>' + rspData[i].nsrzt + '</td>' +
			'<td>' + rspData[i].fddbrxm + '</td>' +
			'<td>' + rspData[i].fddbrsfzhm + '</td>' +
			'<td>' + rspData[i].qslb + '</td>' +
			'<td>' + rspData[i].jyqk + '</td>' +
			'<td>' + rspData[i].zgswks + '</td>' +
			'<td>' + rspData[i].jdxz + '</td>' +
			'<td>' + rspData[i].ssgly + '</td>' +
			'<td>' + rspData[i].qsye + '</td>' +
			'<td>' + rspData[i].wncq + '</td>' +
			'<td>' + rspData[i].bnxq + '</td>' +
			'<td>' + rspData[i].skcllx + '</td>' +
			'<td>' + rspData[i].sfysb + '</td>' +
			'<td>' + rspData[i].sfqrzzcj + '</td>' +
			'<td>' + rspData[i].bz + '</td>' +
			'<td class="fixed-column">'+
			'<span onclick="zzjgFn(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].zbuuid + '">追征结果</span>' +
			((rspData[i].sfqrzzcj=="是" && rspData[i].zzcjrwfkzt=="WFK")?'<span onclick="zzcjrwfkFn(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '"  data-zbuuid="' + rspData[i].zbuuid + '">阻止出境任务反馈</span>':'') +
			'</td></tr>');
	};
}
//阻止出境企业上报

//全选
function checkAll(){
	var tableck = document.getElementById("tableqs").checked;
	var checkboxs = $(".tableCheck");
	activeIds=[];
	activeDatas = [];
	for(var i = 0;i<checkboxs.length;i++){
		var checkbox = checkboxs[i];
		if(tableck){
			checkbox.checked = true;
			activeIds.push($(checkbox).data('id'));
			activeDatas.push($(checkbox).data('zztype'));
		}else{
			checkbox.checked = false;
			activeIds=[];
			activeDatas = [];
		}
	}
}
$(document).on('click', '#deqszzDataList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.tableCheck');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.tableCheck', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		activeIds.push($(this).data('id'));
		activeDatas.push($(this).data('zztype'));
		if($("#deqszzDataList tr").length==activeIds.length){
			document.getElementById("tableqs").checked = true;
		}else{
			document.getElementById("tableqs").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < activeIds.length; i++) {
			if (activeIds[i] == $(this).data('id')) {
				activeIds.splice(i, 1);
				activeDatas.splice(i, 1);
			}
		}
		document.getElementById("tableqs").checked = false;
	}
});
function zzcjrwsbFn() {
	if(activeIds.length==0){
		layer.msg("请选择数据");
		return;
	}
	for(var i=0;i<activeDatas.length;i++){
		if(activeDatas[i]=="是"){
			layer.msg("请选择未阻止出境的数据");
			return;
		}
	}
	let rqsData = {
		bz: activeIds.join(',')
	}
	layer.confirm("确认要上报吗？",{btn:["确认","取消"],btn1:function (index,layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/zzcjqysb",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData,

				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						activeIds = [];
						activeDatas = [];
					}else{
						layer.msg("请求失败："+res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}});
}
//追征结果
function zzjgFn(evt) {
	$(".zzjgModal").show();
	$("#zzjgList").html('');
	var index = layer.load(0);
	let element = evt.target;
	let rowId = $(element).attr("data-id");
	let rqsData = {
		zbuuid: rowId
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/zzjg",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if(rspData && rspData.length>0){
					let kkjsqdhtml= '';
					for (var i = 0; i < rspData.length; i++) {
						kkjsqdhtml += `
							<tr>
								<td>${ i+1 }</td>
								<td>${ rspData[i].tjrq }</td>
								<td>${ rspData[i].nsrmc }</td>
								<td>${ rspData[i].qslb }</td>
								<td>${ rspData[i].zgswks }</td>
								<td>${ rspData[i].ssgly }</td>
								<td>${ rspData[i].xfrq }</td>
								<td>${ rspData[i].zzrwzt }</td>
								<td>${ rspData[i].zzry }</td>
								<td>${ rspData[i].zzrwwcrq }</td>
								<td>${ rspData[i].zzcs }</td>
								<td>${ rspData[i].zzjg }</td>
							</tr>
						`
					}
					$("#zzjgList").append(kkjsqdhtml);
				}else{
					$("#zzjgList").html('<tr><td colspan="12">暂无数据</td></tr>');
				}

			}else{
				layer.msg("请求失败："+res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//阻止出境任务反馈
var zzUuid = null;
var zzZbuuid = null;
var zzcjfk_Fj_Zbuuid= null;
function zzcjrwfkFn(evt) {
	let element = evt.target;
	zzUuid = $(element).attr("data-id");
	zzZbuuid = $(element).attr("data-zbuuid");
	zzcjfk_Fj_Zbuuid = $(element).attr("data-id");
	$(".dezzcjModal").show();
	loadFjlist();
}
function loadFjlist() {
	var index = layer.load(0);
	$("#FJFileList").html('');
	let rqsData = {
		zbuuid: zzcjfk_Fj_Zbuuid
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglfjxxb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				let fjhtml= '';
				if( rspData && rspData.length>0){
					for (var i = 0; i < rspData.length; i++) {
						fjhtml += `
							<tr>
								<td>${ i+1 }</td>
								<td>${ rspData[i].wjm }</td>
								<td>${ rspData[i].wjdx }</td>
								<td>
									<span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}" data-type="${rspData[i].wjlx}">预览</span>
									<span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">下载</span>
									<span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">删除</span>
								</td>
							</tr>
						`
					}
					$("#FJFileList").append(fjhtml);
				}else{
					$("#FJFileList").html('<tr><td colspan="4">暂无数据</td></tr>');
				}
			}else{
				layer.msg("请求失败："+res.msg);
				layer.close(index);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function rwfkFn() {
	let index = layer.load(0);
	layer.confirm("确认要提交阻止出境反馈吗？",{btn:["确认","取消"],btn1:function (index,layero) {
			let rqsData = {
				uuid: zzUuid
			}
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/zzcjrwfkqr",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData,

				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						$(".dezzcjModal").hide();
						deqszzcjsearch(deqszzcjPage)
					}else{
						layer.msg("请求失败："+res.msg);
						layer.close(index);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}});
}
//上传
$(document).on('change', '.uploadButton', function () {
	var fileInput = $('#fileInput')[0]; // 获取文件输入框
	var file = fileInput.files[0]; // 获取选中的第一个文件
	if (!file) {
		layer.msg("请先选择一个文件！");
		return;
	}
	const formData = new FormData();
	formData.append("file", file);
	formData.append("zbuuid", zzcjfk_Fj_Zbuuid);
	formData.append("zzcjfj", 1);
	$.ajax({
		url: url + "/qyqsgl/qyqsglfjxxb/upload",
		type: "POST",
		data: formData,
		headers: {
			'Authorization': 'Bearer ' + 'test1',//token
		},
		processData: false,  // 不处理数据
		contentType: false,  // 不设置 Content-Type
		success: function (response) {
			// if (response.code == 0) {
			layer.msg("上传成功");
			loadFjlist();
			$('#fileInput').val('');
			// }
		},
		error: function () {
			layer.msg("上传失败，请重试！");
		}
	});
});
//预览
$(document).on('click', '.infoYL', function () {
	var id = $(this).data('id');
	var type =  $(this).data('type').toLowerCase();;
	var currurl =url+"/qyqsgl/qyqsglfjxxb/preview?uuid="+id;
	if(type == 'png' || type == 'jpg' || type == 'jpeg'){
		$("#previewPdf").attr("src", "");
		$("#previewPdf").css("display","none")
		$("#previewImg").attr("src", currurl);
		$("#previewImg").css("display","block");
		$('#previewImgModal').modal('show');
	}else if(type == 'pdf'){
		$("#previewImg").attr("src", "");
		$("#previewImg").css("display","none");
		$("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
		$("#previewPdf").css("display","block");
		$('#previewImgModal').modal('show');
	}else{
		layer.msg("当前格式暂不支持预览！");
	}
});
//下载
$(document).on('click', '.infoXZ', function () {
	var id = $(this).data('id');
	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsglfjxxb/download?uuid="+id;  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag); // 下载后移除 a 标签
});
//删除
$(document).on('click', '.infoSC', function () {
	var id = $(this).data('id');
	layer.confirm("是否要删除该文件？",{btn:["确认","取消"],btn1:function (index,layero) {
			var index = layer.load(0);
			$.ajax({
				url: url + "/qyqsgl/qyqsglfjxxb/delete",  // 接口
				type: "post",
				data: {
					uuid: id
				},
				headers: {
					'Authorization': 'Bearer ' + 'test1',//token
				},
				success: function (response) {
					layer.close(index);
					if (response.code == 0) {
						layer.msg("删除成功");
						loadFjlist();
					}else{
						layer.msg("请求失败："+response.msg);
					}
				}
			});
		}});
});

//导出
function dedcFn() {
	var nsrmc1 = $("#nsrmc1").val();
	var nsrszt = $("#nsrzt").val();
	var jsonData = JSON.stringify({
		nsrmc: nsrmc1,
		nsrszt: nsrszt
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}

//tab-大额欠税阻止出境企业上报-end



//关闭弹窗
function closeModal() {
	$(".kkjsqdModal").hide();
	$(".zzrwfkModal").hide();
	$(".zzjgModal").hide();
	$(".dezzcjModal").hide();
}