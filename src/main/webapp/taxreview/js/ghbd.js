var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
    elem: '#ID-laydate-range',
    range: ['#ID-laydate-start-date', '#ID-laydate-end-date']
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
//切换监听
var element = layui.element;
element.on('tab(my-handle)', function (data) {
	if (data.index == 0) {
		search(currentPage);
	} else if (data.index == 1) {
	} else if (data.index == 2) {
	} else if (data.index == 3) {
	}
});
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
			$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ rq: "2025-04-28", ghlhj: "201", ghlxz: "88", ghljs: "10", ghljzz: "20", ysryshhj: "200", ysryshxz: "10", ysryshjs: "20", ysryshjzz: "30", zdsyhhj: "200", zdsyhxz: "10", zdsyhjs: "20", zdsyhjzz: "30", kpwshhj: "200", kpwshxz: "10", kpwshjs: "20", kpwshjzz: "30", spwshhj: "200", spwshxz: "10", spwshjs: "20", spwshjzz: "30", wbshhj: "200", wbshxz: "10", wbshjs: "20", wbshjzz: "30"},
				{ rq: "2025-04-28", ghlhj: "201", ghlxz: "88", ghljs: "10", ghljzz: "20", ysryshhj: "200", ysryshxz: "10", ysryshjs: "20", ysryshjzz: "30", zdsyhhj: "200", zdsyhxz: "10", zdsyhjs: "20", zdsyhjzz: "30", kpwshhj: "200", kpwshxz: "10", kpwshjs: "20", kpwshjzz: "30", spwshhj: "200", spwshxz: "10", spwshjs: "20", spwshjzz: "30", wbshhj: "200", wbshxz: "10", wbshjs: "20", wbshjzz: "30"},
				{ rq: "2025-04-28", ghlhj: "201", ghlxz: "88", ghljs: "10", ghljzz: "20", ysryshhj: "200", ysryshxz: "10", ysryshjs: "20", ysryshjzz: "30", zdsyhhj: "200", zdsyhxz: "10", zdsyhjs: "20", zdsyhjzz: "30", kpwshhj: "200", kpwshxz: "10", kpwshjs: "20", kpwshjzz: "30", spwshhj: "200", spwshxz: "10", spwshjs: "20", spwshjzz: "30", wbshhj: "200", wbshxz: "10", wbshjs: "20", wbshjzz: "30"},
				{ rq: "2025-04-28", ghlhj: "201", ghlxz: "88", ghljs: "10", ghljzz: "20", ysryshhj: "200", ysryshxz: "10", ysryshjs: "20", ysryshjzz: "30", zdsyhhj: "200", zdsyhxz: "10", zdsyhjs: "20", zdsyhjzz: "30", kpwshhj: "200", kpwshxz: "10", kpwshjs: "20", kpwshjzz: "30", spwshhj: "200", spwshxz: "10", spwshjs: "20", spwshjzz: "30", wbshhj: "200", wbshxz: "10", wbshjs: "20", wbshjzz: "30"},
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].rq + '</td>' +
            '<td>' + rspData[i].ghlhj + '</td>' +
            '<td>' + rspData[i].ghlxz + '</td>' +
            '<td>' + rspData[i].ghljs + '</td>' +
            '<td>' + rspData[i].ghljzz + '</td>' +
            '<td>' + rspData[i].ysryshhj + '</td>' +
            '<td>' + rspData[i].ysryshxz + '</td>' +
            '<td>' + rspData[i].ysryshjs + '</td>' +
            '<td>' + rspData[i].ysryshjzz + '</td>' +
			'<td>' + rspData[i].zdsyhhj + '</td>' +
			'<td>' + rspData[i].zdsyhxz + '</td>' +
			'<td>' + rspData[i].zdsyhjs + '</td>' +
			'<td>' + rspData[i].zdsyhjzz + '</td>' +
			'<td>' + rspData[i].kpwshhj + '</td>' +
			'<td>' + rspData[i].kpwshxz + '</td>' +
			'<td>' + rspData[i].kpwshjs + '</td>' +
			'<td>' + rspData[i].kpwshjzz + '</td>' +
			'<td>' + rspData[i].spwshhj + '</td>' +
			'<td>' + rspData[i].spwshxz + '</td>' +
			'<td>' + rspData[i].spwshjs + '</td>' +
			'<td>' + rspData[i].spwshjzz + '</td>' +
			'<td>' + rspData[i].wbshhj + '</td>' +
			'<td>' + rspData[i].wbshxz + '</td>' +
			'<td>' + rspData[i].wbshjs + '</td>' +
			'<td>' + rspData[i].wbshjzz + '</td>' +
            '</tr>');
    }
}
function handleClick(val) {
    var element = layui.element; // 获取 layui 的 element 模块
    var tabId = 'tab' + (val + 1); // 根据 val 生成对应的 tab ID，例如 0 -> tab1, 1 -> tab2
    element.tabChange('my-handle', tabId); // 切换到对应的 tab
}
