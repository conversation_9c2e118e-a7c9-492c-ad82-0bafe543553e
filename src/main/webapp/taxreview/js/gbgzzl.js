var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
	// 		$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ gly: "王苗", jcgh: "201", gprw: "88", fxrw: "2", wbpj: "好", wbpjly: "--", jfhj: "88"},
				{ gly: "李苗", jcgh: "201", gprw: "88", fxrw: "2", wbpj: "好", wbpjly: "--", jfhj: "108"},
				{ gly: "张苗", jcgh: "201", gprw: "88", fxrw: "2", wbpj: "好", wbpjly: "--", jfhj: "208"},
				{ gly: "刘苗", jcgh: "201", gprw: "88", fxrw: "2", wbpj: "好", wbpjly: "--", jfhj: "78"},
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].gly + '</td>' +
            '<td>' + rspData[i].jcgh + '</td>' +
            '<td>' + rspData[i].gprw + '</td>' +
            '<td>' + rspData[i].fxrw + '</td>' +
            '<td>' + rspData[i].wbpj + '</td>' +
            '<td>' + rspData[i].wbpjly + '</td>' +
            '<td>' + rspData[i].jfhj + '</td>' +
            '</tr>');
    }

    var chartBox1 = echarts.init(document.getElementById('chartBox1'));

    // 准备积分统计数据
    var xAxisData = rspData.map(item => item.gly); // 横轴：管理员姓名
    var seriesData = rspData.map(item => parseFloat(item.jfhj)); // 纵轴：积分数量

    // 配置积分统计柱状图的选项
    var option = {
        title: {
            text: '积分统计图',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow' // 显示阴影指示器
            },
            formatter: '{b}<br/>{a}: {c} 分'
        },
        xAxis: {
            type: 'category',
            data: xAxisData, // 横轴显示管理员姓名
            axisLabel: {
                rotate: 45, // 旋转标签，避免重叠
                interval: 0 // 强制显示所有标签
            }
        },
        yAxis: {
            type: 'value',
            name: '积分数量 (分)',
            axisLabel: {
                formatter: '{value} 分' // 格式化纵轴单位
            }
        },
        series: [
            {
                name: '积分数量',
                type: 'bar',
                data: seriesData, // 数据为积分数量
                itemStyle: {
                    color: '#73C0DE', // 设置柱状图颜色
                    borderRadius: [8, 8, 0, 0] // 设置顶部圆角
                },
                barWidth: '50%' // 设置柱宽
            }
        ]
    };

    // 渲染积分统计柱状图
    chartBox1.setOption(option);
}