var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
	// 		$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ xm: "王苗", fjh: "201", ghsl: "88", zzfg: "对接征管科", zzfz: "征管组", qdsx: "--", qtfwsx: "--", lxdh: "--", ABj: "郭峰"},
				{ xm: "王苗", fjh: "201", ghsl: "88", zzfg: "对接征管科", zzfz: "征管组", qdsx: "--", qtfwsx: "--", lxdh: "--", ABj: "郭峰"},
				{ xm: "郭峰", fjh: "201", ghsl: "18", zzfg: "对接征管科", zzfz: "党建组", qdsx: "--", qtfwsx: "--", lxdh: "--", ABj: "郭峰"},
				{ xm: "李琛", fjh: "201", ghsl: "28", zzfg: "对接征管科", zzfz: "风控组", qdsx: "--", qtfwsx: "--", lxdh: "--", ABj: "郭峰"},
				{ xm: "王政", fjh: "201", ghsl: "58", zzfg: "对接征管科", zzfz: "财务科", qdsx: "--", qtfwsx: "--", lxdh: "--", ABj: "郭峰"},
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
    for (var i = 0; i < rspData.length; i++) {
        $("#dataList").append('<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td>' + rspData[i].xm + '</td>' +
            '<td>' + rspData[i].fjh + '</td>' +
            '<td>' + rspData[i].ghsl + '</td>' +
            '<td>' + rspData[i].zzfg + '</td>' +
            '<td>' + rspData[i].zzfz + '</td>' +
            '<td>' + rspData[i].qdsx + '</td>' +
            '<td>' + rspData[i].qtfwsx + '</td>' +
            '<td>' + rspData[i].lxdh + '</td>' +
            '<td>' + rspData[i].ABj + '</td>' +
            '</tr>');
    }

    var chartBox1 = echarts.init(document.getElementById('chartBox1'));

    // 统计 zzfz 分布
    var zzfzStats = rspData.reduce((acc, item) => {
        acc[item.zzfz] = (acc[item.zzfz] || 0) + 1;
        return acc;
    }, {});

    // 准备柱状图数据
    var barData = Object.keys(zzfzStats).map(key => ({
        name: key, // 组职责分名称
        value: zzfzStats[key] // 对应数量
    }));

    // 配置柱状图的选项
    var option = {
		title: {
			text: '组职责分布情况',
			left: 'center'
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow' // 显示阴影指示器
			}
		},
		xAxis: {
			type: 'category',
			data: Object.keys(zzfzStats), // 横轴显示组职责分名称
			axisLabel: {
				rotate: 45, // 旋转标签，避免重叠
				interval: 0 // 强制显示所有标签
			}
		},
		yAxis: {
			type: 'value',
			name: '数量'
		},
		series: [
			{
				name: '组职责分统计',
				type: 'bar',
				data: Object.values(zzfzStats), // 数据为组职责分的数量
				itemStyle: {
					color: '#73C0DE', // 设置柱状图颜色
					borderRadius: [8, 8, 0, 0] // 设置顶部圆角，顺序为左上、右上、右下、左下
				},
				barWidth: '50%' // 设置柱宽
			}
		]
	};

    // 渲染柱状图
    chartBox1.setOption(option);
}