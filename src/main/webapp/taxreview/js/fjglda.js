var url = location.origin+"/clf/fjglda";//请求前缀
var loginUser = null;//登录用户
var layer = null;
layui.use('layer',function(){
    layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
	elem: '#gdrq',
	type: 'date',
});
//分页
var currentPage = 1; // 当前页

function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
//初始进入页面
$(function () {
    search(currentPage);
    getloginuser();
})
//获取用户信息
function getloginuser(){
    $.ajax({
        type: "get",
        url: url + "/getLoginUser",
        async: true,
        headers: {
            'Authorization': 'Bearer ' + 'test1',
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (data) {
            loginUser = data;
        }
    });
}
//查询
function search(currentPage) {
    var index = layer.load(0);
	// 查询条件  id 根据需要自己定义
    var ywbh = $("#ywbh").val();
    var ywlx = $("#ywlx").val();
    var gdnd = $("#gdnd").val();
    var gdrq = $("#gdrq").val();
    var blry = $("#blry").val();
    $.ajax({
        type: "post",//请求类型 get或者post
        url: url + "/getList",//请求地址
        async: true,//异步或者同步，默认是异步
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        //timeout超时时间（毫秒）
        data: {
            ywbh: ywbh,
            ywlx: ywlx,
            gdnd: gdnd,
            gdrq: gdrq,
            blry: blry,
            pageNo: currentPage,
            pageSize: 10,
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (data) {
            layer.close(index);
            $("#dataList").html('');
            getMyMindMapPage(data.total, currentPage, "#pagination",10, search)
            let rspData = data.data;
            // let rspData = [{gdzt:"未归档",ywlx:"征纳互动",ywbh:"111",nsrmc:"222",nsrsbh: "333"}]
            console.log(rspData);
            for (var i = 0; i < rspData.length; i++) {
                $("#dataList").append('<tr>' +
                    '<td>' + (i + 1) + '</td>' +
                    '<td>' + rspData[i].ywbh + '</td>' +
                    '<td>' + rspData[i].ywlx + '</td>' +
                    '<td>' + rspData[i].gdzt + '</td>' +
                    '<td>' + rspData[i].gdnd + '</td>' +
                    '<td>' + rspData[i].gdrq + '</td>' +
                    '<td>' + rspData[i].gdfs + '</td>' +
                    '<td>' + rspData[i].blry + '</td>' +
                    '<td>' + rspData[i].fjsl + '</td>' +
                    '<td class="fixed-column">'+
					((rspData[i].gdzt == '已归档') ?
						'<span onclick="seeFile(1,event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '" data-ywlx="' + rspData[i].ywlx + '"  data-ywbh="' + rspData[i].ywbh + '"   data-nsrmc="' + rspData[i].nsrmc + '"   data-nsrsbh="' + rspData[i].nsrsbh + '">附件查看</span>' : '') +
					((rspData[i].gdzt != '已归档') ?
						'<span onclick="editFile(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '" data-ywlx="' + rspData[i].ywlx + '"  data-ywbh="' + rspData[i].ywbh + '"   data-nsrmc="' + rspData[i].nsrmc + '"   data-nsrsbh="' + rspData[i].nsrsbh + '">附件编辑</span>' : '') +
                    (rspData[i].gdzt != '已归档' ?
                        '<span onclick="czgdFn(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">归档</span>' : '') +
                    '</td></tr>');
            };
        },
        error: function () {
            layer.msg("请求失败");
            layer.close(index);
        },
    })
}
//新增
var fjList = [];
var activeIdsfj = [];
function showGDModal() {
    fjList = [];
    activeIdsfj = [];
    $("#ywbh1").val('');
    $("#nsrmc1").val('');
    $("#nsrsbh1").val('');
    $("#FJFileList").html('');
	$(".gdModal").show();
}
//上传
$('.uploadButton').change(function () {
    var fileInput = $('#fileInput')[0]; // 获取文件输入框
    var file = fileInput.files[0]; // 获取选中的第一个文件
    let wjzllx = $('input[name="ywlx1"]:checked').val();
    if (!file) {
        layer.msg("请先选择一个文件！");
        return;
    }
    const formData = new FormData();
    formData.append("file", file);
    formData.append("wjzllx", wjzllx);

    $.ajax({
        url: url + "/real/fj/upload",
        type: "POST",
        data: formData,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        processData: false,  // 不处理数据
        contentType: false,  // 不设置 Content-Type
        success: function (response) {
            if (response.code == 0) {
                layer.msg("上传成功");
                if(Array.isArray(response.data)){
                    response.data.forEach(item => {
                        fjList.push(item);
                        activeIdsfj.push(item.uuid)
                    });
                }else{
                    fjList.push(response.data);
                    activeIdsfj.push(response.data.uuid)
                }
                loadFjlist();
                $('#fileInput').val('');
            }
        },
        error: function () {
            layer.msg("上传失败，请重试！");
        }
    });
});
function loadFjlist() {
    $("#FJFileList").html('');
    for (var i = 0; i < fjList.length; i++) {
        $("#FJFileList").append(
            `<tr>
                <td>${ i+1 }</td>
                <td>${ fjList[i].wjm }</td>
                <td>${ fjList[i].wjdx }</td>
                <td>${ fjList[i].gdzt }</td>
                <td style='width: 220px;'>
                    <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ fjList[i].uuid }" data-type="${ fjList[i].wjlx }">预览</span>
                    <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ fjList[i].uuid }">下载</span>
                    <span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ fjList[i].uuid }">删除</span>
                </td>
            </tr>`
        )
    }
}
//删除附件
$(document).on('click', '.infoSC', function () {
    var uuid = $(this).data('id');
    var index = fjList.findIndex(function(item) {
        return item.uuid === uuid;
    });
    if (index !== -1) {
        fjList.splice(index, 1);
        activeIdsfj.splice(index, 1);
        $.ajax({
            url: url + "/real/fj/delete",
            type: "POST",
            data: {
                uuid: uuid
            },
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            dateType: 'json',  // 不设置 Content-Type
            success: function (response) {
                if (response.code == 0) {
                    layer.msg("操作成功");
                    loadFjlist();
                    $('#fileInput').val('');
                }
            },
            error: function () {
                layer.msg("操作失败，请重试！");
            }
        });
    }
});

$('.closeGdModal').on('click', function(e) {
    $(".gdModal").hide();
});
//新增暂存
function zcFn() {
    let ywlx = $('input[name="ywlx1"]:checked').val();
    if(ywlx == "" || ywlx == undefined){
        layer.msg("请输入业务类型");
        return false;
    }
    let ywbh = $("#ywbh1").val();
    if (ywbh == "") {
        layer.msg("请输入业务编号");
        return false;
    }
    let nsrmc = $("#nsrmc1").val();
    if (nsrmc == "") {
        layer.msg("请输入纳税人名称");
        return false;
    }
    let nsrsbh = $("#nsrsbh1").val();
    if (nsrsbh == "") {
        layer.msg("请输入纳税人识别号");
        return false;
    }
    let fjuuids = activeIdsfj.join(',');
    if (fjuuids == "") {
        layer.msg("请上传附件");
        return false;
    }
    loadZc(fjuuids,ywlx,ywbh,nsrmc,nsrsbh,"");
}
function loadZc(fjuuids,ywlx,ywbh,nsrmc,nsrsbh,bjActiveId) {
    var index = layer.load(0);
    $.ajax({
        type: "post",//请求类型 get或者post
        url: url + "/real/fj/zc",//请求地址
        async: true,//异步或者同步，默认是异步
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        //timeout超时时间（毫秒）
        data: {
            fjuuids: fjuuids,
            ywlx: ywlx,
            ywbh: ywbh,
            nsrmc: nsrmc,
            nsrsbh: nsrsbh,
            zbuuid: bjActiveId,
        },
        cache: false,
        dataType: "json",
        success: function (data) {
            layer.close(index);
            layer.msg("操作成功");
            $(".gdModal").hide();
            $(".fjEditModal").hide();
            search(currentPage)
        },
        error: function () {
            layer.msg("请求失败");
            layer.close(index);
        },
    })
}
//新增归档
function gdFn() {
    let ywlx = $('input[name="ywlx1"]:checked').val();
    if(ywlx == "" || ywlx == undefined){
        layer.msg("请输入业务类型");
        return false;
    }
    let ywbh = $("#ywbh1").val();
    if (ywbh == "") {
        layer.msg("请输入业务编号");
        return false;
    }
    let nsrmc = $("#nsrmc1").val();
    if (nsrmc == "") {
        layer.msg("请输入纳税人名称");
        return false;
    }
    let nsrsbh = $("#nsrsbh1").val();
    if (nsrsbh == "") {
        layer.msg("请输入纳税人识别号");
        return false;
    }
    let fjuuids = activeIdsfj.join(',');
    if (fjuuids == "") {
        layer.msg("请上传附件");
        return false;
    }
    loadGd(fjuuids,ywlx,ywbh,nsrmc,nsrsbh,"");
}
function loadGd(fjuuids,ywlx,ywbh,nsrmc,nsrsbh,bjActiveId) {
    var index = layer.load(0);
    $.ajax({
        type: "post",//请求类型 get或者post
        url: url + "/real/fj/gd",//请求地址
        async: true,//异步或者同步，默认是异步
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        //timeout超时时间（毫秒）
        data: {
            fjuuids: fjuuids,
            ywlx: ywlx,
            ywbh: ywbh,
            nsrmc: nsrmc,
            nsrsbh: nsrsbh,
            zbuuid: bjActiveId,
        },
        cache: false,
        dataType: "json",
        success: function (data) {
            layer.close(index);
            layer.msg("操作成功");
            $(".gdModal").hide();
            $(".fjEditModal").hide();
            search(currentPage)
        },
        error: function () {
            layer.msg("请求失败");
            layer.close(index);
        },
    })
}
//操作列归档
function czgdFn(evt) {
    let element = evt.target;
    let activeId = $(element).attr("data-id");
    //归档接口
    layer.confirm("是否要归档吗？",{btn:["确认","取消"],btn1:function (index,layero) {
        var index = layer.load(0);
        $.ajax({
            type: "post",//请求类型 get或者post
            url: url + "/real/fj/gd",//请求地址
            async: true,//异步或者同步，默认是异步
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            //timeout超时时间（毫秒）
            data: {
                zbuuid: activeId,
            },
            cache: false,
            dataType: "json",
            success: function (data) {
                layer.close(index);
                layer.msg("操作成功");
                search(currentPage);
            },
            error: function () {
                layer.msg("请求失败");
                layer.close(index);
            },
        })
    }})
}
//查看附件
let infoCurrentPage = 1;
var seeZbuuid = null;
function seeFile(infoCurrentPage,evt) {
    if(evt){
        let element = evt.target;
        seeZbuuid = $(element).attr("data-id");
        let activeYwlx = $(element).attr("data-ywlx");
        let activeYwbh = $(element).attr("data-ywbh");
        let activeNsrmc = $(element).attr("data-nsrmc");
        let activeNsrsbh = $(element).attr("data-nsrsbh");
        $("#attachmentFjType").val(activeYwlx);
        $("#ywbh2").val(activeYwbh);
        $("#nsrmc2").val(activeNsrmc);
        $("#nsrsbh2").val(activeNsrsbh);
        $(".fjSeeModal").show();
    }
	$.ajax({
        url: url + "/fileList",  // 接口
        type: "POST",
        data: {
            zbuuid: seeZbuuid,
			pageNo: infoCurrentPage,
            pageSize: 10,
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            let rspData = response.data;
            getMyMindMapPage(response.total, infoCurrentPage, "#paginationInfo",10, seeFile)
            if (response.code == 0) {
                $("#seeFJFileList").html("");
                if (rspData.length == 0) {
                    $("#seeFJFileList").html("<tr><td style='text-align: center;' colspan='10'>暂无数据</td></tr>");
                }else{
                    for (var i = 0; i < rspData.length; i++) {
                        $("#seeFJFileList").append(
                            `<tr>
                                <td>${ rspData[i].wjm }</td>
                                <td>${ rspData[i].wjdx }</td>
                                <td>${ rspData[i].gdzt }</td>
                                <td style='width: 220px;'>
                                    <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ rspData[i].uuid }" data-type="${ rspData[i].wjlx }">预览</span>
                                    <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ rspData[i].uuid }">下载</span>
                                </td>
                            </tr>`
                        )
                    }
                }
            }
        }
    })
}
$('.closeFjModal').on('click', function(e) {
    $(".fjSeeModal").hide();
});

//编辑附件
let bjCurrentPage = 1;
var bjfjList = [];
var bjactiveIdsfj = [];
var oldIds = [];
var bjActiveId = null;
function editFile(evt) {
    $('#fileInputBj').val('');
    bjActiveId = null;
    bjfjList = [];
    bjactiveIdsfj = [];
    oldIds = [];
    let element = evt.target;
    bjActiveId = $(element).attr("data-id");
    let activeYwlx = $(element).attr("data-ywlx");
    let activeYwbh = $(element).attr("data-ywbh");
    let activeNsrmc = $(element).attr("data-nsrmc");
    let activeNsrsbh = $(element).attr("data-nsrsbh");
    $('input[name="ywlx2"][value="' + activeYwlx + '"]').prop('checked', true);
    $("#ywbh3").val(activeYwbh);
    $("#nsrmc3").val(activeNsrmc);
    $("#nsrsbh3").val(activeNsrsbh);
    $(".fjEditModal").show();
	$.ajax({
        url: url + "/fileList",  // 接口
        type: "POST",
        data: {
            zbuuid: bjActiveId,
			pageNo: 1,
            pageSize: 10000,
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            let rspData = response.data;
            // getMyMindMapPage(response.total, bjCurrentPage, "#bjPagination",10, editFile)
            if (response.code == 0) {
                bjfjList = rspData;
                $("#bjFJFileList").html("");
                if (rspData.length == 0) {
                    $("#bjFJFileList").html("<tr><td style='text-align: center;' colspan='10'>暂无数据</td></tr>");
                }else{
                    for (var i = 0; i < rspData.length; i++) {
                        bjactiveIdsfj.push(rspData[i].uuid);
                        oldIds.push(rspData[i].uuid);
                        $("#bjFJFileList").append(
                            `<tr>
                                <td>${ i+1 }</td>
                                <td>${ rspData[i].wjm }</td>
                                <td>${ rspData[i].wjdx }</td>
                                <td>${ rspData[i].gdzt }</td>
                                <td style='width: 220px;'>
                                    <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ rspData[i].uuid }" data-type="${ rspData[i].wjlx }">预览</span>
                                    <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ rspData[i].uuid }">下载</span>
                                    <span class="infoSCBJ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ rspData[i].uuid }">删除</span>
                                </td>
                            </tr>`
                        )
                    }
                }
            }
        }
    })
}
//上传
$('.uploadButtonBj').change(function () {
    let fileInput = $('#fileInputBj')[0]; // 获取文件输入框
    let file = fileInput.files[0]; // 获取选中的第一个文件
    let wjzllx = $('input[name="ywlx2"]:checked').val();
    if (!file) {
        layer.msg("请先选择一个文件！");
        return;
    }
    const formData = new FormData();
    formData.append("file", file);
    formData.append("wjzllx", wjzllx);
    formData.append("zbuuid", bjActiveId);

    $.ajax({
        url: url + "/real/fj/upload",
        type: "POST",
        data: formData,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        processData: false,  // 不处理数据
        contentType: false,  // 不设置 Content-Type
        success: function (response) {
            if (response.code == 0) {
                layer.msg("上传成功");
                if(Array.isArray(response.data)){
                    response.data.forEach(item => {
                        bjfjList.push(item);
                        bjactiveIdsfj.push(item.uuid)
                    });
                }else{
                    bjfjList.push(response.data);
                    bjactiveIdsfj.push(response.data.uuid)
                }
                bjloadFjlist();
                $('#fileInputBj').val('');
            }
        },
        error: function () {
            layer.msg("上传失败，请重试！");
        }
    });
});
function bjloadFjlist() {
    console.log("新上传后bjfjList=============>",bjfjList);
    $("#bjFJFileList").html('');
    for (var i = 0; i < bjfjList.length; i++) {
        $("#bjFJFileList").append(
            `<tr>
                <td>${ i+1 }</td>
                <td>${ bjfjList[i].wjm }</td>
                <td>${ bjfjList[i].wjdx }</td>
                <td>${ bjfjList[i].gdzt }</td>
                <td style='width: 220px;'>
                    <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ bjfjList[i].uuid }" data-type="${ bjfjList[i].wjlx }">预览</span>
                    <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ bjfjList[i].uuid }">下载</span>
                    <span class="infoSCBJ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${ bjfjList[i].uuid }">删除</span>
                </td>
            </tr>`
        )
    }
}
//删除附件
$(document).on('click', '.infoSCBJ', function () {
    var uuid = $(this).data('id');
    var index = bjfjList.findIndex(function(item) {
        return item.uuid === uuid;
    });
    if (index !== -1) {
        bjfjList.splice(index, 1);
        bjactiveIdsfj.splice(index, 1);
        $.ajax({
            url: url + "/real/fj/delete",
            type: "POST",
            data: {
                uuid: uuid
            },
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            dateType: 'json',  // 不设置 Content-Type
            success: function (response) {
                if (response.code == 0) {
                    layer.msg("操作成功");
                    bjloadFjlist();
                    $('#fileInputBj').val('');
                }
            },
            error: function () {
                layer.msg("操作失败，请重试！");
            }
        });
    }
});
$('.closeBjModal').on('click', function(e) {
    $(".fjEditModal").hide();
});
//编辑暂存
function zcFn2() {
    let ywlx = $('input[name="ywlx2"]:checked').val();
    if(ywlx == "" || ywlx == undefined){
        layer.msg("请输入业务类型");
        return false;
    }
    let ywbh = $("#ywbh3").val();
    if (ywbh == "") {
        layer.msg("请输入业务编号");
        return false;
    }
    let nsrmc = $("#nsrmc3").val();
    if (nsrmc == "") {
        layer.msg("请输入纳税人名称");
        return false;
    }
    let nsrsbh = $("#nsrsbh3").val();
    if (nsrsbh == "") {
        layer.msg("请输入纳税人识别号");
        return false;
    }
    let fjuuids = bjactiveIdsfj.join(',');
    if (fjuuids == "") {
        layer.msg("请上传附件");
        return false;
    }
    let newArr = bjactiveIdsfj.filter(id => !oldIds.includes(id));
    let subFjuuids = newArr.join(',');
    loadZc(subFjuuids,ywlx,ywbh,nsrmc,nsrsbh,bjActiveId);
}
//编辑归档
function gdFn2() {
    let ywlx = $('input[name="ywlx2"]:checked').val();
    if(ywlx == "" || ywlx == undefined){
        layer.msg("请输入业务类型");
        return false;
    }
    let ywbh = $("#ywbh3").val();
    if (ywbh == "") {
        layer.msg("请输入业务编号");
        return false;
    }
    let nsrmc = $("#nsrmc3").val();
    if (nsrmc == "") {
        layer.msg("请输入纳税人名称");
        return false;
    }
    let nsrsbh = $("#nsrsbh3").val();
    if (nsrsbh == "") {
        layer.msg("请输入纳税人识别号");
        return false;
    }
    let fjuuids = bjactiveIdsfj.join(',');
    if (fjuuids == "") {
        layer.msg("请上传附件");
        return false;
    }
    let newArr = bjactiveIdsfj.filter(id => !oldIds.includes(id));
    let subFjuuids = newArr.join(',');
    loadGd(subFjuuids,ywlx,ywbh,nsrmc,nsrsbh,bjActiveId);
}

//预览
$(document).on('click', '.infoYL', function () {
    var id = $(this).data('id');
    var type =  $(this).data('type');
    var currurl =url+"/preview?uuid="+id;
    if(type == 'png' || type == 'jpg' || type == 'jpeg'){
        $("#previewPdf").attr("src", "");
        $("#previewPdf").css("display","none")
        $("#previewImg").attr("src", currurl);
        $("#previewImg").css("display","block");
        $('#previewImgModal').modal('show');
    }else if(type == 'pdf'){
        $("#previewImg").attr("src", "");
        $("#previewImg").css("display","none");
        $("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
        $("#previewPdf").css("display","block");
        $('#previewImgModal').modal('show');
    }else{
        layer.msg("当前格式暂不支持预览！");
    }
});
//下载
$(document).on('click', '.infoXZ', function () {
    var id = $(this).data('id');
    const aTag = document.createElement('a'); // 创建 a 标签
    aTag.href = url+"/download?uuid="+id;  // 设置图片的 URL
    document.body.appendChild(aTag); // 将 a 标签添加到页面中
    aTag.click(); // 触发点击事件，开始下载
    document.body.removeChild(aTag); // 下载后移除 a 标签
});


