var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
$(function () {
	search(currentPage);
})
// 获取 URL 中的查询参数
function getQueryParam(param) {
	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get(param);
}
// 页面加载时设置税所名称
document.addEventListener('DOMContentLoaded', function () {
	const name = getQueryParam('name'); // 获取 name 参数
	if (name) {
		const selectElement = document.getElementById('ssmc');
		const options = selectElement.options;
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === name) {
				options[i].selected = true; // 设置选中项
				break;
			}
		}
	}
});
//查询
function search(currentPage) {
	// var index = layer.load(0);
	// $.ajax({
	// 	type: "post",
	// 	url: url + "/qyqsgl/qyqsglqsqdb/page",
	// 	async: true,
	// 	headers: {
	// 		'Authorization': 'Bearer ' + 'test1',

	// 	},
	// 	data:
	// 		{
	// 			pageNo: currentPage,
	// 			pageSize: 10,
	// 		}
	// 	,
	// 	cache: false,
	// 	dataType: "json",
	// 	success: function (data) {
	// 		layer.close(index);
	// 		$("#dataList").html('');
			// let rspData = data.data;

			//临时
			let rspData = [
				{ xm: "王苗", csrq: "1990-11-22", zzmm: "团员", gznx: "2015", zj: "ky", byyx: "中国人民大学", zy: "会计学", xl: "硕士", jnzs: "无", jkzk: "健康状况" },
				{ xm: "李琳", csrq: "1990-11-22", zzmm: "团员", gznx: "2015", zj: "ky", byyx: "中国人民大学", zy: "会计学", xl: "本科", jnzs: "无", jkzk: "健康状况" },
				{ xm: "刘光", csrq: "1990-11-22", zzmm: "团员", gznx: "2015", zj: "ky", byyx: "中国人民大学", zy: "会计学", xl: "博士", jnzs: "无", jkzk: "健康状况" },
			];

			// activeData = rspData;
			// if (rspData && rspData.length > 0) {
			// 	$("#pagination").css("display", "block");
			// 	getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
	// 		} else {
	// 			$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
	// 			$("#pagination").css("display", "none");
	// 		}
	// 	},
	// 	error: function () {
	// 		layer.close(index);
	// 		layer.msg("请求失败");
	// 	},
	// })
}
function loadList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#dataList").append('<tr>' +
			'<td>' + (i + 1) + '</td>' +
			'<td>' + rspData[i].xm + '</td>' +
			'<td>' + rspData[i].csrq + '</td>' +
			'<td>' + rspData[i].zzmm + '</td>' +
			'<td>' + rspData[i].gznx + '</td>' +
			'<td>' + rspData[i].zj + '</td>' +
			'<td>' + rspData[i].byyx + '</td>' +
			'<td>' + rspData[i].zy + '</td>' +
			'<td>' + rspData[i].xl + '</td>' +
			'<td>' + rspData[i].jnzs + '</td>' +
			'<td>' + rspData[i].jkzk + '</td>' +
			'</tr>');
	};
	var chartBox1 = echarts.init(document.getElementById('chartBox1'));

    // 统计学历分布
    var xlStats = rspData.reduce((acc, item) => {
        acc[item.xl] = (acc[item.xl] || 0) + 1;
        return acc;
    }, {});

    // 准备饼图数据
    var pieData = Object.keys(xlStats).map(key => ({
        name: key, // 学历名称
        value: xlStats[key] // 对应数量
    }));

    // 配置饼图的选项
    var option = {
        title: {
            text: '人员学历分布情况',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            bottom: 'bottom',
            data: Object.keys(xlStats) // 图例显示学历名称
        },
        series: [
            {
                name: '学历统计',
                type: 'pie',
                radius: ['40%', '70%'],
                data: pieData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };

    // 渲染饼图
    chartBox1.setOption(option);
}