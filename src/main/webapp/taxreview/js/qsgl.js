var url = location.origin+"/clf";//请求前缀
// var url = "http://**********:9527/sfzz-prod-api";//请求前缀
var loginUser = null;//登录用户
//当前页数据
var activeData = [];
//金额格式化状态
var formatMoneyState = false;
var layer = null;
layui.use('layer', function () {
	layer = layui.layer;
});
var laydate = layui.laydate;
laydate.render({
	elem: '#nfIpt',
	type: 'year',
	change: function (value, date) {
		whzqFn()
	}
});
laydate.render({
	elem: '.yfIpt',
	type: 'date',
});
var currentPage = 1; // 当前页
var deqsCurrentPage = 1;
var zzcjCurrentPage = 1;
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
	$(paginationId).html('');
	var html = "";
	var lastPage;
	var nextPage;
	var showPage = 4;      //每次显示的页数
	var index;
	var x;               //定义后面页数固定

	// 计算总页数
	var totalPages = Math.ceil(totalCount / pageSize);

	html += "<ul class='pagination'>";
	html += "<li><span class='page-link' data-page='1'>首页</span></li>";

	lastPage = currentPages;
	if (lastPage <= 1) {
		lastPage = 1;
	} else {
		lastPage--;
	}

	html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

	if (totalPages <= showPage) {
		for (var i = 1; i <= totalPages; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	} else {
		index = currentPages + showPage;
		x = currentPages;
		if (index > totalPages) {
			index = totalPages + 1;
			x = index - showPage;
		}

		for (var i = x; i < index; i++) {
			if (i == currentPages) {
				html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			} else {
				html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
			}
		}
	}
	nextPage = currentPages;
	if (nextPage < totalPages) {
		nextPage++;
	} else if (nextPage == totalPages) {
		nextPage = totalPages;
	}

	html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
	html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
	html += "</ul>";
	$(paginationId).append(html);

	// 绑定事件
	$(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
		var page = $(this).data('page');
		callFun(page);
	});
}
//获取链接上参数
function getQueryString(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	var r = window.location.search.substr(1).match(reg);
	if (r != null) return unescape(r[2]);
	return null;
}

//获取用户信息
function getloginuser() {
	$.ajax({
		type: "get",
		url: url + "/getLoginUser",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',
		},
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (data) {
			loginUser = data;
		}
	});
}

//切换监听
var element = layui.element;
//初始进入页面
$(function () {
	var tabIndex = getQueryString("index");
	if (tabIndex !== null) {
		tabIndex = parseInt(tabIndex, 10);
		layui.element.tabChange('my-handle', 'tab' + tabIndex);
	} else {
		search(currentPage);
	}
	// getloginuser();
});
element.on('tab(my-handle)', function (data) {
	if (data.index == 0) {
		search(currentPage);
	} else if (data.index == 1) {
		qstjsearch()
	} else if (data.index == 2) {
		deqssearch(deqsCurrentPage)
	} else if (data.index == 3) {
		zzcjsearch(zzcjCurrentPage)
	}
});
//tab-欠税清单-start
//查询
function search(currentPage) {
	console.log("0");
	var index = layer.load(0);
	var nsrmc = $("#nsrmc").val();
	var shxydm = $("#shxydm").val();
	var qslb = $("#qslb").val();
	var sfhdlhd = $("#sfhdlhd").val();
	var gly = $("#gly").val();
	var zgswks = $("#zgswks").val();
	activeData = [];
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nsrmc: nsrmc,
				shxydm: shxydm,
				qslb: qslb,
				sfhdlhd: sfhdlhd,
				ssgly: gly,
				zgswks: zgswks,
				pageNo: currentPage,
				pageSize: 10,
			}
		,
		cache: false,//默认: true , 为false不读取缓存
		dataType: "json",
		success: function (data) {
			layer.close(index);
			$("#dataList").html('');
			let rspData = data.data;
			activeData = rspData;
			if (rspData && rspData.length > 0) {
				$("#pagination").css("display", "block");
				getMyMindMapPage(data.total, currentPage, "#pagination", 10, search)
				loadList(rspData);
			} else {
				$("#dataList").html('<tr><td colspan="30">暂无数据</td></tr>')
				$("#pagination").css("display", "none");
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function loadList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#dataList").append('<tr>' +
			'<td class="fixed-left-column"><input class="tableCheck" data-id="' + rspData[i].uuid + '"  type="checkbox"></input></td>' +
			'<td>' + (i + 1) + '</td>' +
			'<td>' + rspData[i].tjrq + '</td>' +
			'<td>' + rspData[i].sjly + '</td>' +
			'<td>' + rspData[i].shxydm + '</td>' +
			'<td>' + rspData[i].nsrmc + '</td>' +
			'<td>' + rspData[i].nsrxydj + '</td>' +
			'<td>' + rspData[i].nsrzt + '</td>' +
			'<td>' + rspData[i].fddbrxm + '</td>' +
			'<td>' + rspData[i].fddbrsfzhm + '</td>' +
			'<td>' + rspData[i].yzfsrq + '</td>' +
			'<td>' + rspData[i].qslb + '</td>' +
			'<td>' + rspData[i].jyqk + '</td>' +
			'<td>' + rspData[i].zgswks + '</td>' +
			'<td>' + rspData[i].jdxz + '</td>' +
			'<td>' + rspData[i].ssgly + '</td>' +
			'<td>' + rspData[i].qsye + '</td>' +
			'<td>' + rspData[i].wncq + '</td>' +
			'<td>' + rspData[i].bnxq + '</td>' +
			'<td>' + rspData[i].qsfxdj + '</td>' +
			'<td>' + rspData[i].qcnldf + '</td>' +
			'<td>' + rspData[i].djje + '</td>' +
			'<td>' + rspData[i].zzsldje + '</td>' +
			'<td>' + rspData[i].skcllx + '</td>' +
			// '<td>' + rspData[i].kkjssfqr + '</td>' +
			'<td>' + rspData[i].sfxq + '</td>' +
			'<td>' + rspData[i].sfjc + '</td>' +
			'<td>' + rspData[i].sfhdlhd + '</td>' +
			'<td>' + rspData[i].cjcs + '</td>' +
			'<td>' + rspData[i].cjjg + '</td>' +
			'<td>' + rspData[i].cjzt + '</td>' +
			'<td>' + rspData[i].bz + '</td>' +
			'<td class="fixed-column">' +
			'<span onclick="zzjg(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">追征结果</span>' +
			'<span onclick="zzcs(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">追征措施</span>' +
			'<span onclick="qsmx(1,event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">欠税明细</span>' +
			'<span onclick="bj(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">编辑</span>' +
			'</td></tr>');
	};
}
//导出
function dcFn() {
	var nsrmc = $("#nsrmc").val();
	var shxydm = $("#shxydm").val();
	var qslb = $("#qslb").val();
	var sfhdlhd = $("#sfhdlhd").val();
	var gly = $("#gly").val();
	var zgswks = $("#zgswks").val();
	var jsonData = JSON.stringify({
		nsrmc: nsrmc,
		shxydm: shxydm,
		qslb: qslb,
		sfhdlhd: sfhdlhd,
		ssgly: gly,
		zgswks: zgswks
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsglqsqdb/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//维护征期
function whzqFn(type) {
	$(".whzqModal").show();
	var index = layer.load(0);
	if (type == 1) {
		//获取当前年份
		var date = new Date();
		var year = date.getFullYear();
		$("#nfIpt").val(year);
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/zqwh/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nd: $("#nfIpt").val(),
			}
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			let rspData = res.data;
			for (let i = 0; i < rspData.length; i++) {
				rspData[i].zzrq ? $("#zq" + i).val(rspData[i].zzrq.split(" ")[0]) : "";
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function whzqTrueFn() {
	// let rqsData = [];
	// for (let i = 0; i < 12; i++) {
	// 	rqsData.push({
	// 		nd: $("#nfIpt").val(),
	// 		qqrq: $("#zq" + i).val() ? ($("#zq" + i).val().split('-')[0] + "-" + $("#zq" + i).val().split('-')[1] + "-01" + " 00:00:00") : "",
	// 		zzrq: $("#zq" + i).val() ? $("#zq" + i).val() + " 23:59:59" : "",
	// 	})
	// }
	var index = layer.load(0);
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/zqwh/save",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nd: $("#nfIpt").val(),
				zzrq1: $("#zq0").val() ? $("#zq0").val() + " 23:59:59" : "",
				zzrq2: $("#zq1").val() ? $("#zq1").val() + " 23:59:59" : "",
				zzrq3: $("#zq2").val() ? $("#zq2").val() + " 23:59:59" : "",
				zzrq4: $("#zq3").val() ? $("#zq3").val() + " 23:59:59" : "",
				zzrq5: $("#zq4").val() ? $("#zq4").val() + " 23:59:59" : "",
				zzrq6: $("#zq5").val() ? $("#zq5").val() + " 23:59:59" : "",
				zzrq7: $("#zq6").val() ? $("#zq6").val() + " 23:59:59" : "",
				zzrq8: $("#zq7").val() ? $("#zq7").val() + " 23:59:59" : "",
				zzrq9: $("#zq8").val() ? $("#zq8").val() + " 23:59:59" : "",
				zzrq10: $("#zq9").val() ? $("#zq9").val() + " 23:59:59" : "",
				zzrq11: $("#zq10").val() ? $("#zq10").val() + " 23:59:59" : "",
				zzrq12: $("#zq11").val() ? $("#zq11").val() + " 23:59:59" : ""
			}
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				layer.msg("操作成功");
				$(".whzqModal").hide();
			} else {
				layer.msg("请求失败：" + res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})

}
//金额格式化
function jegshFn() {
	let index = layer.load(0);
	$("#dataList").html('');
	if (formatMoneyState) {
		formatMoneyState = false;
		for (var i = 0; i < activeData.length; i++) {
			//将元转换成万元
			activeData[i].qsye = ((activeData[i].qsye).split('万元')[0] * 10000).toFixed(2);
			activeData[i].wncq = ((activeData[i].wncq).split('万元')[0] * 10000).toFixed(2);
			activeData[i].bnxq = ((activeData[i].bnxq).split('万元')[0] * 10000).toFixed(2);
			activeData[i].djje = ((activeData[i].djje).split('万元')[0] * 10000).toFixed(2);
			activeData[i].zzsldje = ((activeData[i].zzsldje).split('万元')[0] * 10000).toFixed(2);
		}
		setTimeout(function () {
			loadList(activeData);
			layer.close(index)
		}, 1000)
	} else {
		formatMoneyState = true;
		for (var i = 0; i < activeData.length; i++) {
			//将万元转换成元
			activeData[i].qsye = (activeData[i].qsye / 10000).toFixed(2) + '万元';
			activeData[i].wncq = (activeData[i].wncq / 10000).toFixed(2) + '万元';
			activeData[i].bnxq = (activeData[i].bnxq / 10000).toFixed(2) + '万元';
			activeData[i].djje = (activeData[i].djje / 10000).toFixed(2) + '万元';
			activeData[i].zzsldje = (activeData[i].zzsldje / 10000).toFixed(2) + '万元';
		}
		setTimeout(function () {
			loadList(activeData);
			layer.close(index)
		}, 1000)
	}
}
//扣款解锁
function kkjs() {
	layer.confirm("确认要扣款解锁吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglqsqdb/kkjs",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data: {},
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						search(currentPage);
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//追征任务下发
var activeIds = [];
//全选
function checkAll() {
	var tableck = document.getElementById("tableqs").checked;
	var checkboxs = $(".tableCheck");
	activeIds = [];
	for (var i = 0; i < checkboxs.length; i++) {
		var checkbox = checkboxs[i];
		if (tableck) {
			checkbox.checked = true;
			activeIds.push($(checkbox).data('id'));
		} else {
			checkbox.checked = false;
			activeIds = [];
		}
	}
}
$(document).on('click', '#dataList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.tableCheck');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.tableCheck', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		activeIds.push($(this).data('id'));
		if ($("#dataList tr").length == activeIds.length) {
			document.getElementById("tableqs").checked = true;
		} else {
			document.getElementById("tableqs").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < activeIds.length; i++) {
			if (activeIds[i] == $(this).data('id')) {
				activeIds.splice(i, 1);
			}
		}
		document.getElementById("tableqs").checked = false;
	}
});
function zzrwxfFn() {
	if (activeIds.length == 0) {
		layer.msg("请选择数据");
		return;
	}
	let rqsData = {
		bz: activeIds.join(',')
	}
	layer.confirm("确认要下发追征任务吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglqsqdb/zzrwxf",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData
				,
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						search(currentPage);
						activeIds = [];
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//追征结果
function zzjg(evt) {
	$(".zzjgModal").show();
	$("#zzjgList").html('');
	var index = layer.load(0);
	let element = evt.target;
	let rowId = $(element).attr("data-id");
	let rqsData = {
		zbuuid: rowId
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/zzjg",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if (rspData && rspData.length > 0) {
					let kkjsqdhtml = '';
					for (var i = 0; i < rspData.length; i++) {
						kkjsqdhtml += `
							<tr>
								<td>${i + 1}</td>
								<td>${rspData[i].tjrq}</td>
								<td>${rspData[i].nsrmc}</td>
								<td>${rspData[i].qslb}</td>
								<td>${rspData[i].zgswks}</td>
								<td>${rspData[i].ssgly}</td>
								<td>${rspData[i].xfrq}</td>
								<td>${rspData[i].zzrwzt}</td>
								<td>${rspData[i].zzry}</td>
								<td>${rspData[i].zzrwwcrq}</td>
								<td>${rspData[i].zzcs}</td>
								<td>${rspData[i].zzjg}</td>
							</tr>
						`
					}
					$("#zzjgList").append(kkjsqdhtml);
				} else {
					$("#zzjgList").html('<tr><td colspan="12" style="text-align: center;">暂无数据</td></tr>');
				}

			} else {
				layer.msg("请求失败：" + res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//追征措施
function zzcs(evt) {
	$(".zzcsModal").show();
	$("#zzcsList").html('');
	var index = layer.load(0);
	let element = evt.target;
	let rowId = $(element).attr("data-id");
	let rqsData = {
		zbuuid: rowId
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/zzcs",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if (rspData && rspData.length > 0) {
					let kkjsqdhtml = '';
					for (var i = 0; i < rspData.length; i++) {
						kkjsqdhtml += `
							<tr>
								<td>${i + 1}</td>
								<td>${rspData[i].tjrq}</td>
								<td>${rspData[i].nsrmc}</td>
								<td>${rspData[i].qslb}</td>
								<td>${rspData[i].zgswks}</td>
								<td>${rspData[i].ssgly}</td>
								<td>${rspData[i].xfrq}</td>
								<td>${rspData[i].zzrwzt}</td>
								<td>${rspData[i].xfr}</td>
								<td>${rspData[i].zzrwwcrq}</td>
								<td>${rspData[i].zzcs}</td>
								<td>${rspData[i].zzjg}</td>
							</tr>
						`
					}
					$("#zzcsList").append(kkjsqdhtml);
				} else {
					$("#zzcsList").html('<tr><td colspan="12" style="text-align: center;">暂无数据</td></tr>');
				}

			} else {
				layer.msg("请求失败：" + res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//欠税明细
var qsmxcurrentPage = 1;
function qsmx(qsmxcurrentPage,evt) {
	$(".qsmxModal").show();
	$("#qsmxList").html('');
	var index = layer.load(0);
	let element = evt.target;
	let rowId = $(element).attr("data-id");
	let rqsData = {
		zbuuid: rowId,
		pageNo: qsmxcurrentPage,
		pageSize: 10
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdmxb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if (rspData && rspData.length > 0) {
					$("#qsmxpagination").css("display", "block");
					getMyMindMapPage(res.total, qsmxcurrentPage, "#qsmxpagination", 10, qsmx)
					let qsmxhtml = '';
					for (var i = 0; i < rspData.length; i++) {
						qsmxhtml += `
							<tr>
								<td>${i + 1}</td>
								<td>${rspData[i].nsrmc}</td>
								<td>${rspData[i].yzfsrq}</td>
								<td>${rspData[i].zsxm}</td>
								<td>${rspData[i].zspm}</td>
								<td>${rspData[i].xmmc}</td>
								<td>${rspData[i].ybtse}</td>
								<td>${rspData[i].skssqq}</td>
								<td>${rspData[i].skssqz}</td>
								<td>${rspData[i].jkqx}</td>
							</tr>
						`
					}
					$("#qsmxList").append(qsmxhtml);
				} else {
					$("#qsmxList").html('<tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>');
					$("#qsmxpagination").css("display", "none");
				}

			} else {
				layer.msg("请求失败：" + res.msg);
				layer.close(index);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//编辑
var editId = null;
function bj(evt) {
	$(".bjModal").show();
	let element = evt.target;
	editId = $(element).attr("data-id");
}
function qsqdBJ(evt) {
	var index = layer.load(0);
	let jyqk = $("#jyqk").val();
	let bz = $("#bjbz").val();
	let rqsData = {
		uuid: editId,
		jyqk: jyqk,
		bz: bz
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdb/update",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				layer.msg("操作成功");
				$(".bjModal").hide();
				search(currentPage);
			} else {
				layer.msg("请求失败：" + res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//tab-欠税清单-end




//tab-欠税统计-start
function qstjsearch() {
	console.log("1");
	var qstjindex = layer.load(0);
	let rqsData = {}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglqsqdb/qstj",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData
		,
		cache: false,
		dataType: "json",
		success: function (data) {
			layer.close(qstjindex);
			$("#qstjdataList").html('');
			let rspData = data.data;
			console.log(rspData);
			for (var i = 0; i < rspData.length; i++) {
				$("#qstjdataList").append('<tr>' +
					'<td>' + rspData[i].fl + '</td>' +
					'<td>' + rspData[i].qsze + '</td>' +
					'<td>' + rspData[i].cqze + '</td>' +
					'<td>' + rspData[i].xqze + '</td>' +
					'</tr>');
			};
		},
		error: function () {
			layer.msg("请求失败");
			layer.close(qstjindex);
		},
	})
}
//导出
function qstjdcFn() {
	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsglqsqdb/qstjdcFn";  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//tab-欠税统计-end





//tab-大额欠税阻止出境-start
function deqssearch(deqsCurrentPage) {
	let nsrmc = $("#deqsnsrmc").val();
	let nsrszt = $("#nsrszt").val();
	console.log("2");
	$("#deqsdataList").html('');
	var deqsindex = layer.load(0);
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsgldeqszzcjsqb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				nsrmc: nsrmc,
				nsrszt: nsrszt,
				pageNo: deqsCurrentPage,
				pageSize: 10,
			}
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(deqsindex);
			let rspData = res.data;
			if (rspData && rspData.length > 0) {
				$("#pagination1").css("display", "block");
				getMyMindMapPage(res.total, deqsCurrentPage, "#pagination1", 10, deqssearch);
				loadDeqsList(rspData)
			} else {
				$("#deqsdataList").html('<tr><td colspan="20" style="text-align: center;">暂无数据</td></tr>');
				$("#pagination1").css("display", "none");
			}

		},
		error: function () {
			layer.msg("请求失败");
		},
	})
}
function loadDeqsList(rspData) {
	for (var i = 0; i < rspData.length; i++) {
		$("#deqsdataList").append('<tr>' +
			'<td class="fixed-left-column"><input class="deqstableCheck" data-id="' + rspData[i].uuid + '" data-zzcjzt="' + rspData[i].zzcjzt + '"  type="checkbox"></input></td>' +
			'<td>' + (i + 1) + '</td>' +
			'<td>' + rspData[i].shxydm + '</td>' +
			'<td>' + rspData[i].nsrmc + '</td>' +
			'<td>' + rspData[i].nsrzt + '</td>' +
			'<td>' + rspData[i].fddbrxm + '</td>' +
			'<td>' + rspData[i].fddbrsfzhm + '</td>' +
			'<td>' + rspData[i].jyqk + '</td>' +
			'<td>' + rspData[i].zgswks + '</td>' +
			'<td>' + rspData[i].jdxz + '</td>' +
			'<td>' + rspData[i].ssgly + '</td>' +
			'<td>' + rspData[i].qsye + '</td>' +
			'<td>' + rspData[i].wncq + '</td>' +
			'<td>' + rspData[i].bnxq + '</td>' +
			'<td>' + rspData[i].qsfxdj + '</td>' +
			'<td>' + rspData[i].qcnldf + '</td>' +
			'<td>' + rspData[i].djje + '</td>' +
			'<td>' + rspData[i].zzsldje + '</td>' +
			'<td>' + rspData[i].sfhdlhd + '</td>' +
			'<td>' + rspData[i].cjzt + '</td>' +
			'<td>' + rspData[i].cjcs + '</td>' +
			'<td>' + rspData[i].cjjg + '</td>' +
			'<td>' + rspData[i].gdzt + '</td>' +
			'<td>' + rspData[i].zzcjzt + '</td>' +
			'<td>' + rspData[i].bz + '</td>' +
			'<td class="fixed-column">' +
			'<span onclick="deqszzjg(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].zbuuid + '">追征结果</span>' +
			(rspData[i].zzcjzt != '已阻止' ? '<span onclick="deqszzcj(event,1)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '"  data-zzcjsbjfkuuid="' + rspData[i].zzcjsbjfkuuid + '">阻止出境</span>' : '') +
			'</td></tr>');
	};
}

//主列表多选
var deqszzcjIds = [];
var deqszzcjDatas = [];
function checkDeqsAll() {
	var tableck = document.getElementById("tabledeqs").checked;
	var checkboxs = $(".deqstableCheck");
	deqszzcjIds = [];
	deqszzcjDatas = [];
	for (var i = 0; i < checkboxs.length; i++) {
		var checkbox = checkboxs[i];
		if (tableck) {
			checkbox.checked = true;
			deqszzcjIds.push($(checkbox).data('id'));
			deqszzcjDatas.push($(checkbox).data('zzcjzt'))
		} else {
			checkbox.checked = false;
			deqszzcjIds = [];
			deqszzcjDatas = [];
		}
	}
}
$(document).on('click', '#deqsdataList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.deqstableCheck');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.deqstableCheck', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		deqszzcjIds.push($(this).data('id'));
		deqszzcjDatas.push($(this).data('zzcjzt'));
		if ($("#deqsdataList tr").length == deqszzcjIds.length) {
			document.getElementById("tabledeqs").checked = true;
		} else {
			document.getElementById("tabledeqs").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < deqszzcjIds.length; i++) {
			if (deqszzcjIds[i] == $(this).data('id')) {
				deqszzcjIds.splice(i, 1);
				deqszzcjDatas.splice(i, 1);
			}
		}
		document.getElementById("tabledeqs").checked = false;
	}
});


//导出
function dedcFn() {
	let nsrmc = $("#deqsnsrmc").val();
	let nsrszt = $("#nsrszt").val();
	var jsonData = JSON.stringify({
		nsrmc: nsrmc,
		nsrszt: nsrszt
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsgldeqszzcjsqb/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//归档
function degdFn() {
	console.log(deqszzcjIds)
	if (deqszzcjIds.length == 0) {
		layer.msg("请选择数据");
		return;
	}
	for(var i=0;i<deqszzcjDatas.length;i++){
		if(deqszzcjDatas[i]!="已阻止"){
			layer.msg("请选择已阻止出境的数据");
			return;
		}
	}
	let rqsData = {
		bz: deqszzcjIds.join(',')
	}
	layer.confirm("确认要归档吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsgldeqszzcjsqb/guidang",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData
				,
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						deqszzcjIds = [];
						deqssearch(deqsCurrentPage)
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//阻止出境企业名单
var zzqyIds = null;
var zzqyCurrentPage = 1;
function zzcjmdFn() {
	zzqyIds = [];
	$(".dezzcjmdModal").show();
	loadDezzqyList(zzqyCurrentPage)
}
function loadDezzqyList(zzqyCurrentPage) {
	$("#dezzqyList").html('');
	var deqsindex = layer.load(0);
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/zzcjqymd",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{
				pageNo: zzqyCurrentPage,
				pageSize: 10,
			}
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(deqsindex);
			let rspData = res.data;
			if (rspData && rspData.length > 0) {
				$("#paginationZZQY").css("display", "block")
				getMyMindMapPage(res.total, zzqyCurrentPage, "#paginationZZQY", 10, zzcjmdFn)
				for (var i = 0; i < rspData.length; i++) {
					$("#dezzqyList").append('<tr>' +
						'<td class="fixed-left-column"><input class="tableCheckDE" data-id="' + rspData[i].uuid + '"  type="checkbox"></input></td>' +
						'<td>' + (i + 1) + '</td>' +
						'<td>' + rspData[i].tjrq + '</td>' +
						'<td>' + rspData[i].nsrmc + '</td>' +
						'<td>' + rspData[i].nsrzt + '</td>' +
						'<td>' + rspData[i].qslb + '</td>' +
						'<td>' + rspData[i].zgswks + '</td>' +
						'<td>' + rspData[i].ssgly + '</td>' +
						'<td>' + rspData[i].zzrwzt + '</td>' +
						'<td>' + rspData[i].zzrwwcrq + '</td>' +
						'<td>' + rspData[i].zzry + '</td>' +
						'<td>' + rspData[i].zzcs + '</td>' +
						'<td>' + rspData[i].zzjg + '</td>' +
						'<td>' + rspData[i].sfqrzzcj + '</td>' +
						'</tr>');
				};
			} else {
				$("#dezzqyList").append('<tr><td colspan="15" style="text-align: center;">暂无数据</td></tr>');
				$("#paginationZZQY").css("display", "none")
			}
		},
		error: function () {
			layer.msg("请求失败");
		},
	})

}
function checkAllDE() {
	var tableck = document.getElementById("dezzcjtable").checked;
	var checkboxs = $(".tableCheckDE");
	zzqyIds = [];
	for (var i = 0; i < checkboxs.length; i++) {
		var checkbox = checkboxs[i];
		if (tableck) {
			checkbox.checked = true;
			zzqyIds.push($(checkbox).data('id'));
		} else {
			checkbox.checked = false;
			zzqyIds = [];
		}
	}
}
$(document).on('click', '#dezzqyList tr', function (event) {
	var target = event.target;
	if ($(target).closest(".fixed-column").length > 0) {
		return;
	}
	if ($(target).closest(".fixed-left-column").length > 0) {
		return;
	}
	// 获取当前行中的复选框
	var checkbox = $(this).find('.tableCheckDE');

	// 判断是否点击了复选框本身，如果是，则不处理
	if ($(event.target).is(checkbox)) {
		return;
	}

	// 切换复选框状态
	if (checkbox.is(':checked')) {
		checkbox.prop('checked', false); // 取消选中
	} else {
		checkbox.prop('checked', true); // 选中
	}

	// 触发复选框的 change 事件（如果需要）
	checkbox.trigger('change');
});
//行选中
$(document).on('change', '.tableCheckDE', function () {
	if ($(this).is(':checked')) {
		$(this).parents('tr').addClass('selected');
		zzqyIds.push($(this).data('id'));
		if ($("#dezzqyList tr").length == zzqyIds.length) {
			document.getElementById("dezzcjtable").checked = true;
		} else {
			document.getElementById("dezzcjtable").checked = false;
		}
	} else {
		$(this).parents('tr').removeClass('selected');
		for (var i = 0; i < zzqyIds.length; i++) {
			if (zzqyIds[i] == $(this).data('id')) {
				zzqyIds.splice(i, 1);
			}
		}
		document.getElementById("dezzcjtable").checked = false;
	}
});
//确认阻止
function deqrzzFn() {
	if (zzqyIds.length == 0) {
		layer.msg("请选择数据");
		return;
	}
	let rqsData = {
		bz: zzqyIds.join(',')
	}
	layer.confirm("确认要阻止吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/qrzzcj",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData
				,
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						zzqyIds = [];
						loadDezzqyList(zzqyCurrentPage)
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//取消阻止
function deqxzzFn() {
	if (zzqyIds.length == 0) {
		layer.msg("请选择数据");
		return;
	}
	let rqsData = {
		bz: zzqyIds.join(',')
	}
	layer.confirm("确认要取消阻止吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				type: "post",
				url: url + "/qyqsgl/qyqsglzzrwqdzzcjsbjfkb/qxzzcj",
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData
				,
				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(index);
					if (res.code == 0) {
						layer.msg("操作成功");
						zzqyIds = [];
						loadDezzqyList(zzqyCurrentPage)
					} else {
						layer.msg("请求失败：" + res.msg);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//追征结果
function deqszzjg(evt) {
	$(".dezzjgModal").show();
	$("#dezzjgList").html('');
	var index = layer.load(0);
	let element = evt.target;
	let rowId = $(element).attr("data-id");
	let rqsData = {
		zbuuid: rowId
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglzzrwqdb/zzjg",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				if (rspData && rspData.length > 0) {
					let kkjsqdhtml = '';
					for (var i = 0; i < rspData.length; i++) {
						kkjsqdhtml += `
							<tr>
								<td>${i + 1}</td>
								<td>${rspData[i].tjrq}</td>
								<td>${rspData[i].nsrmc}</td>
								<td>${rspData[i].qslb}</td>
								<td>${rspData[i].zgswks}</td>
								<td>${rspData[i].ssgly}</td>
								<td>${rspData[i].xfrq}</td>
								<td>${rspData[i].zzrwzt}</td>
								<td>${rspData[i].zzry}</td>
								<td>${rspData[i].zzrwwcrq}</td>
								<td>${rspData[i].zzcs}</td>
								<td>${rspData[i].zzjg}</td>
							</tr>
						`
					}
					$("#dezzjgList").append(kkjsqdhtml);
				} else {
					$("#dezzjgList").html('<tr><td colspan="12" style="text-align: center;">暂无数据</td></tr>');
				}

			} else {
				layer.msg("请求失败：" + res.msg);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
//阻止出境  //解除阻止出境
var zzUuid = null;
var zzZbuuid = null;
var zzcjType = null;
var zzcjfk_Fj_Zbuuid = null;
function deqszzcj(evt, type) {
	let element = evt.target;
	zzUuid = $(element).attr("data-id");
	zzZbuuid = $(element).attr("data-zbuuid");
	if (type == 1) {
		$("#zzcjTit").text("阻止出境");
		zzcjfk_Fj_Zbuuid = $(element).attr("data-zzcjsbjfkuuid");
	} else {
		$("#zzcjTit").text("解除阻止出境");
		zzcjfk_Fj_Zbuuid = $(element).attr("data-id");
	}
	zzcjType = type;
	$(".dezzcjModal").show();
	loadFjlist();
}
function loadFjlist() {
	var index = layer.load(0);
	$("#FJFileList").html('');
	let rqsData = {
		zbuuid: zzcjfk_Fj_Zbuuid
	}
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsglfjxxb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
		rqsData,

		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(index);
			if (res.code == 0) {
				let rspData = res.data;
				let fjhtml = '';
				if (rspData && rspData.length > 0) {
					for (var i = 0; i < rspData.length; i++) {
						fjhtml += `
							<tr>
								<td>${i + 1}</td>
								<td>${rspData[i].wjm}</td>
								<td>${rspData[i].wjdx}</td>
								<td>
									<span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}" data-type="${rspData[i].wjlx}">预览</span>
									<span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">下载</span>
									<span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">删除</span>
								</td>
							</tr>
						`
					}
					$("#FJFileList").append(fjhtml);
				} else {
					$("#FJFileList").html('<tr><td colspan="4">暂无数据</td></tr>');
				}
			} else {
				layer.msg("请求失败：" + res.msg);
				layer.close(index);
			}
		},
		error: function () {
			layer.close(index);
			layer.msg("请求失败");
		},
	})
}
function tsgaFn() {
	let zzcjryxm = $("#zzcjryxm").val();
	let lxdh = $("#lxdh").val();
	let sfzh = $("#sfzh").val();
	let tsrq = $("#tsrq").val();
	let swgly = $("#swgly").val();
	let glylxfs = $("#glylxfs").val();
	let bz = $("#debz").val();
	if($("#FJFileList").html() == '<tr><td colspan="4">暂无数据</td></tr>'){
		layer.msg("请上传文件");
		return;
	}
	if(zzcjryxm == ""){
		layer.msg("请输入组织出境人员姓名");
		return;
	}
	if(lxdh == ""){
		layer.msg("请输入联系电话");
		return;
	}
	if(sfzh == ""){
		layer.msg("请输入身份证号码");
		return;
	}
	if(tsrq == ""){
		layer.msg("请选择推送日期");
		return;
	}
	if(swgly == ""){
		layer.msg("请输入税务管理员");
		return;
	}
	if(glylxfs == ""){
		layer.msg("请输入管理员联系方式");
		return;
	}
	layer.confirm("确认要推送公安吗？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			let zzcjryxm = $("#zzcjryxm").val();
			let lxdh = $("#lxdh").val();
			let sfzh = $("#sfzh").val();
			let tsrq = $("#tsrq").val();
			let swgly = $("#swgly").val();
			let glylxfs = $("#glylxfs").val();
			let bz = $("#debz").val();
			let rqsData = {
				uuid: zzUuid,
				zzcjryxm: zzcjryxm,
				lxdh: lxdh,
				sfzh: sfzh,
				tsrq: tsrq,
				swgly: swgly,
				glylxfs: glylxfs,
				bz: bz,
			}
			let indexs = layer.load(0);
			let tsgaUrl = zzcjType == 1 ? '/qyqsgl/qyqsgldeqszzcjsqb/zzcjtsga' : '/qyqsgl/qyqsgljcdeqszzcjsqb/jczzcjtsga';
			$.ajax({
				type: "post",
				url: url + tsgaUrl,
				async: true,
				headers: {
					'Authorization': 'Bearer ' + 'test1',

				},
				data:
				rqsData,

				cache: false,
				dataType: "json",
				success: function (res) {
					layer.close(indexs);
					if (res.code == 0) {
						layer.msg("操作成功");
						$(".dezzcjModal").hide();
						if(zzcjType == 1) {
							deqssearch(deqsCurrentPage)
						}else {
							zzcjsearch(zzcjCurrentPage)
						}

					} else {
						layer.msg("请求失败：" + res.msg);
						layer.close(index);
					}
				},
				error: function () {
					layer.close(index);
					layer.msg("请求失败");
				},
			})
		}
	});
}
//上传
$(document).on('change', '.uploadButton', function () {
	var fileInput = $('#fileInput')[0]; // 获取文件输入框
	var file = fileInput.files[0]; // 获取选中的第一个文件
	if (!file) {
		layer.msg("请先选择一个文件！");
		return;
	}
	const formData = new FormData();
	formData.append("file", file);
	formData.append("zbuuid", zzcjfk_Fj_Zbuuid);
	formData.append("zzcjfj", 1);
	$.ajax({
		url: url + "/qyqsgl/qyqsglfjxxb/upload",
		type: "POST",
		data: formData,
		headers: {
			'Authorization': 'Bearer ' + 'test1',//token
		},
		processData: false,  // 不处理数据
		contentType: false,  // 不设置 Content-Type
		success: function (response) {
			// if (response.code == 0) {
			layer.msg("上传成功");
			loadFjlist();
			$('#fileInput').val('');
			// }
		},
		error: function () {
			layer.msg("上传失败，请重试！");
		}
	});
});
//预览
$(document).on('click', '.infoYL', function () {
	var id = $(this).data('id');
	var type = $(this).data('type').toLowerCase();
	var currurl = url + "/qyqsgl/qyqsglfjxxb/preview?uuid=" + id;
	if (type == 'png' || type == 'jpg' || type == 'jpeg') {
		$("#previewPdf").attr("src", "");
		$("#previewPdf").css("display", "none")
		$("#previewImg").attr("src", currurl);
		$("#previewImg").css("display", "block");
		$('#previewImgModal').modal('show');
	} else if (type == 'pdf') {
		$("#previewImg").attr("src", "");
		$("#previewImg").css("display", "none");
		$("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
		$("#previewPdf").css("display", "block");
		$('#previewImgModal').modal('show');
	} else {
		layer.msg("当前格式暂不支持预览！");
	}
});
//下载
$(document).on('click', '.infoXZ', function () {
	var id = $(this).data('id');
	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url + "/qyqsgl/qyqsglfjxxb/download?uuid=" + id;  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag); // 下载后移除 a 标签
});
//删除
$(document).on('click', '.infoSC', function () {
	var id = $(this).data('id');
	layer.confirm("是否要删除该文件？", {
		btn: ["确认", "取消"], btn1: function (index, layero) {
			var index = layer.load(0);
			$.ajax({
				url: url + "/qyqsgl/qyqsglfjxxb/delete",  // 接口
				type: "post",
				data: {
					uuid: id
				},
				headers: {
					'Authorization': 'Bearer ' + 'test1',//token
				},
				success: function (response) {
					layer.close(index);
					if (response.code == 0) {
						layer.msg("删除成功");
						loadFjlist();
					} else {
						layer.msg("请求失败：" + response.msg);
					}
				}
			});
		}
	});
});
//tab-大额欠税阻止出境-end



//tab-阻止出境人员-start
function zzcjsearch(zzcjCurrentPage) {
	console.log("3");
	var zzcjryindex = layer.load(0);
	$.ajax({
		type: "post",
		url: url + "/qyqsgl/qyqsgljcdeqszzcjsqb/page",
		async: true,
		headers: {
			'Authorization': 'Bearer ' + 'test1',

		},
		data:
			{

			}
		,
		cache: false,
		dataType: "json",
		success: function (res) {
			layer.close(zzcjryindex);
			$("#zzcjrydataList").html('');
			let rspData = res.data;
			if(rspData && rspData.length > 0) {
				getMyMindMapPage(res.total, zzcjCurrentPage, "#pagination2",10,zzcjsearch)
				for (var i = 0; i < rspData.length; i++) {
					$("#zzcjrydataList").append('<tr>' +
						'<td class="fixed-left-column">' + (i + 1) + '</td>' +
						'<td>' + rspData[i].shxydm + '</td>' +
						'<td>' + rspData[i].nsrmc + '</td>' +
						'<td>' + rspData[i].nsrzt + '</td>' +
						'<td>' + rspData[i].zgswks + '</td>' +
						'<td>' + rspData[i].jdxz + '</td>' +
						'<td>' + rspData[i].ssgly + '</td>' +
						'<td>' + rspData[i].qsye + '</td>' +
						'<td>' + rspData[i].zzcjryxm + '</td>' +
						'<td>' + rspData[i].zzcjksrq + '</td>' +
						'<td>' + rspData[i].zzcjjsrq + '</td>' +
						'<td class="fixed-column">' +
						'<span onclick="deqszzcj(event,2)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].uuid + '">解除阻止出境</span>' +
						'</td></tr>');
				};
			}else{
				$("#zzcjrydataList").html('<tr><td colspan="12">暂无数据</td></tr>');
				$("#pagination2").css("display","none");
			}

		},
		error: function () {
			layer.msg("请求失败");
		},
	})
}
//导出
function zzcjdcFn() {
	var nsrmc2 = $("#nsrmc2").val();
	var nsrszt2 = $("#nsrszt2").val();
	var jsonData = JSON.stringify({
		nsrmc: nsrmc2,
		nsrszt: nsrszt2
	});

	const aTag = document.createElement('a'); // 创建 a 标签
	aTag.href = url+"/qyqsgl/qyqsgljcdeqszzcjsqb/dcFn?jsonData="+encodeURI(encodeURI(jsonData));  // 设置图片的 URL
	document.body.appendChild(aTag); // 将 a 标签添加到页面中
	aTag.click(); // 触发点击事件，开始下载
	document.body.removeChild(aTag);

}
//解除阻止出境
function jczzcj() {
	var id = $(this).attr("data-id");
}

//tab-阻止出境人员-end

//关闭弹窗
function closeModal() {
	$(".whzqModal").hide();
	$(".zzjgModal").hide();
	$(".zzcsModal").hide();
	$(".qsmxModal").hide();
	$(".bjModal").hide();
	$(".dezzjgModal").hide();
	$(".dezzcjmdModal").hide();
	$(".dezzcjModal").hide();
}