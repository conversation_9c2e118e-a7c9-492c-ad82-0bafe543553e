var url = location.origin+"/clf/clfqy";//请求前缀
var loginUser = null;//登录用户
var layer = null;
layui.use('layer',function(){
    layer = layui.layer;
});
//分页
var currentPage = 1; // 当前页
function getMyMindMapPage(totalCount, currentPages, paginationId, pageSize, callFun) {
    $(paginationId).html('');
    var html = "";
    var lastPage;
    var nextPage;
    var showPage = 4;      //每次显示的页数
    var index;
    var x;               //定义后面页数固定

    // 计算总页数
    var totalPages = Math.ceil(totalCount / pageSize);

    html += "<ul class='pagination'>";
    html += "<li><span class='page-link' data-page='1'>首页</span></li>";

    lastPage = currentPages;
    if (lastPage <= 1) {
        lastPage = 1;
    } else {
        lastPage--;
    }

    html += "<li><span class='page-link' data-page='" + lastPage + "'>上一页</span></li>";

    if (totalPages <= showPage) {
        for (var i = 1; i <= totalPages; i++) {
            if (i == currentPages) {
                html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
            } else {
                html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
            }
        }
    } else {
        index = currentPages + showPage;
        x = currentPages;
        if (index > totalPages) {
            index = totalPages + 1;
            x = index - showPage;
        }

        for (var i = x; i < index; i++) {
            if (i == currentPages) {
                html += "<li class='active'><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
            } else {
                html += "<li><span class='page-link' data-page='" + i + "'>" + i + "</span></li>";
            }
        }
    }
    nextPage = currentPages;
    if (nextPage < totalPages) {
        nextPage++;
    } else if (nextPage == totalPages) {
        nextPage = totalPages;
    }

    html += "<li><span class='page-link' data-page='" + nextPage + "'>下一页</span></li>";
    html += "<li><span class='page-link' data-page='" + totalPages + "'>尾页</span></li>";
    html += "</ul>";
    $(paginationId).append(html);

    // 绑定事件
    $(paginationId).off('click', '.page-link').on('click', '.page-link', function () {
        var page = $(this).data('page');
        callFun(page);
    });
}
//初始进入页面
$(function () {
    search(currentPage);
    getloginuser();
})
//获取用户信息
function getloginuser(){
    $.ajax({
        type: "get",
        url: url + "/getLoginUser",
        async: true,
        headers: {
            'Authorization': 'Bearer ' + 'test1',
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (data) {
            loginUser = data;
        }
    });
}

//查询
function search(currentPage) {
    var index = layer.load(0);
    var fgslbh = $("#seachacceptanceNum").val();
    var cqzh = $("#cqzh").val();
    var zldz = $("#zldz").val();
    $.ajax({
        type: "post",//请求类型 get或者post
        url: url + "/getList",//请求地址
        async: true,//异步或者同步，默认是异步
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        //timeout超时时间（毫秒）
        data: {
            fgslbh: fgslbh,
            cqzh: cqzh,
            zldz: zldz,
            pageNo: currentPage,
            pageSize: 10,
        },
        cache: false,//默认: true , 为false不读取缓存
        dataType: "json",
        success: function (data) {
            layer.close(index);
            $("#dataList").html('');
            getMyMindMapPage(data.total, currentPage, "#pagination",10,search)
            let rspData = data.data;
            console.log(rspData);
			// 根据需要自己定义业务类型type   0存量房个人交易 1存量房对公交易 2征纳互动 3股权变更 4大额欠税
            for (var i = 0; i < rspData.length; i++) {
				$("#dataList").append('<tr>' +
					'<td class="fixed-left-column">' + (i + 1) + '</td>' +
					'<td>' + rspData[i].sqbh + '</td>' +
					'<td>' + rspData[i].fgslbh + '</td>' +
					'<td>' + rspData[i].fwlx + '</td>' +
					'<td>' + rspData[i].zldz + '</td>' +
					'<td>' + rspData[i].jzmj + '</td>' +
					'<td>' + rspData[i].cqzh + '</td>' +
					'<td>' + rspData[i].jzjg + '</td>' +
					'<td>' + rspData[i].sbdj + '</td>' +
					'<td>' + rspData[i].sbzj + '</td>' +
					'<td class="fixed-column">'+
                    '<span onclick="detail(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].zbuuid + '">详情</span>' +
					// '<span onclick="jbrList()" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">经办人</span>' +
					// '<span onclick="qyList()" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="' + rspData[i].id + '">企业信息</span>' +
					'<span onclick="fjList(event)" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-uuid="' + rspData[i].uuid + '" data-id="' + rspData[i].zbuuid + '">附件管理</span>' +
					'</tr>');
            };
        },
        error: function () {
            layer.msg("请求失败");
        },
    })
}
//详情
var xqModal = document.getElementById("xqModal");
var activeId = null;//主表uuid
var activeUuid = null;//房屋uuid
function detail(event){
    var element = event.target;
    activeId = $(element).attr("data-id");
    xqModal.style.right = "0";
	$.ajax({
        url: url + "/real/fw/get",  // 接口
        type: "POST",
        data: {
            zbuuid: activeId
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            let rspData = response.data;
            if (response.code == 0) {
                $("#jbrList").html("");
                xqfillTable(rspData);
                let jbrxxList = rspData ? rspData.jbrxxList :[];
                for (var i = 0; i < jbrxxList.length; i++) {
                    $("#jbrList").append(
                        `<tr>
                            <td>${ jbrxxList[i].jyjs }</td>
							<td>${ jbrxxList[i].jbrxm }</td>
                            <td>${ jbrxxList[i].sfzh }</td>
                            <td>${ jbrxxList[i].lxdz }</td>
                            <td>${ jbrxxList[i].lxdh }</td>
							<td>${ jbrxxList[i].wtrq }</td>
                        </tr>`
                    )
                }
            }
        }
    })
}
function xqfillTable(data) {
    document.getElementById('gmfdwzcmc').value = data.gmfdwzcmc;
    document.getElementById('gmftyshxydm').value = data.gmftyshxydm;
    document.getElementById('gmffddbr').value = data.gmffddbr;
    document.getElementById('gmflxdz').value = data.gmflxdz;
    document.getElementById('gmflxdh').value = data.gmflxdh;

    document.getElementById('xsfdwzcmc').value = data.xsfdwzcmc;
    document.getElementById('xsftyshxydm').value = data.xsftyshxydm;
    document.getElementById('xsffddbr').value = data.xsffddbr;
    document.getElementById('xsflxdz').value = data.xsflxdz;
    document.getElementById('xsflxdh').value = data.xsflxdh;
}
//附件列表

function fjList(event) {
    $("#attachmentType").val('营业执照');
    $('#fileInput').val('');
    var element = event.target;
    activeId = $(element).attr("data-id");
    activeUuid = $(element).attr("data-uuid");
    loadFjlist(activeId);
}
// 要调整
var qianzistr = "存量商业用房评估信息采集表（办公用房）,存量商业用房评估信息采集表（车位）,存量商业用房评估信息采集表（公寓）,存量商业用房评估信息采集表（商服用房）,重庆市存量住房信息采集表（个人转让存量住房适用）,税务证明事项告知承诺书（买方承诺书）,存量房交易税费申报表（承受方）,存量房交易税费申报表（转让方）,存量房交易信息采集表";
var qianzhangstr = "存量房交易税费申报表（承受方）,存量房交易税费申报表（转让方）,完税凭证,存量房交易信息采集表";
var activeIdsfj = [];
var fjgdzts = [];
function loadFjlist(activeId) {
    activeIdsfj = [];
    fjgdzts = [];
    document.getElementById("tablefj").checked = false;
    $("#FJFileList").html("");
	$(".fjglModal").show();
	$.ajax({
        url: url + "/fileList",  // 接口
        type: "POST",
        data: {
            zbuuid: activeId,
            fwuuid: activeUuid,
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            let rspData = response.data;
            if (response.code == 0) {
                if (rspData.length == 0) {
                    $("#FJFileList").html("<tr><td style='text-align: center;' colspan='10'>暂无数据</td></tr>");
                }else{
                    for (var i = 0; i < rspData.length; i++) {
                        var innerHtml =
                            `<tr>
                                <td class="fixed-left-column" style='width: 32px;'><input class="tableCheckfj" data-id="${rspData[i].uuid}" data-gdzt="${rspData[i].gdzt}" type="checkbox"></input></td>
                                <td>${ rspData[i].wjm }</td>
                                <td>${ rspData[i].wjzllx }</td>
                                <td>${ rspData[i].wjgs==1?'纳税人':rspData[i].wjgs==2?'房管':rspData[i].wjgs==3?'税务':rspData[i].wjgs==4?'公安局':rspData[i].wjgs }</td>
                                <td>${ rspData[i].wjdx }</td>
                                <td>${rspData[i].nsrsfqz?rspData[i].nsrsfqz:qianzistr.indexOf(rspData[i].wjzllx)>-1?"否":"-"}</td>
                                <td>${rspData[i].swsfyqr?rspData[i].swsfyqr:qianzhangstr.indexOf(rspData[i].wjzllx)>-1?"否":"-"}</td>
                                <td>${rspData[i].gdzt?rspData[i].gdzt:"否"}</td>
                                <td class="fixed-column" style='width: 220px;'>
                                <span class="infoYL" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}" data-type="${rspData[i].wjlx}">预览</span>
                                <span class="infoXZ" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">下载</span>`
                                if(rspData[i].wjgs=="3"){
                                    innerHtml += `<span class="infoSC" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">删除</span>`
                                }
                                if(qianzistr.indexOf(rspData[i].wjzllx)>-1 && (rspData[i].nsrsfqz=="否" || rspData[i].nsrsfqz=="")){
                                    innerHtml+= `<span class="infoTS" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}">推送</span>`;
                                }
                                if(qianzhangstr.indexOf(rspData[i].wjzllx)>-1 && (rspData[i].swsfyqr=="否" || rspData[i].swsfyqr=="")){
                                    innerHtml+=`<span class="infoQR" style="cursor:pointer;margin-right: 10px;color: rgb(39, 137, 238);font-size: 12px;" data-id="${rspData[i].uuid}" data-documentType="${rspData[i].wjzllx}">确认</span>`;
                                }
                        innerHtml+= `</td></tr>`
                        $("#FJFileList").append(innerHtml)
                    }
                }
            }
        }
    })
}
//附件归档
$(document).on('click', '#FJFileList tr', function (event) {
    var target = event.target;
    if ($(target).closest(".fixed-column").length > 0) {
        return;
    }
    if ($(target).closest(".fixed-left-column").length > 0) {
        return;
    }
    // 获取当前行中的复选框
    var checkbox = $(this).find('.tableCheckfj');

    // 判断是否点击了复选框本身，如果是，则不处理
    if ($(event.target).is(checkbox)) {
        return;
    }

    // 切换复选框状态
    if (checkbox.is(':checked')) {
        checkbox.prop('checked', false); // 取消选中
    } else {
        checkbox.prop('checked', true); // 选中
    }

    // 触发复选框的 change 事件（如果需要）
    checkbox.trigger('change');
});
////行选中
$(document).on('change', '.tableCheckfj', function () {
    if ($(this).is(':checked')) {
        $(this).parents('tr').addClass('selected');
        activeIdsfj.push($(this).data('id'));
        fjgdzts.push($(this).data('gdzt'));
        if($("#FJFileList tr").length==activeIdsfj.length){
            document.getElementById("tablefj").checked = true;
        }else{
            document.getElementById("tablefj").checked = false;
        }
    } else {
        $(this).parents('tr').removeClass('selected');
        for (var i = 0; i < activeIdsfj.length; i++) {
            if (activeIdsfj[i] == $(this).data('id')) {
                activeIdsfj.splice(i, 1);
                fjgdzts.splice(i, 1);
            }
        }
        document.getElementById("tablefj").checked = false;
    }
});
////全选
function checkAllfj(){
    var tableck = document.getElementById("tablefj").checked;
    var checkboxs = $(".tableCheckfj");
    activeIdsfj=[];
    fjgdzts=[];
    for(var i = 0;i<checkboxs.length;i++){
        var checkbox = checkboxs[i];
        if(tableck){
            checkbox.checked = true;
            activeIdsfj.push($(checkbox).data('id'));
            fjgdzts.push($(checkbox).data('gdzt'));
        }else{
            checkbox.checked = false;
            activeIdsfj=[];
            fjgdzts=[];
        }
    }
}
function fjgdFn() {
    if (activeIdsfj.length == 0) {
        layer.msg("请选择数据");
        return;
    }
    for(var i=0;i<fjgdzts.length;i++){
        if(fjgdzts[i]=="已归档"){
            layer.msg("请选择未归档数据");
            return;
        }
    }
    console.log("activeIdsfj=====>",activeIdsfj);
    layer.confirm("是否要对选中"+activeIdsfj.length+"个文件进行归档？",{btn:["确认","取消"],btn1:function (index,layero) {
        var indexfj = layer.load(0);
        $.ajax({
            url: url + "/real/fj/gd",  // 接口
            type: "POST",
            data: {
                zbuuid: activeId,
                fwuuid: activeUuid,
                fjuuids: activeIdsfj.join(',')
            },
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            success: function (response) {
                layer.close(indexfj);
                if (response.code == 0) {
                    layer.msg("操作成功");
                    loadFjlist(activeId);
                }else{
                    layer.msg(response.msg)
                }
            }
        });
    }});
}
//关闭
function closeModal() {
	// $(".jbrModal").hide();
	// $(".qyModal").hide();
    xqModal.style.right = "-100%";
	$(".fjglModal").hide();
}
//上传
$('.uploadButton').change(function () {
    var fileInput = $('#fileInput')[0]; // 获取文件输入框
    var file = fileInput.files[0]; // 获取选中的第一个文件
    var attachmentName = $("#attachmentType").val();
    if (!file) {
        layer.msg("请先选择一个文件！");
        return;
    }
    const formData = new FormData();
    formData.append("file", file);
    formData.append("wjzllx", attachmentName);
    formData.append("wjgs", '3');
    formData.append("fwuuid", activeUuid);
    formData.append("configId", 0);

    $.ajax({
        url: url + "/real/fj/upload",
        type: "POST",
        data: formData,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        processData: false,  // 不处理数据
        contentType: false,  // 不设置 Content-Type
        success: function (response) {
            if (response.code == 0) {
                layer.msg("上传成功");
                loadFjlist(activeId);
                $('#fileInput').val('');
            }
        },
        error: function () {
            layer.msg("上传失败，请重试！");
        }
    });
});
//预览
$(document).on('click', '.infoYL', function () {
    var id = $(this).data('id');
    var type =  $(this).data('type');
    var currurl =url+"/preview?uuid="+id;
    if(type == 'png' || type == 'jpg' || type == 'jpeg'){
        $("#previewPdf").attr("src", "");
        $("#previewPdf").css("display","none")
        $("#previewImg").attr("src", currurl);
        $("#previewImg").css("display","block");
        $('#previewImgModal').modal('show');
    }else if(type == 'pdf'){
        $("#previewImg").attr("src", "");
        $("#previewImg").css("display","none");
        $("#previewPdf").attr("src", currurl);//需要个返回流的接口+currurl  预览pdf
        $("#previewPdf").css("display","block");
        $('#previewImgModal').modal('show');
    }else{
        layer.msg("当前格式暂不支持预览！");
    }
});
//下载
$(document).on('click', '.infoXZ', function () {
    var id = $(this).data('id');
    const aTag = document.createElement('a'); // 创建 a 标签
    aTag.href = url+"/download?uuid="+id;  // 设置图片的 URL
    document.body.appendChild(aTag); // 将 a 标签添加到页面中
    aTag.click(); // 触发点击事件，开始下载
    document.body.removeChild(aTag); // 下载后移除 a 标签
});
//删除附件
$(document).on('click', '.infoSC', function () {
    var id = $(this).data('id');
    layer.confirm("是否要删除该文件？",{btn:["确认","取消"],btn1:function (index,layero) {
        var index = layer.load(0);
        $.ajax({
            url: url + "/real/fj/delete",  // 接口
            type: "post",
            data: {
                uuid: id
            },
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            success: function (response) {
                layer.close(index);
                if (response.code == 0) {
                    layer.msg("操作成功");
                    loadFjlist(activeId);
                }else{
                    layer.msg("请求失败："+response.msg);
                }
            }
        });
    }});
});
//签章配置
$(document).on('click', '.infoQR', function () {
    $('#fileInput1').val('');
    fileId = $(this).data('id');
    $('#swqzModal').modal('show');
});
$(document).on('change', '#fileInput1', function () {
    $("#selectfiles").html("");
    for(var i =0;i<this.files.length;i++){
        const file = this.files[i];
        $("#selectfiles").append("当前选择附件："+file.name+"<br/>附件大小："+file.size+" kb");
    }
});
function swqzTrue(){
    var fileInput = $('#fileInput1')[0]; // 获取文件输入框
    var file = fileInput.files[0]; // 获取选中的第一个文件

    const formData = new FormData();
    formData.append("file", file);
    formData.append("uuid", fileId);
    $.ajax({
        url: url + "/real/fj/swqr",  // 接口
        type: "POST",
        data: formData,
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        processData: false,  // 不处理数据
        contentType: false,  // 不设置 Content-Type
        success: function (response) {
            if (response.code == 0) {
                layer.msg("操作成功");
                $('#swqzModal').modal('hide');
                loadFjlist(activeId);
            }else{
                layer.msg("请求失败："+response.msg);
            }
        },
        error: function () {
            layer.msg("上传失败，请重试！");
        }
    });
}
//签字开始  // 要调整
var positionObj = {
    // "存量商业用房评估信息采集表（办公用房）":[],//签字
    // "存量商业用房评估信息采集表（车位）":[],//签字
    // "存量商业用房评估信息采集表（公寓）":[],//签字
    // "存量商业用房评估信息采集表（商服用房）":[],//签字
    // "重庆市存量住房信息采集表（个人转让存量住房适用）":[],//签字
    // "税务证明事项告知承诺书（买方承诺书）":[],//签字
    // "存量房交易税费申报表（承受方）":[],//签字加盖章
    // "存量房交易税费申报表（转让方）":[],//签字加盖章
    // "存量房交易信息采集表":[],//签字加盖章
    "存量房交易税费申报表（承受方）":[
        {type:1, x:180, y:200, w:150, h:100, keyword:'…', start:'left',index:7},
        {type:1, x:180, y:80, w:150, h:100, keyword:'…', start:'left',index:3}
    ],
    "存量房交易税费申报表（转让方）": [//存量房交易税费申报表（转让方） 只能单字查找关键字 关键字：…
        {type:1, x:180, y:200, w:150, h:100, keyword:'…', start:'left',index:7},
        {type:1, x:180, y:80, w:150, h:100, keyword:'…', start:'left',index:3}
    ],
    "存量房交易信息采集表":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字（盖章）确认', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字（盖章）确认', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'税务人员签字审核', start:'right',index:0}
    ],
    // "税务证明事项告知承诺书（卖方承诺书）":[
    //     {type:1, x:0, y:-50, w:120, h:60, keyword:'姓名(关系):', start:'right',index:0},
    //     {type:1, x:0, y:-30, w:120, h:60, keyword:'身份证件号码:', start:'right',index:0},
    //     {type:1, x:0, y:-30, w:120, h:60, keyword:'纳税人:', start:'right',index:0}
    // ],
    "税务证明事项告知承诺书（买方承诺书）":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'纳税人签字', start:'right',index:0}
    ],
    "存量商业用房评估信息采集表（办公用房）":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:3, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "存量商业用房评估信息采集表（车位）":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:3, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "存量商业用房评估信息采集表（公寓）":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0}
    ],
    "存量商业用房评估信息采集表（商服用房）":[
        {type:1, x:0, y:-50, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-30, w:120, h:60, keyword:'受让方签字', start:'right',index:0},
        {type:3, x:0, y:-30, w:120, h:60, keyword:'签字按印', start:'right',index:0}
    ],
    "重庆市存量住房信息采集表（个人转让存量住房适用）":[
        {type:1, x:0, y:-40, w:120, h:60, keyword:'转让方签字', start:'right',index:0},
        {type:1, x:0, y:-40, w:120, h:60, keyword:'承受方签字', start:'right',index:0}
    ]
}
//签字结束 回推
var allIdx = null;
function qzcallback(rjson){
    layer.close(allIdx);
    if(rjson.code!=0){
        layer.msg(rjson.info);
        return;
    }
    if (rjson.retData != null) {//处理base64位的图片存放到设备
        if (rjson.retData.pdfBase64 != null) {
            var rest = convertBase64UrlToBlob(rjson.retData.pdfBase64);
            const formData = new FormData();
            formData.append("file", rest);
            formData.append("uuid", fileId);
            $.ajax({
                url: url + "/real/fj/ts",  // 接口
                type: "POST",
                data: formData,
                headers: {
                    'Authorization': 'Bearer ' + 'test1',//token
                },
                processData: false,  // 不处理数据
                contentType: false,  // 不设置 Content-Type
                success: function (response) {
                    if (response.code == 0) {
                        layer.msg("操作成功");
                        loadFjlist(activeId);
                    }else{
                        layer.msg("请求失败："+response.msg);
                    }
                },
                error: function () {
                    layer.msg("上传失败，请重试！");
                }
            });
        }
    }
}
//推送
var fileId = null;
$(document).on('click', '.infoTS', function () {
    fileId = $(this).data('id');
    layer.confirm("是否需要推送该文件到纳税人签章？",{btn:["确认","取消"],btn1:function (index,layero) {
        var index = layer.load(0);
        $.ajax({
            url: url + "/previewqk",  // 接口
            type: "post",
            data: {
                uuid: fileId
            },
            headers: {
                'Authorization': 'Bearer ' + 'test1',//token
            },
            success: function (response) {
                layer.close(index);
                if (response.code == 0) {
                    var base64String = response.data[0].wjnr;
                    var documentType = response.data[0].wjzllx;
                    if(positionObj[documentType]){
                        allIdx = layer.load(0);
                        TRANSCEND_SIGNDEVICE.startPdf(qzcallback, getSignConfig(1,base64String,JSON.stringify(positionObj[documentType]))); // 开始签名
                    }else{
                        layer.msg("当前文书未配置签字信息！");
                    }
                }else{
                    layer.msg("请求失败："+response.msg);
                }
            }
        });
    }});
});


//审核通过
function shtg() {
	$("#shtgModal").modal('show');
}
function shtgTrue() {
	let sqbh = $("#sqbh").val();
	if (sqbh == "") {
		layer.msg("请输入申请编号");
		return false;
	}
    var index = layer.load(0);
    $.ajax({
        url: url + "/real/qy/over",  // 接口
        type: "POST",
        data: {
            sqbh: sqbh,
            sqzt:"审核通过"
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            layer.close(index);
            if (response.code == 0) {
                layer.msg("操作成功");
                $("#shtgModal").modal('hide');
                location.reload();
            }
        }
    })
}
//审核驳回
function shbh() {
	$("#shbhModal").modal('show');
}
function shbhTrue() {
	let sqbh = $("#bhsqbh").val();
	let bhyy = $("#bhyy").val();
	if (sqbh == "") {
		layer.msg("请输入申请编号");
		return false;
	}
	if (bhyy == "") {
        layer.msg("请输入驳回原因");
		return false;
	}
    var index = layer.load(0);
    $.ajax({
        url: url + "/real/qy/reject",  // 接口
        type: "POST",
        data: {
            sqbh: sqbh,
            bhyy: bhyy,
            sqzt: "审核驳回" 
        },
        headers: {
            'Authorization': 'Bearer ' + 'test1',//token
        },
        success: function (response) {
            layer.close(index);
            if (response.code == 0) {
                layer.msg("操作成功");
                $("#shbhModal").modal('hide');
                location.reload();
            } else {
                layer.msg(response.msg);
            }
        }
    })
}


