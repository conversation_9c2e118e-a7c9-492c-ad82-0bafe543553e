<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="Cache-Control" CONTENT="no-cache">
	<meta http-equiv="Pragma" content="no-cache">
	<meta name="format-detection" content="telephone=no" />
	<title>管户变动</title>
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link rel="stylesheet" href="./js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/szscreset.css">
	</link>
	<style>
		* {
			box-sizing: border-box
		}

		.main {
			background-color: #f2f2f2;
			width: 100%;
			min-width: 1200px;
			overflow: hidden;
			min-height: 100vh;
		}

		.myContainer {
			width: 1200px;
			margin: 0 auto;
			padding: 24px;
			background-color: #FFFFFF;
			min-height: 100vh;
		}

		#ssmc {
			background: #eee;
			cursor: not-allowed;
		}

		.myThead {
			background-color: #2789EE;
			color: white;
			font-size: 14px;
		}

		.subThead th {
			min-width: 100px;
		}
	</style>
</head>

<body>
	<div class="main">
		<div class="myContainer">
			<div class="layui-tab layui-tab-brief" lay-filter="my-handle">
				<ul class="layui-tab-title">
					<li class="layui-this" lay-id="tab1">管户变动汇总</li>
					<li lay-id="tab2">有收入有税户</li>
					<li lay-id="tab3">重点税源户</li>
					<li lay-id="tab4">开票无税户</li>
					<li lay-id="tab5">受票无税户</li>
					<li lay-id="tab6">未办税户</li>
				</ul>
				<div class="layui-tab-content">
					<div class="layui-tab-item layui-show">
						<div class="seachBox">
							<form class="form-inline">
								<div class="row">
									<div class="col-sm-3 col-md-3 col-lg-3">
										<div class="form-group">
											<label for="ssmc">税所名称</label>
											<select id="ssmc" disabled class="filter-select">
												<option value="虎溪街道">虎溪街道</option>
												<option value="曾家镇">曾家镇</option>
												<option value="金凤镇">金凤镇</option>
												<option value="走马镇">走马镇</option>
												<option value="巴福镇">巴福镇</option>
												<option value="石板镇">石板镇</option>
												<option value="白市驿镇">白市驿镇</option>
												<option value="含谷镇">含谷镇</option>
												<option value="香炉山街道">香炉山街道</option>
												<option value="西永街道">西永街道</option>
											</select>
										</div>
									</div>
									<div class="col-sm-6 col-md-6 col-lg-6">
										<div class="form-group layui-form-item">
											<label class="layui-form-label">日期范围</label>
											<div class="layui-inline" id="ID-laydate-range">
												<div class="layui-input-inline">
													<input type="text" autocomplete="off" id="ID-laydate-start-date"
														class="layui-input" placeholder="开始日期">
												</div>
												<div class="layui-form-mid">-</div>
												<div class="layui-input-inline">
													<input type="text" autocomplete="off" id="ID-laydate-end-date"
														class="layui-input" placeholder="结束日期">
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-3 col-md-3 col-lg-3" style="text-align: right;">
										<!-- <button type="button" class="btn btn-default">重置</button> -->
										<button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
									</div>
								</div>
							</form>
						</div>
						<div class="tableBox">
							<table class="table tableqsList table-hover">
								<thead class="myThead">
									<tr>
										<th></th>
										<th style="border-right: 1px solid white;"></th>
										<th style="border-right: 1px solid white;cursor: pointer;" colspan="4"
											onclick="handleClick(0)">
											管户量</th>
										<th style="border-right: 1px solid white;cursor: pointer;" colspan="4"
											onclick="handleClick(1)">
											有收入有税户</th>
										<th style="border-right: 1px solid white;cursor: pointer;" colspan="4"
											onclick="handleClick(2)">
											重点税源户</th>
										<th style="border-right: 1px solid white;cursor: pointer;" colspan="4"
											onclick="handleClick(3)">
											开票无税户</th>
										<th style="border-right: 1px solid white;cursor: pointer;" colspan="4"
											onclick="handleClick(4)">
											受票无税户</th>
										<th style="cursor: pointer;" colspan="4" onclick="handleClick(5)">未办税户</th>
									</tr>
									<tr class="subThead">
										<th>序号</th>
										<th style="border-right: 1px solid white;">日期</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th style="border-right: 1px solid white;">净增长</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th style="border-right: 1px solid white;">净增长</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th style="border-right: 1px solid white;">净增长</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th style="border-right: 1px solid white;">净增长</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th style="border-right: 1px solid white;">净增长</th>
										<th>合计</th>
										<th>新增</th>
										<th>减少</th>
										<th>净增长</th>
									</tr>
								</thead>
								<tbody id="dataList">

								</tbody>
							</table>
						</div>
					</div>
					<div class="layui-tab-item">内容-2</div>
					<div class="layui-tab-item">内容-3</div>
					<div class="layui-tab-item">内容-4</div>
					<div class="layui-tab-item">内容-5</div>
					<div class="layui-tab-item">内容-5</div>
				</div>
			</div>
		</div>
	</div>
	<script src="./js/jquery-3.7.1.min.js"></script>
	<script src="./js/bootstrap.min.js"></script>
	<script src="./js/echarts.min.js"></script>
	<script src="./js/layui/layui.js"></script>
	<script src="./js/ghbd.js"></script>
</body>

</html>