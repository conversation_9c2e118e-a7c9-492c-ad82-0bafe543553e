<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Pragma" content="no-cache">
    <meta name="format-detection" content="telephone=no" />
    <title>附件管理档案</title>
    <link rel="stylesheet" href="./js/layui/css/layui.css" />
    <link rel="stylesheet" href="css/reset.css" />
    <link rel="stylesheet" href="css/bootstrap.min.css" />
    <style>
        * {
            box-sizing: border-box
        }

        .main {
            background-color: #f2f2f2;
            width: 100%;
            min-width: 1200px;
            overflow: hidden;
            min-height: 100vh;
        }

        .myContainer {
            width: 1200px;
            margin: 0 auto;
            padding: 24px;
            background-color: #FFFFFF;
            min-height: 100vh;
        }

        .seachBox {
            border-bottom: 1px solid #e0e1e6;
        }

        .btnBox {
            text-align: right;
            height: 60px;
            line-height: 60px;
        }

        .myThead {
            background-color: #2789EE;
            color: white;
            font-size: 14px;
        }

        .myBtn {
            padding: 6px 15px !important;
            margin-right: 10px;
        }

        .rightBtn {
            margin-right: 24px;
        }
    </style>
</head>
<script src="./js/jquery-3.7.1.min.js"></script>
<script src="./js/bootstrap.min.js"></script>


<body>
<div class="main">
    <div class="myContainer">
        <div class="seachBox">
            <form class="form-inline">
                <div class="row">
                    <div class="col-sm-3 col-md-3 col-lg-3">
                        <div class="form-group">
                            <label for="ywbh">业务编号</label>
                            <input type="text" class="form-control" id="ywbh" placeholder="请输入">
                        </div>
                    </div>
                    <div class="col-sm-3 col-md-3 col-lg-3">
                        <div class="form-group">
                            <label for="ywlx">业务类型</label>
                            <input type="text" class="form-control" id="ywlx" placeholder="请输入">
                        </div>
                    </div>
                    <div class="col-sm-3 col-md-3 col-lg-3">
                        <div class="form-group">
                            <label for="gdnd">归档年度</label>
                            <input type="text" class="form-control" id="gdnd" placeholder="请输入">
                        </div>
                    </div>
                    <div class="col-sm-3 col-md-3 col-lg-3">
                        <div class="form-group">
                            <label for="gdrq">归档日期</label>
                            <input type="text" class="form-control" id="gdrq" placeholder="请输入">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label for="blry">办理人员</label>
                            <input type="text" class="form-control" id="blry" placeholder="请输入">
                        </div>
                    </div>
                    <div class="col-sm-6 col-md-6 col-lg-6" style="text-align: right;">
                        <!-- <button type="button" class="btn btn-default">重置</button> -->
                        <button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="btnBox">
            <button onclick="location.href='/clf/sfzgzxxt.html'"
                    class="layui-btn layui-btn-primary layui-border-blue midGoback">返回首页</button>
            <button onclick="showGDModal(1)" type="button" class="btn btn-primary">新增</button>
        </div>
        <div class="tableBox">
            <table class="table table-hover">
                <thead class="myThead">
                <tr>
                    <th>序号</th>
                    <th>业务编号</th>
                    <th>业务类型</th>
                    <th>归档状态</th>
                    <th>归档年度</th>
                    <th>归档日期</th>
                    <th>归档方式</th>
                    <th>办理人员</th>
                    <th>附件数量</th>
                    <th class="fixed-column">操作</th>
                </tr>
                </thead>
                <tbody id="dataList">

                </tbody>
            </table>
        </div>
        <nav aria-label="Page navigation" class="myPagination">
            <ul class="pagination" id="pagination"></ul>
        </nav>
    </div>
    <!-- 预览模态框 -->
    <div id="previewModal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static"
         data-keyboard="false" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <span class="modal-title" id="exampleModalLabel">版式文件预览</span>
                </div>
                <div class="modal-body">
                    <iframe id="bswjPreviewPdf" src="" width="100%" height="600px"></iframe>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 预览图片-->
    <div id="previewImgModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false"
         role="dialog" aria-labelledby="exampleModalLabel3" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <span class="modal-title" id="exampleModalLabel3">预览</span>
                </div>
                <div class="modal-body">
                    <img id="previewImg" style="display: none;" src="">
                    <iframe id="previewPdf" style="display: none;" src="" width="100%" height="600px"></iframe>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default myBtn" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增 手动归档模态框 -->
    <div class="modal-mask fjModal gdModal">
        <div class="modal-content" style="min-width: 1200px!important;">
            <div class="modal-header">
                <h3>新增</h3>
                <span class="close-btn closeGdModal">×</span>
            </div>
            <div class="filter-upload-row" style="padding-bottom: 0!important;">
                <label class="upload-button">上传附件<input class="uploadButton" type="file" id="fileInput"
                                                        multiple></label>
            </div>
            <div class="filter-upload-row subIpt">
                <div class="filter-container">
                    <label class="filter-label">业务类型：</label>
                    <div class="radio-group">
                        <label style="text-align: left!important;">
                            <input style="float: left;margin-top: 2px!important;" type="radio" name="ywlx1"
                                   value="征纳互动" checked>
                            <span style="float: left;margin-left: 5px;color: #999;">征纳互动</span>
                        </label>
                        <label style="text-align: left!important;">
                            <input style="float: left;margin-top: 2px!important;" type="radio" name="ywlx1"
                                   value="股权变更">
                            <sapn style="float: left;margin-left: 5px;color: #999;">股权变更</sapn>
                        </label>
                    </div>
                </div>
                <div class="filter-container">
                    <label for="ywbh1" class="filter-label">业务编号：</label>
                    <input type="text" class="form-control" id="ywbh1" placeholder="请输入业务编号">
                </div>
                <div class="filter-container">
                    <label for="nsrmc1" class="filter-label">纳税人名称：</label>
                    <input type="text" class="form-control" id="nsrmc1" placeholder="请输入纳税人名称">
                </div>
                <div class="filter-container">
                    <label for="nsrsbh1" class="filter-label">纳税人识别号：</label>
                    <input type="text" class="form-control" id="nsrsbh1" placeholder="请输入纳税人识别号">
                </div>
            </div>
            <div class="tableBox2 tableBox" style="flex: 1;">
                <table class="table table-hover">
                    <thead class="myThead myThead2">
                    <tr>
                        <th class="fixed-left-column">序号</th>
                        <th>附件名称</th>
                        <th>附件大小</th>
                        <th>归档状态</th>
                        <th class="fixed-column">操作</th>
                    </tr>
                    </thead>
                    <tbody id="FJFileList">

                    </tbody>
                </table>
                <nav aria-label="Page navigation" class="myPagination" style="width: 96%;margin: 0 auto;">
                    <ul class="pagination" id="infoPagination"></ul>
                </nav>
            </div>

            <div class="action-buttons">
                <button class="btn-default closeGdModal">取消</button>
                <button class="confirm-btn" onclick="zcFn()">暂存</button>
                <button class="confirm-btn" onclick="gdFn()">归档</button>
            </div>
        </div>
    </div>
    <!-- 附件查看 -->
    <div class="modal-mask fjModal fjSeeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="fjSeeOrEditTit">查看附件</h3>
                <span class="close-btn closeFjModal">×</span>
            </div>
            <div class="filter-upload-row subIpt">
                <div class="filter-container">
                    <label for="attachmentFjType" class="filter-label">业务类型：</label>
                    <select id="attachmentFjType" disabled class="filter-select" style="cursor: not-allowed;">
                        <option value="征纳互动">征纳互动</option>
                        <option value="股权变更">股权变更</option>
                        <option value="大额欠税">大额欠税</option>
                        <option value="存量房个人">存量房个人</option>
                        <option value="存量房企业">存量房企业</option>
                    </select>
                </div>
                <div class="filter-container">
                    <label for="ywbh2" class="filter-label">业务编号：</label>
                    <input disabled type="text" class="form-control" id="ywbh2" placeholder="请输入业务编号">
                </div>
                <div class="filter-container">
                    <label for="nsrmc2" class="filter-label">纳税人名称：</label>
                    <input disabled type="text" class="form-control" id="nsrmc2" placeholder="请输入纳税人名称">
                </div>
                <div class="filter-container">
                    <label for="nsrsbh2" class="filter-label">纳税人识别号：</label>
                    <input disabled type="text" class="form-control" id="nsrsbh2" placeholder="请输入纳税人识别号">
                </div>
            </div>
            <div class="tableBox2 tableBox">
                <table class="table table-hover">
                    <thead class="myThead myThead2">
                    <tr>
                        <th>附件名称</th>
                        <th>附件大小</th>
                        <th>归档状态</th>
                        <th class="fixed-column">操作</th>
                    </tr>
                    </thead>
                    <tbody id="seeFJFileList">

                    </tbody>
                </table>
            </div>
            <nav aria-label="Page navigation" class="myPagination">
                <ul class="pagination" id="paginationInfo"></ul>
            </nav>
        </div>
    </div>
    <!-- 附件编辑 -->
    <div class="modal-mask fjModal fjEditModal">
        <div class="modal-content" style="min-width: 1200px!important;">
            <div class="modal-header">
                <h3>编辑</h3>
                <span class="close-btn closeBjModal">×</span>
            </div>
            <div class="filter-upload-row" style="padding-bottom: 0!important;">
                <label class="upload-button">上传附件<input class="uploadButtonBj" type="file" id="fileInputBj"
                                                        multiple></label>
            </div>
            <div class="filter-upload-row subIpt">
                <div class="filter-container">
                    <label class="filter-label">业务类型：</label>
                    <div class="radio-group">
                        <label style="text-align: left!important;">
                            <input style="float: left;margin-top: 2px!important;" type="radio" name="ywlx2"
                                   value="征纳互动">
                            <span style="float: left;margin-left: 5px;color: #999;">征纳互动</span>
                        </label>
                        <label style="text-align: left!important;">
                            <input style="float: left;margin-top: 2px!important;" type="radio" name="ywlx2"
                                   value="股权变更">
                            <sapn style="float: left;margin-left: 5px;color: #999;">股权变更</sapn>
                        </label>
                    </div>
                </div>
                <div class="filter-container">
                    <label for="ywbh3" class="filter-label">业务编号：</label>
                    <input type="text" class="form-control" id="ywbh3" placeholder="请输入业务编号">
                </div>
                <div class="filter-container">
                    <label for="nsrmc3" class="filter-label">纳税人名称：</label>
                    <input type="text" class="form-control" id="nsrmc3" placeholder="请输入纳税人名称">
                </div>
                <div class="filter-container">
                    <label for="nsrsbh3" class="filter-label">纳税人识别号：</label>
                    <input type="text" class="form-control" id="nsrsbh3" placeholder="请输入纳税人识别号">
                </div>
            </div>
            <div class="tableBox2 tableBox" style="flex: 1;">
                <table class="table table-hover">
                    <thead class="myThead myThead2">
                    <tr>
                        <th class="fixed-left-column">序号</th>
                        <th>附件名称</th>
                        <th>附件大小</th>
                        <th>归档状态</th>
                        <th class="fixed-column">操作</th>
                    </tr>
                    </thead>
                    <tbody id="bjFJFileList">

                    </tbody>
                </table>
                <nav aria-label="Page navigation" class="myPagination" style="width: 96%;margin: 0 auto;">
                    <ul class="pagination" id="bjPagination"></ul>
                </nav>
            </div>

            <div class="action-buttons">
                <button class="btn-default closeBjModal">取消</button>
                <button class="confirm-btn" onclick="zcFn2()">暂存</button>
                <button class="confirm-btn" onclick="gdFn2()">归档</button>
            </div>
        </div>
    </div>
</div>
<script src="./js/layui/layui.js"></script>
<script src="./js/fjglda.js"></script>
<script src="./js/TranscendSign.js"></script>
</body>

</html>