<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Pragma" content="no-cache">
    <meta name="format-detection" content="telephone=no" />
    <title>所长手册</title>
    <link rel="stylesheet" href="css/szscreset.css">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    </link>
    <style>
        * {
            box-sizing: border-box
        }

        .main {
            background-color: #f2f2f2;
            width: 100%;
            min-width: 1200px;
            overflow: hidden;
            min-height: 100vh;
        }

        .myContainer {
            width: 1200px;
            margin: 0 auto;
            padding: 24px;
            background-color: #FFFFFF;
            min-height: 100vh;
        }

        #mapWin {
            position: absolute;
            display: none;
            background-color: #fff;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            font-size: 14px;
            color: #333;
            max-width: 200px;
            word-wrap: break-word;
            cursor: pointer;
        }

        #swsName {
            line-height: 30px;
            padding: 0 10px;
        }

        .content_title {
            line-height: 30px;
            padding: 0 10px;
        }

        .content_title:hover {
            background-color: #f0f0f0;
        }
    </style>
</head>

<body>
    <div class="main">
        <div class="myContainer">
            <div id="chartBox" style="width: 1152px;height:80vh;"></div>
            <div id="mapWin"></div>
        </div>
    </div>
    <script src="./js/jquery-3.7.1.min.js"></script>
    <script src="./js/bootstrap.min.js"></script>
    <script src="./js/echarts.min.js"></script>
    <script src="./js/layui/layui.js"></script>
    <script src="./js/gx.js"></script>
    <script type="text/javascript">
        var myChart = echarts.init(document.getElementById('chartBox'));
        echarts.registerMap('gx', geoJson);
        let mapData = [
            { name: "石板镇", value: 1.05, rs: 708, zb: 0.0301 * 100 },
            { name: "走马镇", value: 0.89, rs: 1948, zb: 0.0478 * 100 },
            { name: "含谷镇", value: 5.94, rs: 3684, zb: 0.1041 * 100 },
            { name: "巴福镇", value: 4.26, rs: 1342, zb: 0.0435 * 100 },
            { name: "金凤镇", value: 16.36, rs: 3107, zb: 0.1025 * 100 },
            { name: "白市驿镇", value: 6.64, rs: 8922, zb: 0.203 * 100 },
            { name: "虎溪街道", value: 9.26, rs: 12020, zb: 0.2281 * 100 },
            { name: "西永街道", value: 26.36, rs: 5422, zb: 0.1675 * 100 },
            { name: "曾家镇", value: 7.57, rs: 2844, zb: 0.0555 * 100 },
            { name: "香炉山街道", value: 0.26, rs: 1130, zb: 0.0243 * 100 }
        ];
        const option = {
            backgroundColor: "",
            geo: {
                map: "gx",
                aspectScale: 1.1, //长宽比
                zoom: 1, // 增加缩放比例
                roam: false,
                left: "2%",
                top: "2%",
                bottom: "5%",
                right: "2%",
                layoutCenter: ["50%", "50%"],
                layoutSize: "95%", // 增加布局尺寸
                itemStyle: {
                    normal: {
                        areaColor: "#082F56",
                        shadowColor: "#3c96e3",
                        shadowOffsetX: 2,
                        shadowOffsetY: 5,
                        borderColor: "#27698a",
                        borderWidth: 1.5,
                    },
                },
            },
            series: [
                {
                    type: "map",
                    silent: false,
                    selectedMode: false,
                    data: mapData,
                    aspectScale: 1.1,
                    left: "2%",
                    right: "2%",
                    top: "2%",
                    bottom: "5%",
                    label: {
                        normal: {
                            show: true,
                            textStyle: {
                                fontSize: "14", // 增加字体大小
                                color: "#fff",
                                fontFamily: "Alibaba PuHuiTi",
                                fontWeight: "400",
                            },
                        },
                        emphasis: {
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    itemStyle: {
                        normal: {
                            borderColor: "#77d8ef",
                            borderWidth: 0.5,
                            areaColor: "rgba(57, 140, 143, 1)",
                            borderType: "solid",
                        },
                    },
                    zoom: 1, // 增加缩放比例
                    roam: false,
                    map: "gx", // 使用
                    layoutCenter: ["50%", "50%"],
                    layoutSize: "95%", // 增加布局尺寸
                },
            ],
        };
        myChart.setOption(option);
        window.addEventListener('resize', function () {
            myChart.resize();
        }.bind(this));
        setTimeout(function () {
            myChart.resize();
        }.bind(this), 200);

        //点击事件
        myChart.on('click', function (params) {
            console.log(params.data);
            if (params.data) {
                const x = params.event.offsetX;
                const y = params.event.offsetY;
                const mapWin = document.getElementById('mapWin');
                mapWin.style.left = `${x}px`;
                mapWin.style.top = `${y}px`;
                mapWin.style.display = 'block';
                mapWin.innerHTML = `
                    <div class="map_content_item">
                        <div>${params.data.name}</div>
                        <div class="content_title" data-url="swsgk.html" data-name="${params.data.name}">税务所概况</div>
                        <div class="content_title" data-url="ryqk.html" data-name="${params.data.name}">人员情况</div>
                        <div class="content_title" data-url="gzsz.html" data-name="${params.data.name}">岗责设置</div>
                        <div class="content_title" data-url="swsgzf.html" data-name="${params.data.name}">税务所工作法</div>
                        <div class="content_title" data-url="ghbd.html" data-name="${params.data.name}">管户变动</div>
                        <div class="content_title" data-url="srbd.html" data-name="${params.data.name}">收入变动</div>
                        <div class="content_title" data-url="sqxy.html" data-name="${params.data.name}">诉求响应</div>
                        <div class="content_title" data-url="gbgzzl.html" data-name="${params.data.name}">干部工作质量</div>
                    </div>
                `;
            } else {
                console.log("点击的区域没有绑定数据");
            }
        }.bind(this));
        // 使用事件委托绑定点击事件
        document.getElementById('mapWin').addEventListener('click', function (event) {
            const target = event.target;
            if (target.classList.contains('content_title')) {
                const url = target.getAttribute('data-url');
                const name = target.getAttribute('data-name');
                if (url) {
                    window.location.href = url + '?name=' + encodeURIComponent(name);
                }
            }
        });

    </script>
</body>

</html>