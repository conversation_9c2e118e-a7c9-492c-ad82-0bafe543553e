/* 缁備胶鏁Phone娑撶挅afari閻ㄥ嫬鐡ч崣鐤殰閸斻劏鐨熼弫锟� */
html {
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;

}

/* 閸樺娅巌Phone娑擃參绮拋銈囨畱input閺嶅嘲绱�  濞撳懘娅庨懟瑙勭亯濞村繗顫嶉崳銊ф畱閸﹀棜顫楃悰銊ュ礋*/
input[type="submit"],

input[type="reset"],

input[type="button"] {
	-webkit-appearance: none;
	resize: none;
}

/* 閸欐牗绉烽柧鐐复妤傛ü瀵�  */
body,
div,
ul,
li,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
p,
dl,
dt,
dd,
a,
img,
button,
form,
table,
th,
tr,
td,
tbody,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 鐠佸墽鐤咹TML5閸忓啰绀屾稉鍝勬健 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

/* 閸ュ墽澧栭懛顏堬拷鍌氱安 */
img {
	max-width: 100%;
	height: auto;
	width: auto\9;
	/* ie8 */
	-ms-interpolation-mode: bicubic;
	/*娑撹桨绨￠悡褔銆恑e閸ュ墽澧栫紓鈺傛杹婢惰京婀�*/
}

/* 閸掓繂顫愰崠锟� */
body,
div,
ul,
li,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
p,
dl,
dt,
dd,
a,
img,
button,
form,
table,
th,
tr,
td,
tbody,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	margin: 0;
	padding: 0;
	border: none;
}

body {
	font-family: Microsoft YaHei, Tahoma, Arial, sans-serif;
	color: #555;
	background-color: #FFFFFF;
}

em,
i {
	font-style: normal;
}

strong {
	font-weight: normal;
}

.clearfix:after {
	content: "";
	display: block;
	visibility: hidden;
	height: 0;
	clear: both;
}

.clearfix {
	zoom: 1;
}

a {
	text-decoration: none;
	color: #969696;
	font-family: Microsoft YaHei, Tahoma, Arial, sans-serif;
}

a:hover {
	text-decoration: none;
}

ul,
ol {
	list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: 100%;
	font-family: Microsoft YaHei;
}

img {
	border: none;
}

.scope {
	min-width: 320px;
	max-width: 540px;
}

.clearfix:before,
.clearfix:after,
.cf:before,
.cf:after {
	display: block;
	content: " ";
}

.clearfix:after,
.cf:after {
	clear: both;
}

.clearfix,
.cf {
	zoom: 1;
}

/*body{font-size: 62.5%;}*/
input:-webkit-autofill {
	-webkit-box-shadow: inset 0 0 0 100px #ffffff inset !important;
	-webkit-text-fill-color: #818391;
}

input,
select {
	background: white;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	font-size: 12px;
	color: #999999;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
	font-size: 12px;
	color: #999999;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
	font-size: 12px;
	color: #999999;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
	font-size: 12px;
	color: #999999;
}

input {
	text-indent: 10px;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.row {
	margin-bottom: 18px;
}

label {
	width: 90px;
	text-align: right;
	font-size: 12px;
	font-weight: 500 !important;
	color: #192236;
	padding-right: 12px;
	vertical-align: middle;
	margin-bottom: 0 !important;
}

.form-control {
	box-shadow: none !important;
	border: 1px solid #DCDFE6 !important;
	color: #192236 !important;
	padding: 0 !important;
	width: 170px!important;
}
.form-group{
	width: 100%!important;
}
.allModal{
	z-index: 99;
}
.allModal .form-control {
	width: 240px!important;
}

.form-control:focus {
	box-shadow: none !important;
	outline: none !important;
	border-color: #2789EE !important;
}

.btn-primary {
	background-color: #2789EE !important;
	border-color: #2789EE !important;
	font-size: 12px !important;
}

.btn-default {
	font-size: 12px !important;
	color: #192236 !important;
}

.caret {
	margin-left: 5px !important;
}

.dropdown-menu>li {
	height: 30px;
	line-height: 30px;
	padding: 0 20px;
	font-size: 12px;
	color: #192236;
	border-bottom: 1px solid #f2f2f2 !important;
	cursor: pointer;
}

.dropdown-menu>li:hover {
	background-color: #1890ff;
	color: white;
}

.table>thead>tr>th {
	border-bottom: 0 !important;
	padding: 10px !important;
	text-align: center;
	white-space: nowrap;
}

.table>tbody>tr>td {
	border-top: 0 !important;
	padding: 10px !important;
	border-bottom: 1px solid #dfe6ec !important;
	font-size: 12px;
	color: #192236;
	line-height: 23px !important;
	text-align: center;
	white-space: nowrap;
}

.allModal {
	position: fixed;
	top: 0;
	right: -100%;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: flex-end;
	align-items: center;
	transition: right 0.3s ease;
}

.allModal-content {
	background: #f6f6f6;
	color: #192236;
	width: 800px;
	height: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	background-color: #FFFFFF;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2), 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
	box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2), 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
	overflow: hidden;
	outline: 0;
}

.allModal-header {
	padding: 0;
	margin: 0;
	height: 40px;
	line-height: 45px;
	font-size: 14px;
	background: #FFFFFF;
	color: #192236;
	font-weight: 600;
	text-indent: 12px;
	border-bottom: 1px solid #e0e1e6;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.allModal-title {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
}

.modalCloseBtn {
	width: 28px;
	height: 100%;
	text-align: center;
	cursor: pointer;
	line-height: normal;
	font-size: 16px;
	color: #ccc;
	background-color: transparent;
}

.allModal-body {
	padding-bottom: 46px;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	overflow: auto;
}

.allModal-info {
	width: 100%;
	padding: 24px;
	background: #FFF;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.allModal-footer {
	width: 800px;
	height: 45px;
	line-height: 45px;
	position: absolute;
	bottom: 0;
	right: 0;
	text-align: right;
	border-top: 1px solid #e0e1e6;
	background: #FFFFFF;
}

.btn-default.active,
.btn-default:active,
.open>.dropdown-toggle.btn-default {
	color: #1682e6 !important;
	border-color: #1682e6 !important;
	background-color: #fff !important;
	outline: none !important;
	border-color: #1682e6 !important;
}

.btn-default:hover {
	color: #1682e6 !important;
	border-color: #1682e6 !important;
	background-color: #fff !important;
	outline: none !important;
	border-color: #1682e6 !important;
}

.btn-default.focus,
.btn-default:focus {
	color: #1682e6 !important;
	border-color: #1682e6 !important;
	background-color: #fff !important;
	outline: none !important;
	border-color: #1682e6 !important;
}

.btn-primary.active,
.btn-primary:active,
.open>.dropdown-toggle.btn-primary {
	background: #1682e6 !important;
	border-color: #1682e6 !important;
	background-color: #1682e6 !important;
	color: #FFFFFF !important;
	outline: none !important;
}

.btn-primary.focus,
.btn-primary:focus {
	background: #1682e6 !important;
	border-color: #1682e6 !important;
	background-color: #1682e6 !important;
	color: #FFFFFF !important;
	outline: none !important;
}

.titleClass {
	padding: 10px 0;
	margin-bottom: 24px;
	border-bottom: 1px solid #e0e1e6;
	font-size: 16px;
	color: #333;
	letter-spacing: 0;
	font-weight: 500;
	padding-left: 12px;
	overflow: hidden;
	position: relative;
}
.ywsldh{
	font-size: 16px;
	color: #333;
	letter-spacing: 0;
}


.titleClass span {
	display: inline-block;
	width: 4px;
	height: 22px;
	position: absolute;
	left: 0;
	background: #1E92F0;
	border-radius: 2px;
}

.titleClass .addImg {
	display: inline-block;
	width: 22px;
	height: 22px;
	vertical-align: bottom;
	cursor: pointer;
}

.allModal-info .form-group {
	height: 34px!important;
	margin-bottom: 22px !important;
}

.allModal-info .row {
	margin-bottom: 18px;
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.allModal-info .myTextArea {
	width: 100% !important;
	padding: 5px 15px !important;
}
.form-group .myTextArea {
	width: 100% !important;
	padding: 5px 15px !important;
}

.uploadBox {
	font-size: 14px;
	color: #333;
	font-weight: 800;
	margin-bottom: 24px;

}

.uploadBox .uploadName {
	float: left;
	line-height: 24px;
	margin-right: 10px;
}

.btn.mini {
	padding: 3px 10px !important;
	display: inline-block;
	position: relative;
}

.myUpload {
	width: 70px;
	height: 25px;
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
	cursor: pointer;
}
.uploadTit{
	margin-bottom: 12px;
}
.uploadList {
	overflow: hidden;
	width: 100%;
	margin-top: 18px !important;
}

.lstInfoBox {
	background: #f5f7fa;
	padding: 5px 10px;
	box-sizing: border-box;
	margin: 5px 0;
	cursor: pointer;
}

.fileInfoName {
	display: inline-block;
	font-size: 12px;
	color: #333;
	margin-left: 5px;
	width: 225px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	vertical-align: text-bottom;
}

.btnList {
	float: right;
}

.btnList i {
	margin-left: 5px;
	color: #192236;
}
.sellerBox {
	border: 1px solid #e0e1e6;
	padding-top: 12px;
	margin: 0 auto !important;
	margin-bottom: 24px !important;
	border-radius: 4px;
	position: relative;
	overflow: hidden;
  }
  .hxBox {
	margin-bottom: 0!important;
  }
  .hxBox .form-control{
	width: 95% !important;
  }
  .hxBox .col-sm-6 .form-control{
	width: 72% !important;
  }
  .myPagination{
	text-align: right;
	margin: 16px;
  }
  .pagination{
	margin: 0!important;

  }
  .myPagination span{
	cursor: pointer;
  }
  .myModalBody{
	height: 200px!important;
	position: fixed!important;
	top: 0!important;
	right: 0!important;
	bottom: 0!important;
	left: 0!important;
	margin: auto!important;
  }
  .myMsgBody{
	height: 200px!important;
	position: fixed!important;
	top: 0!important;
	right: 0!important;
	bottom: 0!important;
	left: 0!important;
	margin: auto!important;
	width: 320px!important;
  }
  .myMsgBody .modal-body{
	padding: 20px;
	text-align: center;
  }
  .myMsgBody .modal-header{
	border-bottom: 0;
  }
  .myMsgBody .modal-footer{
	border-top: 0;
  }
  #previewImgModal{
	z-index: 9999!important;
  }
  #previewImg{
	width: 100%;
  }
  #previewImgModal .modal-content{
	height: 92vh;
	position: relative;
  }
  #previewImgModal .modal-body{
	width: 100%;
	height: 80vh;
	position: absolute;
	overflow: hidden;
    overflow-y: scroll;
  }

  #previewImgModal .modal-footer{
	height: 60px;
    position: absolute;
    text-align: right;
    border-top: 1px solid #e5e5e5;
    bottom: 0;
    right: 0;
    left: 0;
  }


  /* new add */
  ::-webkit-scrollbar {
		width: 12px;
		height: 12px;
	}
	::-webkit-scrollbar-thumb {
		border-radius: 12px;
		border: 4px solid rgba(0, 0, 0, 0);
		box-shadow: 4px 4px 0 0 #cdcdcd inset;
	}
	#previewPdf{
		height: 100%;
	}
	.infoTable table {
        width: 100%;
        border-collapse: collapse;
    }
    .infoTable table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
		font-size: 12px;
		min-width: 50px;
    }

    /* .infoTable table tr:nth-child(even) {
        background-color: #f2f2f2;
    } */

    .infoTable table tr:hover {
        background-color: #ddd;
    }

    .infoTable table th {
        background-color: #f2f2f2;
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
		font-size: 12px;
    }

    .infoTable1 table td:first-child,
    .infoTable1 table th:first-child {
        width: 25%;
    }

    .infoTable1 table td:not(:first-child),
    .infoTable1 table th:not(:first-child) {
        width: 25%;
    }


	/* 弹窗遮罩层 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 99;
		display: none;
	}

	.fjModal .modal-content {
		background: white;
		width: 80%;
		min-width: 800px;
		border-radius: 4px;
		display: flex;
		flex-direction: column;
		height: 100%;
		max-height: 80vh;
		border: none;
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		margin: auto;
	}

	.fjModal .modal-header {
		display: flex;
		align-items: center;
		background-color: #2789EE;
		padding: 8px 15px;
		color: #fff;
		font-size: 14px;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
	}
	
	.fjModal .modal-header h3 {
		margin: 0;
		font-size: 14px;
		font-weight: normal;
	}
	
	.close-btn {
		font-size: 20px;
		cursor: pointer;
		margin-left: auto;
	}
	
	.attachment-table-box{
		flex: 1;
		overflow-y: auto;
		padding: 0 20px;
		padding-top: 0;
	}
	.attachment-table {
		width: 100%;
		border-collapse: collapse;
		text-align: center;
	}
	.attachment-table thead {
		position: sticky;
		top: -1px;
		background-color: white;
	}

	.attachment-table th,
	.attachment-table td {
		border: 1px solid #ddd;
		padding: 8px;
		text-align: center;
		font-size: 12px;
		white-space: nowrap;
	}

	
	.action-buttons {
		display: flex;
		justify-content: center;
		padding: 20px 0;
		background: white;
		border-top: 1px solid #e0e1e6;
	}
	
	.action-buttons button {
		padding: 6px 16px;
		margin: 0 10px; /* 按钮间间距 */
		border: 1px solid #ddd;
		border-radius: 4px;
		cursor: pointer;
	}

	.confirm-btn {
		background: #2789EE;
		color: white;
		border-color: #2789EE;
	}

	.filter-upload-row {
		display: flex;
		align-items: center;
		padding: 20px;
	}
	
	.filter-container {
		display: flex;
		align-items: center;
		margin-right: 20px;
	}
	
	.filter-label {
		font-size: 14px;
		color: #333;
		margin-right: 10px;
	}
	
	.filter-select {
		width: 180px;
		padding: 8px 12px;
		font-size: 14px;
		border: 1px solid #ccc;
		border-radius: 4px;
		background-color: #fff;
		cursor: pointer;
		transition: border-color 0.3s ease;
	}
	
	.filter-select:focus {
		outline: none;
	}
	
	.upload-button {
		background: #1890ff;
		color: white;
		padding: 8px 16px;
		border-radius: 4px;
		cursor: pointer;
		width: auto;
	}

	.upload-button input[type="file"] {
		display: none;
	}
	.tableBox {
		width: 1152px;
		overflow-x: auto;
	}
	.table {
		width: 100%;
		border-collapse: collapse;
	}
	.table th.fixed-column{
		position: sticky;
		right: 0;
		background-color: #2789EE;
		z-index: 2; /* 确保固定列在其他列之上 */
	}
	.table td.fixed-column {
		position: sticky;
		right: 0;
		background-color: white; /* 确保固定列背景色与表格背景色一致 */
		z-index: 1;
	}
	.table th.fixed-left-column{
		position: sticky;
		left: 0;
		background-color: #2789EE;
		z-index: 2; /* 确保固定列在其他列之上 */
	}
	.table td.fixed-left-column {
		position: sticky;
		left: 0;
		background-color: white; /* 确保固定列背景色与表格背景色一致 */
		z-index: 1;
	}
	

	.tableBox2{
		width: 96%;
		margin: 0 auto;
	}
	.table .myThead2{
		background-color: #CDE3FB!important;
		color: #192236;
		font-size: 12px; 
	}
	.myThead2 th.fixed-column{
		right: 0;
		background-color: #CDE3FB!important;
	}
	.myThead2 th.fixed-left-column{
		background-color: #CDE3FB!important;
	}

	.myThead2 {
		position: sticky;
		top: -1px;
		background-color: white;
		z-index: 8;
	}

	.layui-tab{
		margin: 0;
	}
	.layui-tab-brief>.layui-tab-title .layui-this{
		color: #2789EE;
		font-weight: 500;
	}
	.layui-tab-brief>.layui-tab-more li.layui-this:after, .layui-tab-brief>.layui-tab-title .layui-this:after{
		border-bottom: 3px solid #2789EE;
	}
	.form-inline .filter-select{
		width: 170px;
		padding: 6px 12px;
	}
	.layui-tab-content{
		padding: 24px 0;
	}
	.layui-form-label{
		padding: 7px 15px;
	}
	.layui-input, .layui-select{
		height: 34px;
	}
	.layui-input-block.textarea{
		width: 80%;
		margin-left: 80px;
	}
	.deForm .row{
		margin: 0;
	}
	.deForm .layui-form-label{
		width: 126px;
	}
	.deForm .layui-input-block.textarea{
		width: 86%;
		margin-left: 126px;
	}
	.zzrwfkModal .layui-form-label{
		width: 126px;
	}

	.subIpt label{
		width: 87px!important;
		padding-right: 0!important;
	}
	.subIpt .filter-label{
		font-size: 12px!important;
		margin-right: 0!important;
	}
	.layui-form-item .layui-input-inline {
		float: left;
		width: 190px;
		margin-right: 10px;
	}