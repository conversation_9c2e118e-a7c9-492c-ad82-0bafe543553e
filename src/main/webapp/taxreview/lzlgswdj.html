<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="Cache-Control" CONTENT="no-cache">
	<meta http-equiv="Pragma" content="no-cache">
	<meta name="format-detection" content="telephone=no" />
	<title>漏征漏管税务登记</title>
	<link rel="stylesheet" href="css/bootstrap.min.css" />
	<link rel="stylesheet" href="./js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/reset.css" />
	<style>
		* {
			box-sizing: border-box
		}

		.main {
			background-color: #f2f2f2;
			width: 100%;
			min-width: 1200px;
			overflow: hidden;
			min-height: 100vh;
		}

		.myContainer {
			width: 1200px;
			margin: 0 auto;
			padding: 24px;
			background-color: #FFFFFF;
			min-height: 100vh;
		}

		.seachBox {
			border-bottom: 1px solid #e0e1e6;
		}

		.btnBox {
			text-align: right;
			height: 60px;
			line-height: 60px;
		}

		.myThead {
			background-color: #2789EE;
			color: white;
			font-size: 14px;
		}

		.myBtn {
			padding: 6px 15px !important;
			margin-right: 10px;
		}

		.rightBtn {
			margin-right: 24px;
		}
	</style>
</head>
<script src="./js/jquery-3.7.1.min.js"></script>
<script src="./js/bootstrap.min.js"></script>
<script src="./js/layui/layui.js"></script>
<script src="./js/lzlgswdj.js"></script>
<script src="./js/TranscendSign.js"></script>

<body>
<div class="main">
	<div class="myContainer">
		<div class="seachBox">
			<form class="form-inline">
				<div class="row">
					<div class="col-sm-3 col-md-3 col-lg-3">
						<div class="form-group">
							<label for="dwzcmc">单位注册名称</label>
							<input type="text" class="form-control" id="dwzcmc" placeholder="请输入">
						</div>
					</div>
					<div class="col-sm-3 col-md-3 col-lg-3">
						<div class="form-group">
							<label for="tyshxydm">统一社会信用代码</label>
							<input type="text" class="form-control" id="tyshxydm" placeholder="请输入">
						</div>
					</div>
					<div class="col-sm-3 col-md-3 col-lg-3">
						<div class="form-group">
							<label for="fddbr">法定代表人</label>
							<input type="text" class="form-control" id="fddbr" placeholder="请输入">
						</div>
					</div>
					<div class="col-sm-3 col-md-3 col-lg-3">
						<div class="form-group">
							<label for="kzztdjlx">课征主体登记类型</label>
							<select id="kzztdjlx" class="filter-select">
								<option value="">全部</option>
								<option value="企业">企业</option>
								<option value="个体">个体</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12 col-md-12 col-lg-12" style="text-align: right;">
						<!-- <button type="button" class="btn btn-default">重置</button> -->
						<button onclick="search(1)" type="button" class="btn btn-primary">查询</button>
					</div>
				</div>
			</form>
		</div>
		<div class="btnBox">
			<button onclick="location.href='/clf/sfzgzxxt.html'"
					class="layui-btn layui-btn-primary layui-border-blue midGoback">返回首页</button>
			<button onclick="dcFn()" type="button" class="btn btn-primary">导出</button>
			<button onclick="djrwfkAllFn()" type="button" class="btn btn-primary">登记任务反馈</button>
		</div>
		<div class="tableBox">
			<table class="table tableqsList table-hover">
				<thead class="myThead">
				<tr>
					<th class="fixed-left-column"><input type="checkbox" id="tableqs" onclick="checkAll()" />
					</th>
					<th>序号</th>
					<th>统计日期</th>
					<th>单位注册名称</th>
					<th>统一社会信用代码</th>
					<th>纳税人状态</th>
					<th>注册地</th>
					<th>经营范围</th>
					<th>法定代表人</th>
					<th>联系方式</th>
					<th>课征主体登记类型</th>
					<th>员工数量</th>
					<th>营业期限</th>
					<th>仓储物品</th>
					<th>主管税务科所</th>
					<th>街道乡镇</th>
					<th>税收管理员</th>
					<th>登记任务下发日期</th>
					<th>登记任务完成日期</th>
					<th>登记任务状态</th>
					<th>是否进行税务登记</th>
					<th>登记结果</th>
					<th>备注</th>
					<th class="fixed-column">操作</th>
				</tr>
				</thead>
				<tbody id="dataList">

				</tbody>
			</table>
		</div>
		<nav aria-label="Page navigation" class="myPagination">
			<ul class="pagination" id="pagination"></ul>
		</nav>
	</div>
	<!-- 登记任务反馈 -->
	<div class="modal-mask fjModal djrwfkModal">
		<div class="modal-content" style="width:800px!important;max-height: 400px!important;">
			<div class="modal-header">
				<h3>登记任务反馈</h3>
				<span class="close-btn closeFjglModal" onclick="closeModal()">×</span>
			</div>
			<div class="modal-body" style="flex: 1;">
				<form class="layui-form zzrwfkModal">
					<div class="row" style="margin-top: 20px;">
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">登记任务完成日期</label>
									<div class="layui-input-inline">
										<input type="text" class="layui-input" id="djrwwcrq" placeholder="请选择">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">登记任务状态</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="djrwzt" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row" style="margin-bottom: 40px;">
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">登记结果</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="djjg" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
						<div class="col-sm-6 col-md-6 col-lg-6">
							<div class="layui-form-item">
								<div class="layui-inline">
									<label class="layui-form-label">登记人员</label>
									<div class="layui-input-inline">
										<input type="text" style="padding-left: 0!important;" class="layui-input"
											   id="djry" placeholder="请输入">
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="action-buttons">
				<button class="btn-default" onclick="closeModal()">取消</button>
				<button class="confirm-btn" onclick="djrwfk()">确认</button>
			</div>
		</div>
	</div>
</div>
</body>

</html>