<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Pragma" content="no-cache">
    <meta name="format-detection" content="telephone=no" />
    <title>存量房税费审核</title>
    <link rel="stylesheet" href="./taxreview/js/layui/css/layui.css" />
    <link rel="stylesheet" href="./taxreview/css/reset.css" />
    <style>
        .content {
            width: 100%;
            display: flex;
        }

        #treeBox {
            width: 220px;
            height: 100vh;
            border-right: 1px solid #e6e6e6;
            padding-left: 20px;
        }

        .buttonboxTop {
            text-align: center;
            flex: 1;
        }

        .buttonbox {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            color: #000000;
            cursor: pointer;
            display: inline-block;
            width: 200px;
            height: 180px;
            float: left;
        }

        .imgbutton {
            width: 100px;
            height: 100px;
            border-radius: 5px;
            margin: 20px auto 10px auto;
        }

        .layui-nav {
            background-color: #10375F;
        }

        .layui-nav-itemed>.layui-nav-child {
            background-color: rgb(12, 39, 78) !important;
        }

        .layui-nav-tree .layui-nav-bar {
            background-color: #1E92F0;
        }

        .layui-nav-tree .layui-nav-child dd.layui-this,
        .layui-nav-tree .layui-nav-child dd.layui-this a,
        .layui-nav-tree .layui-this,
        .layui-nav-tree .layui-this>a,
        .layui-nav-tree .layui-this>a:hover {
            background-color: #1E92F0;
        }

        .layui-nav .layui-nav-item .layui-nav-child a {
            padding-left: 40px;
        }
    </style>
</head>

<body>
<div class="content">
    <div id="treeBox"></div>
    <!-- <ul id="treeBox" class="layui-nav layui-nav-tree">
        <li class="layui-nav-item layui-nav-itemed">
            <a href="javascript:;">征管在线协同</a>
            <dl class="layui-nav-child">
                <dd><a href="javascript:;">欠税清册</a></dd>
                <dd><a href="javascript:;">阻止大额欠税出境</a></dd>
                <dd><a href="javascript:;">漏征漏管</a></dd>
                <dd><a href="javascript:;">存量房交易</a></dd>
                <dd><a href="javascript:;">附件管理档案</a></dd>
                <dd><a href="javascript:;">所长清册</a></dd>
            </dl>
        </li>
    </ul> -->
    <div class="buttonboxTop">
        <div onclick="location.href='/clf/taxreview/qsgl.html?index=1'" class="buttonbox btn1">
            <div><img src="./taxreview/images/qs1.png" class="imgbutton"></div>
            <div>欠税清单</div>
        </div>
        <div onclick="location.href='/clf/taxreview/qsgl.html?index=2'" class="buttonbox btn1">
            <div><img src="./taxreview/images/qs2.png" class="imgbutton"></div>
            <div>欠税统计</div>
        </div>
        <div onclick="location.href='/clf/taxreview/qszzrwcl.html?index=1'" class="buttonbox btn1">
            <div><img src="./taxreview/images/qs3.png" class="imgbutton"></div>
            <div>追征企业清单</div>
        </div>
        <div onclick="location.href='/#'" class="buttonbox btn1">
            <div><img src="./taxreview/images/qs4.png" class="imgbutton"></div>
            <div>欠税一户式</div>
        </div>

        <div onclick="location.href='/clf/taxreview/qsgl.html?index=3'" class="buttonbox btn2">
            <div><img src="./taxreview/images/deqs1.png" class="imgbutton"></div>
            <div>大额欠税阻止出境</div>
        </div>
        <div onclick="location.href='/clf/taxreview/qsgl.html?index=4'" class="buttonbox btn2">
            <div><img src="./taxreview/images/deqs2.png" class="imgbutton"></div>
            <div>阻止出境人员</div>
        </div>
        <div onclick="location.href='/clf/taxreview/qszzrwcl.html?index=2'" class="buttonbox btn2">
            <div><img src="./taxreview/images/deqs3.png" class="imgbutton"></div>
            <div>大额欠税阻止出境企业上报</div>
        </div>

        <div onclick="location.href='/clf/taxreview/lzlg.html'" class="buttonbox btn3">
            <div><img src="./taxreview/images/lzlg1.png" class="imgbutton"></div>
            <div>漏征漏管税局</div>
        </div>
        <div onclick="location.href='/clf/taxreview/lzlgswdj.html'" class="buttonbox btn3">
            <div><img src="./taxreview/images/lzlg2.png" class="imgbutton"></div>
            <div>漏征漏管税务登记税所</div>
        </div>

        <div onclick="location.href='/clf/taxreview/index.html'" class="buttonbox btn4">
            <div><img src="./taxreview/images/clf1.png" class="imgbutton"></div>
            <div>存量房交易个人</div>
        </div>
        <div onclick="location.href='/clf/taxreview/corporate.html'" class="buttonbox btn4">
            <div><img src="./taxreview/images/clf2.png" class="imgbutton"></div>
            <div>存量房交易企业</div>
        </div>
    </div>
</div>

<script src="./taxreview/js/layui/layui.js"></script>
<script>
    var layer = null;
    layui.use('layer', function () {
        layer = layui.layer;
    });
    var tree = layui.tree;
    tree.render({
        elem: '#treeBox',
        data: [
            {
                title: '征管在线协同',
                spread: true,
                children: [
                    {
                        title: '欠税清册',
                        name: 'btn1',
                    },
                    {
                        title: '阻止大额欠税出境',
                        name: 'btn2',
                    },
                    {
                        title: '漏征漏管',
                        name: 'btn3',
                    },
                    {
                        title: '存量房交易',
                        name: 'btn4',
                    },
                    {
                        title: '附件管理档案',
                        href: '/clf/taxreview/fjglda.html',
                    },
                    {
                        title: '所长清册',
                        href: '/clf/taxreview/szscindex.html',
                    },
                ]
            }
        ],
        click: function (obj) {
            var data = obj.data;
            if (data.href) {
                location.href = data.href;
            } else if (data.name) {
                let buttonboxTop = document.querySelector('.buttonboxTop');
                let buttons = buttonboxTop.querySelectorAll('.buttonbox');
                buttons.forEach(function (button) {
                    if (button.classList.contains(data.name)) {
                        button.style.display = '';
                    } else {
                        button.style.display = 'none';
                    }
                });

            }
        },
    });

</script>
</body>