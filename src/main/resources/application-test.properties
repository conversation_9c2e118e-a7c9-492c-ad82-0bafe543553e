server.port=8080 
server.context-path=/clf/

#˰�Ѵ�����ƽ̨��ַ
esb_url=http://98.9.1.218/bondeWebService?wsdl
esb_method=requestService

#���Ͽ��ַ
syzlkUrl=http://98.11.153.207/bondeWebService/?wsdl
syzlkMethod=requestService

#�ļ��ϴ���ַ
filePath=/Users/<USER>/Downloads/tax

#�Ϲ���cas���� casEnabled�Ƿ�����CAS
spring.cas.casEnabled=false
spring.cas.ignorePattern=/*/load/wbsj
spring.cas.sign-out-filters=/logout
spring.cas.auth-filters=/*
spring.cas.validate-filters=/*
spring.cas.request-wrapper-filters=/*
spring.cas.assertion-filters=/*
spring.cas.cas-server-login-url=http://ysccas.cqsw.tax.cn:8201/login
spring.cas.cas-server-url-prefix=http://ysccas.cqsw.tax.cn:8201
spring.cas.redirect-after-validation=true
spring.cas.use-session=true
spring.cas.server-name=http://127.0.0.1:8079
spring.cas.encoding=utf-8
spring.cas.jrlx=2
spring.cas.gzlm=com.aisino.cq.cas.CasGzConfigur
spring.cas.gzffm=CasSessionConfigur

flzl.view.url=http://127.0.0.1:8079

#数据库的配置
spring.datasource.url=****************************************
spring.datasource.username=DEV01
spring.datasource.password=Cx_17112
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
#spring.datasource.type=com.alibaba.druid.pool.DruidConfig   # 指定数据源为druid
# 连接池的配置信息
# 初始化大小，最小，最大
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.maxActive=20
# 配置获取连接等待超时的时间
spring.datasource.druid.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.druid.minEvictableIdleTimeMillis=300000
#用来检测连接是否有效的sql，要求是一个查询语句。
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.druid.filters=stat,wall,log4j
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.druid.connectionProperties=druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
