server.port=8079 
server.context-path=/clf/

spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=100MB

#˰�Ѵ�����ƽ̨��ַ
esb_url=http://**********/bondeWebService?wsdl
esb_method=requestService

#���Ͽ��ַ
syzlkUrl=http://************:8001/bondeWebService?wsdl
syzlkMethod=requestService

#�ļ��ϴ���ַ
filePath=/home/<USER>/app/files

#�Ϲ���cas���� casEnabled�Ƿ�����CAS
spring.cas.casEnabled=true 
spring.cas.ignorePattern=/*/load/wbsj
spring.cas.sign-out-filters=/logout
spring.cas.auth-filters=/*
spring.cas.validate-filters=/*
spring.cas.request-wrapper-filters=/*
spring.cas.assertion-filters=/*
spring.cas.cas-server-login-url=http://cas.cqsw.tax.cn/login
spring.cas.cas-server-url-prefix=http://cas.cqsw.tax.cn
spring.cas.redirect-after-validation=true
spring.cas.use-session=true
spring.cas.server-name=http://dsjssfxhx.cqsw.tax.cn
spring.cas.encoding=utf-8
spring.cas.jrlx=2
spring.cas.gzlm=com.aisino.cq.cas.CasGzConfigur
spring.cas.gzffm=CasSessionConfigur