package com.aisino.cq.fjglda.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ZipUtil;
import com.aisino.cq.clf.controller.ClfController;
import com.aisino.cq.clf.controller.ClfQyController;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.utlis.EncodedTools;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.aisino.cq.clf.util.RequestUtil.callFile;

@RestController
@RequestMapping("fjglda")
public class FjgldaController {
    private static final Logger log = LoggerFactory.getLogger(FjgldaController.class);
    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Resource
    private ClfController clfController;
    @Resource
    private ClfQyController clfQyController;

    @Value("${filePath}")
    private String filePath;

    //档案新增sql序号
    private static final String daInsertSqlxh = "30000055";
    //档案查询sql序号
    private static final String daQuerySqlxh = "30000057";
    //档案更新sql序号
    private static final String daUpdateSqlxh = "30000059";
    //明细新增sql序号
    private static final String mxInsertSqlxh = "30000056";
    //明细查询sql序号
    private static final String mxQuerySqlxh = "30000058";
    //明细查询数量sql序号
    private static final String mxQueryCountSqlxh = "30000098";
    //明细更新sql序号
    private static final String mxUpdateSqlxh = "30000060";
    //存量房个人附件查询
    private static final String grFjQuerySqlxh = "30000017";
    //存量房企业附件查询
    private static final String qyFjQuerySqlxh = "30000049";
    private static final String fjywlx = "fjglda";
    @Resource(name = "sfzzThreadPool")
    private Executor executor;


    @RequestMapping("isAlive")
    @ResponseBody
    public Object isAlive(HttpServletRequest request){
        CommonResult rtn = new CommonResult();
        rtn.setSuccess("");
        return rtn;
    }

    @RequestMapping("getLoginUser")
    @ResponseBody
    public Object getLoginUser(HttpServletRequest request){
        return request.getSession().getAttribute("usercode");
    }

    /**
     * function 获取附件管理档案列表信息
     * 传参 pageSize、pageNo、ywbh（业务编号）、ywlx（业务类型）、gdnd（归档年度）、gdrq（归档日期）、blry（办理人员）
     */
    @RequestMapping("getList")
    @ResponseBody
    public Object getList(HttpServletRequest request){
        log.info("获取列表信息");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        //传参
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String ywbh = param.get("ywbh");
        String ywlx = param.get("ywlx");
        String gdnd = param.get("gdnd");
        String gdrq = param.get("gdrq");
        String blry = param.get("blry");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"ywbh",ywbh,"int");
        esbService.addParam(paramList,"ywlx",ywlx,"string");
        esbService.addParam(paramList,"gdnd",gdnd,"string");
        esbService.addParam(paramList,"gdrq",gdrq,"string");
        esbService.addParam(paramList,"blry",blry,"string");
        return esbService.queryEsb(daQuerySqlxh, pageNo, pageSize, "0", "true", paramList);
    }

    /**
     * 根据主表id 获取所有附件信息
     * 传参 uuid （档案表的uuid）
     * @return
     */
    @RequestMapping("fileList")
    @ResponseBody
    public Object fileList(HttpServletRequest request){
        log.info("获取附件列表");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String zbuuid = param.get("zbuuid");
        JSONObject ywsj = new JSONObject();
        if(StringUtils.isNotBlank(pageNo)){
            ywsj.put("pageNo",pageNo);
        }
        if(StringUtils.isNotBlank(pageSize)){
            ywsj.put("pageSize",pageSize);
        }
        //根据主表id查询档案主表，
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
        CommonResult rtn = esbService.queryEsb(daQuerySqlxh, "1", "10", "0", "false", paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                String ywlx = jo.getString("ywlx");
                String ywid = jo.getString("ywid");
                if(StringUtils.equals("存量房个人",ywlx)){
                    //1、如果是存量房个人则将取ywid为ywbh的值
                    ywsj.put("ywbh",ywid);
                    ywsj.put("ywlx","clfgr");
                    rtn = callFile("flzllb",ywsj);
                }else if(StringUtils.equals("存量房企业",ywlx)){
                    //2、如果是存量房企业则取ywid并按-分隔做为ywbh的值，分别查询主表和房屋表的附件
                    String[] ywbhArr = ywid.split("-");
                    JSONArray ywbhJsonArray = new JSONArray();
                    ywbhJsonArray.add(ywbhArr[0]);
                    ywbhJsonArray.add(ywbhArr[1]);
                    ywsj.put("ywbhList",ywbhJsonArray);
                    ywsj.put("ywlx","clfqy");
                    rtn = callFile("flzllb",ywsj);
                }else {
                    //3、如果是股权变更和征纳互动则直接查询附件即可
                    ywsj.put("ywbh",zbuuid);
                    ywsj.put("ywlx",fjywlx);
                    rtn = callFile("flzllb",ywsj);
                }
            }
        }
        return rtn;
    }

    /**
     * function 文件下载（有用）
     * 传参uuid （明细表的uuid）
     */
    @RequestMapping("download")
    @ResponseBody
    public void download(String uuid,HttpServletResponse response){
        log.info("文件下载"+uuid);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",fjywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段（字符串）
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String fileName = obj.getString("wjm");
                    String wjurl = obj.getString("wjurl");
                    String downloadName = new String(fileName.getBytes("gb2312"),"ISO8859-1");
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-Disposition","attachment;filename=\""+downloadName);
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                        byte[] b = IoUtil.readBytes(inputStream);
                        out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 文件预览
     * 传参uuid（明细表的uuid）
     */
    @RequestMapping("preview")
    @ResponseBody
    public void preview(String uuid,HttpServletResponse response){
        log.info("文件预览"+uuid);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",fjywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String type = obj.getString("wjlx");
                    String wjurl = obj.getString("wjurl");
                    if("pdf".equalsIgnoreCase(type)){
                        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                    }else if("png".equalsIgnoreCase(type)){
                        response.setContentType(MediaType.IMAGE_PNG_VALUE);
                    }else if("jpg".equalsIgnoreCase(type)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("jpeg".equalsIgnoreCase(type)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("gif".equalsIgnoreCase(type)){
                        response.setContentType(MediaType.IMAGE_GIF_VALUE);
                    }
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                        byte[] b = IoUtil.readBytes(inputStream);
                        out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 删除附件（删除只会删除手动归档的股权变更和征纳互动，自动归档的不会删除，不用考虑删除原业务表数据）
     * 传参uuid （明细表的uuid）
     */
    @RequestMapping("real/fj/delete")
    @ResponseBody
    public Object delete(String uuid,HttpServletRequest request){
        log.info("删除附件");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult("00","success");
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",fjywlx);
            result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段（字符串）
                JSONArray ja = (JSONArray) result.getData();
                if(ja.size() > 0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String gdzt = obj.getString("gdzt");
                    String zldjxh = obj.getString("zldjxh");
                    String gldh = obj.getString("gldh");
                    String ywbh = obj.getString("ywbh");
                    if("已归档".equals(gdzt)){
                        log.info("当前资料已归档---删除归档信息----");
                        result = dzzlkService.zlyc(gldh,zldjxh);
                    }
                    if(result.isSuccess()){
                        //调用附件删除接口，也将归档附件删掉了，现在是同一个附件
                        result = callFile("flzlsc",ywsj);
                        //先查目前明细表数据，然后更新本地档案系统主表附件数量
                        if(result.isSuccess()){
                            ywsj.clear();
                            ywsj.put("ywbh",ywbh);
                            ywsj.put("ywlx",fjywlx);
                            result = callFile("flzlsl", ywsj);
                            if(result.isSuccess()){
                                String sl = (String) result.getData();
                                paramList.clear();
                                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                                paramList.add(esbService.addParam("fjsl",sl,"string"));
                                paramList.add(esbService.addParam("uuid",ywbh,"string"));
                                result = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail(e.getMessage());
        }
        return result;
    }
    /**
     *function 暂存
     * 传参 ywlx（业务类型）、ywbh（业务编号）、fjuuids（附件uuid的集合）、nsrmc（纳税人名称）、nsrsbh（纳税人识别号）、zbuuid（档案表的uuid，新增时不传，修改时要传）
     */
    @RequestMapping("/real/fj/zc")
    @ResponseBody
    public Object zc(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        userCode = StringUtils.isBlank(userCode) ? "1" : userCode;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("暂存"+param);
        //本次新增的附件uuid集合
        String fjuuids = param.get("fjuuids");
        CommonResult rtn = new CommonResult("00","success");
        if(StringUtils.isNotBlank(fjuuids)){
            //暂存-生成档案主表数据
            rtn = czdasj(param, userCode, true,userName);
            if(rtn.isSuccess()){
                String zbuuid = param.get("zbuuid");
                //将fjuuid按逗号分隔，然后将每一项加到jsonArray中
                String[] fjuuidArr = StringUtils.split(fjuuids, ",");
                JSONArray fjbhJsonArray = new JSONArray();
                for (String fjuuid : fjuuidArr) {
                    fjbhJsonArray.add(fjuuid);
                }
                JSONObject ywsj = new JSONObject();
                ywsj.put("fjbhList",fjbhJsonArray);
                ywsj.put("xgrdm",userCode);
                ywsj.put("ywbh",zbuuid);
                ywsj.put("ywlx",fjywlx);
                //暂存-附件表关联到主表上
                rtn = callFile("flzlgx",ywsj);
                if(!rtn.isSuccess()){
                    rtn.setFail(rtn.getMsg());
                }
            }
        }
        return rtn;
    }

    /**
     * 操作档案主表数据
     * @param param  参数集合
     * @param userCode 用户编码
     * @param zcflag 是否暂存
     * @return
     */
    private CommonResult czdasj(Map<String,String> param,String userCode,boolean zcflag,String userName){
        //业务类型 股权变更、征纳互动
        String ywlx = param.get("ywlx");
        //业务编号
        String ywbh = param.get("ywbh");
        //附件uuid集合
        String fjuuids = param.get("fjuuids");
        //纳税人名称
        String nsrmc = param.get("nsrmc");
        //纳税人识别号
        String nsrsbh = param.get("nsrsbh");
        //主表uuid
        String zbuuid = param.get("zbuuid");
        String[] fjuuidArr = StringUtils.split(fjuuids, ",");
        List<Map<String,Object>> paramList = new ArrayList<>();
        if(StringUtils.isBlank(zbuuid)){
            //zbuuid为空时生成主表数据
            zbuuid =  UUID.randomUUID().toString().replaceAll("-", "");
            param.put("zbuuid",zbuuid);
            paramList.add(esbService.addParam("uuid",zbuuid,"string"));
            paramList.add(esbService.addParam("ywbh",ywbh,"string"));
            paramList.add(esbService.addParam("ywlx",ywlx,"string"));
            paramList.add(esbService.addParam("gdnd", "-","string"));
            paramList.add(esbService.addParam("gdrq", "-","string"));
            paramList.add(esbService.addParam("blry",StringUtils.isBlank(userName) ? "管理员" : userName,"string"));
            paramList.add(esbService.addParam("fjsl",String.valueOf(fjuuidArr.length),"string"));
            paramList.add(esbService.addParam("ywid","","string"));
            paramList.add(esbService.addParam("gdfs","手动归档","string"));
            paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
            paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
            paramList.add(esbService.addParam("gdzt",zcflag ? "未归档" : "已归档","string"));
            paramList.add(esbService.addParam("lrrdm",userCode,"string"));
            paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
            paramList.add(esbService.addParam("xgrdm",userCode,"string"));
            paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
            paramList.add(esbService.addParam("yxbz","Y","string"));
            return esbService.queryEsb(daInsertSqlxh,"1", "10","0","false",paramList);
        }else {
            //不为空时，先查询是否存在，存在更新，不存在生成
            paramList.clear();
            paramList.add(esbService.addParam("uuid",zbuuid,"string"));
            CommonResult rtn = esbService.queryEsb(daQuerySqlxh, "1", "10", "0", "false", paramList);
            if(rtn.isSuccess()){
                JSONArray data = (JSONArray) rtn.getData();
                if(data.size() > 0){
                    //查询当前附件数量并重新计算
                    JSONObject ywsj = new JSONObject();
                    ywsj.put("ywbh",zbuuid);
                    ywsj.put("ywlx",fjywlx);
                    rtn = callFile("flzlsl", ywsj);
                    if(rtn.isSuccess()){
                        int sl = (Integer) rtn.getData();
                        sl += fjuuidArr.length;
                        //这个是编辑页面，更新档案主表数据
                        paramList.clear();
                        String gdnd = String.valueOf(LocalDate.now().getYear());
                        String gdrq = LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss");
                        String gdzt = "已归档";
                        if(zcflag){
                            gdnd = "-";
                            gdrq = "-";
                            gdzt = "未归档";
                        }
                        paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                        paramList.add(esbService.addParam("fjsl",String.valueOf(sl),"string"));
                        paramList.add(esbService.addParam("ywlx",ywlx,"string"));
                        paramList.add(esbService.addParam("ywbh",ywbh,"string"));
                        paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
                        paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
                        paramList.add(esbService.addParam("gdzt",gdzt,"string"));
                        paramList.add(esbService.addParam("gdnd",gdnd,"string"));
                        paramList.add(esbService.addParam("gdrq",gdrq,"string"));
                        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                        rtn = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
                    }
                }else {
                    //这个是新增页面，使用传过来的zbuuid生成主表信息
                    paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                    paramList.add(esbService.addParam("ywbh",ywbh,"string"));
                    paramList.add(esbService.addParam("ywlx",ywlx,"string"));
                    paramList.add(esbService.addParam("gdnd", String.valueOf(LocalDate.now().getYear()),"string"));
                    paramList.add(esbService.addParam("gdrq", LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"),"string"));
                    paramList.add(esbService.addParam("blry",StringUtils.isBlank(userName) ? "管理员" : userName,"string"));
                    paramList.add(esbService.addParam("fjsl",String.valueOf(fjuuidArr.length),"string"));
                    paramList.add(esbService.addParam("ywid","","string"));
                    paramList.add(esbService.addParam("gdfs","手动归档","string"));
                    paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
                    paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
                    paramList.add(esbService.addParam("gdzt", "已归档","string"));
                    paramList.add(esbService.addParam("lrrdm",userCode,"string"));
                    paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
                    paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                    paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                    paramList.add(esbService.addParam("yxbz","Y","string"));
                    return esbService.queryEsb(daInsertSqlxh,"1", "10","0","false",paramList);
                }
            }
            return rtn;
        }
    }

    /**
     *function 归档（两个入口，一个在列表上，表示将该业务下未归档的附件全部归档；一个在附件上传里，表示将本次上传的数据归档）
     * 传参 zbuuid（档案表uuid）、fjuuids（要归档的附件uuid集合，附件上传里的归档必传）、nsrmc（纳税人名称）、nsrsbh（纳税人识别号）、ywlx（业务类型）、ywbh（业务编号）
     */
    @RequestMapping("/real/fj/gd")
    @ResponseBody
    public Object gd(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        CommonResult rtn = new CommonResult("00","");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("归档"+param);
        //本次新增的附件uuid集合
        String fjuuids = param.get("fjuuids");
        //主表uuid
        String zbuuid = param.get("zbuuid");
        if(StringUtils.isNotBlank(fjuuids)){
            if(StringUtils.isNotBlank(zbuuid)){
                //在编辑页面上归档,先把之前未归档的附件查出来进行归档即可，因为编辑页面上传的附件已经直接和主表绑定好了
                rtn = ywqbgd(zbuuid,userCode,userName);
            }else {
                //在新增页面上归档
                zbuuid = UUID.randomUUID().toString().replaceAll("-", "");
                rtn = dzzlk(param,zbuuid,userCode,userName);
            }
        }else if(StringUtils.isBlank(fjuuids) && StringUtils.isNotBlank(zbuuid)){
            //将该笔业务下所有未归档的附件全部归档
            rtn = ywqbgd(zbuuid,userCode,userName);
        }else{
            rtn.setFail("请选择要归档的附件！");
        }
        return rtn;
    }
    /**
     * 已经没用了
     *function 取消归档（编辑页面，可以勾选已归档的附件进行取消归档操作） 两步操作，第一步移除电子档案系统数据，第二步将本地明细表状态改为未归档
     * 传参 fjuuids（要取消归档的附件uuid集合）
     */
    @RequestMapping("/real/fj/qxgd")
    @ResponseBody
    public Object qxgd(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();
        log.info("取消归档"+param);
        //附件uuid集合
        String fjuuids = param.get("fjuuids");
        String uuids = Arrays.stream(fjuuids.split(","))
                .map(String::trim) //去除空格
                .map(part -> "'"+part+"'") //添加单引号
                .collect(Collectors.joining(","));
        paramList.add(esbService.addParam("uuids",uuids,"int"));
        //查询主表获取业务类型
        CommonResult rtn = esbService.queryEsb(mxQuerySqlxh, "1", "1000", "0", "false", paramList);
        if(rtn.isSuccess()){
            JSONArray mxJsonArray = (JSONArray)rtn.getData();
            if(mxJsonArray.size() > 0){
                for(int i=0;i<mxJsonArray.size();i++){
                    JSONObject mxJsonObject = mxJsonArray.getJSONObject(i);
                    String gdzt = mxJsonObject.getString("gdzt");
                    String zldjxh = mxJsonObject.getString("zldjxh");
                    String gldh = mxJsonObject.getString("gldh");
                    String uuid = mxJsonObject.getString("uuid");
                    if("已归档".equals(gdzt)){
                        log.info("当前资料已归档---删除归档信息----");
                        rtn = dzzlkService.zlyc(gldh,zldjxh);
                    }
                    if(rtn.isSuccess()){
                        //将本地档案系统附件删除
                        paramList.clear();
                        paramList.add(esbService.addParam("uuids",uuid,"string"));
                        paramList.add(esbService.addParam("gdzt","已删除","string"));
                        paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                        paramList.add(esbService.addParam("yxbz","N","string"));
                        rtn = esbService.queryEsb(mxUpdateSqlxh,"1", "10","0","false",paramList);
                    }
                }
            }
        }
        return rtn;
    }

    /**
     * 将该业务下全部未归档附件进行归档
     * @param zbuuid
     * @return
     */
    private CommonResult ywqbgd(String zbuuid,String userCode,String userName){
        //大致分为两类，一类是股权变更和征纳互动是手动归档的只需要处理归档表相关就可以了，一类是存量房个人、存量房企业和大额欠税是自动归档的，需要先找到业务表的相关数据再进行归档
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
        //查询主表获取业务类型
        CommonResult rtn = esbService.queryEsb(daQuerySqlxh, "1", "10", "0", "false", paramList);
        if(rtn.isSuccess()){
            JSONArray daJsonArray = (JSONArray)rtn.getData();
            JSONObject daJsonObject = daJsonArray.getJSONObject(0);
            String ywlx = daJsonObject.getString("ywlx");
            String ywid = daJsonObject.getString("ywid");
            String ywbh = daJsonObject.getString("ywbh");
            String nsrmc = daJsonObject.getString("nsrmc");
            String nsrsbh = daJsonObject.getString("nsrsbh");
            if(!EncodedTools.isFullContains("股权变更，征纳互动",ywlx)){
                //查询要归档的数据
                JSONObject ywsj = new JSONObject();
                ywsj.put("ywbh",zbuuid);
                ywsj.put("gdzt","未归档");
                ywsj.put("ywlx",fjywlx);
                rtn = callFile("flzllb",ywsj);
                if(rtn.isSuccess()){
                    List<String> fjuuidList = new ArrayList<>();
                    // 2. 获取data字段（字符串）
                    JSONArray ja = (JSONArray)rtn.getData();
                    if(ja.size() > 0){
                        for(int i=0;i<ja.size();i++){
                            JSONObject obj = ja.getJSONObject(i);
                            fjuuidList.add(obj.getString("uuid"));
                        }
                        String join = StringUtils.join(fjuuidList, ",");
                        Map<String,String> param = new HashMap<>();
                        param.put("nsrmc",nsrmc);
                        param.put("nsrsbh",nsrsbh);
                        param.put("ywlx",ywlx);
                        param.put("ywid",ywid);
                        param.put("ywbh",ywbh);
                        param.put("fjuuids",join);
                        rtn = dzzlk(param,zbuuid,userCode,userName);
                    }else {
                        rtn.setSuccess(null);
                    }
                }
            }else {
                //处理原业务表，将原业务表上未归档的数据进行归档
                if(StringUtils.equals("存量房个人",ywlx)){
                    //根据业务id查询不同表未归档的附件信息
                    List<String> fjidList = new ArrayList<>();
                    paramList.clear();
                    paramList.add(esbService.addParam("reciid",ywid,"string"));
                    paramList.add(esbService.addParam("gdzt","未归档","string"));
                    rtn = esbService.queryEsb(grFjQuerySqlxh,"1", "100","0","false",paramList);
                    if(rtn.isSuccess()){
                        JSONArray fjJsonArray = (JSONArray)rtn.getData();
                        if(fjJsonArray.size() > 0){
                            for(int i=0;i<fjJsonArray.size();i++){
                                JSONObject jsonObject = fjJsonArray.getJSONObject(i);
                                fjidList.add(jsonObject.getString("id"));
                            }
                        }
                    }
                    String fjids = StringUtils.join(fjidList, ",");
                    //将获取到的附件数据进行推送电子档案系统和本地归档
                    clfController.wjzlgd(ywid,fjids,userCode,userName);
                }else if(StringUtils.equals("存量房企业",ywlx)){
                    //根据业务id查询不同表未归档的附件信息
                    String qyuuid = ywid.split("-")[0];
                    String fwuuid = ywid.split("-")[1];
                    List<String> fjuuidList = new ArrayList<>();
                    paramList.clear();
                    paramList.add(esbService.addParam("zbuuid",qyuuid,"string"));
                    paramList.add(esbService.addParam("gdzt","未归档","string"));
                    rtn = esbService.queryEsb(qyFjQuerySqlxh,"1", "100","0","false",paramList);
                    if(rtn.isSuccess()){
                        JSONArray fjJsonArray = (JSONArray)rtn.getData();
                        if(fjJsonArray.size() > 0){
                            for(int i=0;i<fjJsonArray.size();i++){
                                JSONObject jsonObject = fjJsonArray.getJSONObject(i);
                                fjuuidList.add(jsonObject.getString("uuid"));
                            }
                        }
                    }
                    paramList.clear();
                    paramList.add(esbService.addParam("zbuuid",fwuuid,"string"));
                    paramList.add(esbService.addParam("gdzt","未归档","string"));
                    rtn = esbService.queryEsb(qyFjQuerySqlxh,"1", "100","0","false",paramList);
                    if(rtn.isSuccess()){
                        JSONArray fjJsonArray = (JSONArray)rtn.getData();
                        if(fjJsonArray.size() > 0){
                            for(int i=0;i<fjJsonArray.size();i++){
                                JSONObject jsonObject = fjJsonArray.getJSONObject(i);
                                fjuuidList.add(jsonObject.getString("uuid"));
                            }
                        }
                    }
                    String fjuuids = StringUtils.join(fjuuidList, ",");
                    //存量房企业进行文件资料归档（包含电子档案系统和本地档案系统）
                    rtn = clfQyController.wjzlgd(qyuuid, fwuuid, fjuuids, userCode,userName);
                }else if(StringUtils.equals("大额欠税",ywlx)){
                    //实际只有股权变更和征纳互动，下边的逻辑都用不到，暂时保留吧

                }else {
                    throw new RuntimeException("不支持的业务类型："+ywlx);
                }
            }
        }
        return rtn;

    }
    /**
     *function 归档
     * @param param 参数集合
     * @param zbuuid 档案表uuid
     */
    private CommonResult dzzlk(Map<String,String> param,String zbuuid,String userCode,String userName){
        //本次要归档的附件uuid集合
        String fjuuids = param.get("fjuuids");
        CommonResult result = new CommonResult();
        try{
            //关联单号
            String gldh = esbService.MD5(zbuuid);
            //登记序号
            String djxh= param.get("nsrsbh");
            //纳税人名称
            String nsrmc = param.get("nsrmc");
            //纳税人识别号
            String nsrsbh = param.get("nsrsbh");
            CommonResult initDzzlkRtn = dzzlkService.init(gldh,djxh,nsrsbh,nsrmc);
            if(initDzzlkRtn.isSuccess()){
                String[] uuids = StringUtils.split(fjuuids, ",");
                List<CompletableFuture<String>> futures = new ArrayList<>();
                for(String fjuuid : uuids){
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                        CommonResult rtn = new CommonResult();
                        List<Map<String,Object>> paramList = new ArrayList<>();
                        paramList.clear();
                        JSONObject ywsj = new JSONObject();
                        ywsj.put("fjbh",fjuuid);
                        ywsj.put("ywlx",fjywlx);
                        CommonResult commonResult = callFile("flzllb", ywsj);
                        if (commonResult.isSuccess()) {
                            //附件数据
                            JSONArray ja = (JSONArray)commonResult.getData();
                            if (ja.size() == 0) {
                                return "success";
                            }
                            JSONObject obj = ja.getJSONObject(0);
                            String uuid = obj.getString("uuid");
                            String wjlx = obj.getString("wjlx");
                            String wjzllx = obj.getString("wjzllx");
                            String gdzt = obj.getString("gdzt");
                            String wjurl = obj.getString("wjurl");
                            if("已归档".equals(gdzt)){
                                return "success";
                            }
                            String zllxdm = "";
                            wjzllx = wjzllx == null ? "" : wjzllx;
                            if (wjzllx.contains("申报表")) {//税费申报表
                                zllxdm = "125131";
                            } else if (wjzllx.contains("凭证")) {//完税凭证
                                zllxdm = "125132";
                            } else if (wjzllx.contains("采集表")) {//房屋采集表
                                zllxdm = "125133";
                            } else if (wjzllx.contains("税")) {//个人所得税扣除
                                zllxdm = "125134";
                            } else if (wjzllx.contains("土地")) {//土地增值税扣除
                                zllxdm = "125135";
                            } else if (wjzllx.contains("受理")) {//不动产登记受理通知单
                                zllxdm = "125136";
                            } else if (wjzllx.contains("采集表")) {//存量房交易采集表
                                zllxdm = "125137";
                            } else if (wjzllx.contains("房产证")) {//房产证
                                zllxdm = "123493";
                            } else if (wjzllx.contains("身份证")) {//身份证
                                zllxdm = "001832";//对应资料库000750
                            } else if (wjzllx.contains("合同")) {//简易合同
                                zllxdm = "000109";//对应资料库000315
                            } else if (wjzllx.contains("营业执照")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("委托书")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("股权变更")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("征纳互动")) {
                                zllxdm = "000000"; //TODO
                            } else {
                                zllxdm = "000000";//其他资料
                            }
                            if (StringUtils.isNotEmpty(zllxdm)) {
                                try {
                                    String zldata = "";
                                    //通过文件url读取文件流
                                    try (InputStream inputStream = new URL(wjurl).openStream()) {
                                        byte[] bytes = IoUtil.readBytes(inputStream);
//                                        zldata = new String(bytes, Charset.defaultCharset());
                                        zldata = Base64Utils.encodeToString(bytes);
                                    }
                                    if (StringUtils.isNotEmpty(zldata)) {
                                        rtn.addResult(dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata, wjlx));
                                        if (rtn.isSuccess()) {
                                            Map<String, Object> zlscObj = (Map<String, Object>) rtn.getData();
                                            String zldjxh = (String) zlscObj.get("zldjxh");
                                            String zlurlnw = (String) zlscObj.get("zlurlnw");
                                            //更新业务附件表状态
                                            ywsj.clear();
                                            ywsj.put("fjbh",fjuuid);
                                            ywsj.put("ywbh",zbuuid);
                                            ywsj.put("ywlx",fjywlx);
                                            ywsj.put("gdzt","已归档");
                                            ywsj.put("zlurlnw",zlurlnw);
                                            ywsj.put("zldjxh",zldjxh);
                                            ywsj.put("gldh",gldh);
                                            commonResult = callFile("flzlgx", ywsj);
                                            if(!commonResult.isSuccess()){
                                                rtn.setFail(commonResult.getMsg());
                                            }
                                        }
                                    } else {
                                        rtn.setFail(fjuuid + "文件不存在");
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.info(uuid + "归档时发生异常" + e.getMessage());
                                    rtn.setFail(e.getMessage());
                                }
                            }
                        }
                        return "success";
                    }, executor).exceptionally(ex -> {
                        log.error("uuid为：【" + fjuuid + "】的数据电子归档失败，原因为：" + ex.getMessage());
                        return "【uuid："+fjuuid+"】";
                    });
                    futures.add(future);
                }
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                //默认推送成功
                boolean flag = true;
                StringBuffer stringBuffer = new StringBuffer("以下数据归档异常：");
                //等待所有异步任务完成
                allFutures.get(300L, TimeUnit.SECONDS);
                for(CompletableFuture<String> future : futures){
                    String taskResult = future.get();
                    if(!StringUtils.equals(taskResult,"success")){
                        //有推送失败的，则整体标记为失败
                        flag = false;
                        stringBuffer.append(taskResult);
                    }
                }
                if(flag){
                    result = dzzlkService.sxzttz(gldh, djxh, nsrsbh, nsrmc);
                    if(result.isSuccess()){
                        // 通知成功后，插入主表
                        param.put("zbuuid",zbuuid);
                        result = czdasj(param, userCode, false,userName);
                    }
                }else {
                    result.setFail(stringBuffer.toString());
                }
            }
        }catch (Exception e){
            log.error("归档发生异常：{}",e.getMessage());
            result.setFail(e.getMessage());
        }
        return result;
    }


    /**
     * function 文件上传
     * 传参 流文件、wjzllx（文件资料类型）、zbuuid（档案表的uuid）
     */
    @RequestMapping("/real/fj/upload")
    @ResponseBody
    public Object upload(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        CommonResult rtn = new CommonResult();
        //获取操作人
        String userCode = (String)request.getSession().getAttribute("usercode");
        userCode = userCode == null ? "1" : userCode;
        log.info("文件上传");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String wjzllx = param.get("wjzllx");
        String zbuuid = param.get("zbuuid");
        zbuuid = StringUtils.isBlank(zbuuid) ? "0" : zbuuid;
        //获取文件全名，截取文件名和文件类型
        String wjqm = file.getOriginalFilename();
        String wjlx = wjqm.substring(wjqm.lastIndexOf(".")+1);
        String wjm = wjqm.substring(0,wjqm.lastIndexOf("."));
        List<Object> uploadResult = new ArrayList<>();
        if(StringUtils.equalsIgnoreCase("zip",wjlx)){
            //创建解压临时目录
            File tempDir = FileUtil.mkdir(filePath + File.separator + "uploadTemp"+File.separator+zbuuid+"-"+wjzllx);
            try {
               //创建临时文件，将上传文件转为本地文件
                File zipTempFile = File.createTempFile("temp", ".zip");
                file.transferTo(zipTempFile);
                //将此压缩包也作为文件上传，防止有意外情况时还可以找回原压缩包文件
                rtn = uploadFile(zipTempFile,zbuuid,wjzllx,wjqm,wjlx,userCode);
                if(rtn.isSuccess()){
                    uploadResult.add(rtn.getData());
                }
                //解压压缩包
                ZipUtil.unzip(zipTempFile,tempDir);
                //获得实际解压后的目录地址，遍历其下所有文件
                File[] files = new File(tempDir.getPath()+File.separator+wjm).listFiles();
                if(files != null && files.length > 0){
                    //遍历压缩包文件进行文件上传
                    for(File mxfile : files){
                        if(mxfile.isFile()){
                            wjqm = mxfile.getName();
                            wjlx = wjqm.substring(wjqm.lastIndexOf(".")+1);
                            rtn = uploadFile(mxfile,zbuuid,wjzllx,wjqm,wjlx,userCode);
                            if(rtn.isSuccess()){
                                uploadResult.add(rtn.getData());
                            }
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(uploadResult)){
                    //删除临时文件
                    FileUtil.del(zipTempFile);
                    //删除解压临时目录
                    FileUtil.del(tempDir);
                    //将所有上传数据返回
                    rtn.setSuccess(uploadResult);
                }
            } catch (Exception e) {
                rtn.setFail("上传失败："+e.getMessage());
            }
        }else {
            try {
                //创建临时文件，将上传文件转为本地文件
                File tempFile = File.createTempFile("temp", wjlx);
                file.transferTo(tempFile);
                //进行文件上传
                rtn = uploadFile(tempFile,zbuuid,wjzllx,wjqm,wjlx,userCode);
                if(rtn.isSuccess()){
                    //上传成功删除临时文件
                    FileUtil.del(tempFile);
                }
            } catch (IOException e) {
                rtn.setFail("上传失败："+e.getMessage());
            }
        }
        return rtn;
    }

    /**
     * 本地附件上传
     * @param file
     * @param zbuuid
     * @param wjzllx
     * @param userCode
     * @return
     */
    private CommonResult uploadFile(File file,String zbuuid,String wjzllx,String wjqm,String wjlx,String userCode){
        String wjgs = "3";
        String wjlj =DateUtils.strNow("yyyyMM");
        CommonResult rtFile = new CommonResult("00","success");
        try{
            byte[] byes = Files.readAllBytes(file.toPath());
            String fileStr = Base64Utils.encodeToString(byes);
            //String fileId = UUID.randomUUID().toString().replaceAll("-","");
            String wjdx = String.valueOf(file.length());
            boolean qzflag = false;
            if(StringUtils.contains(wjzllx,"采集表") || StringUtils.contains(wjzllx,"申报表") || StringUtils.contains(wjzllx,"承诺书")){
                qzflag = true;
            }
            JSONObject data = new JSONObject();
            //data.put("fjbh",fileId);
            data.put("ywbh",zbuuid);
            data.put("pzbh","0");
            data.put("wjzllx",wjzllx);
            data.put("wjgs",wjgs);
            data.put("wjm",wjqm);
            data.put("wjlj",wjlj);
            data.put("wjnr",fileStr);
            data.put("wjlx",wjlx);
            data.put("wjdx",wjdx);
            data.put("lrrdm",userCode);
            data.put("lrrq",DateUtils.strNow());
            data.put("xgrdm",userCode);
            data.put("xgrq",DateUtils.strNow());
            data.put("yxbz","Y");
            data.put("nsrsfqz",qzflag ? "否" : "-");
            data.put("sfxyswqr",qzflag ? "否" : "-");
            data.put("swsfyqr",qzflag ? "否" : "-");
            data.put("swsfqz",qzflag ? "否" : "-");
            data.put("gdzt","未归档");
            data.put("gldh","");
            data.put("zldjxh","");
            data.put("zlurlnw","");
            data.put("sfts",qzflag ? "否" : "-");
            data.put("ywlx",fjywlx);
            rtFile = callFile("flzlxz", data);
            if(rtFile.isSuccess()){
                List<Map<String,Object>> paramList = new ArrayList<>();
                String fileId = (String) rtFile.getData();
                paramList.add(esbService.addParam("uuid",fileId,"string"));
                paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
                paramList.add(esbService.addParam("pzbh","0","string"));
                paramList.add(esbService.addParam("wjzllx",wjzllx,"string"));
                paramList.add(esbService.addParam("wjgs",wjgs,"string"));
                paramList.add(esbService.addParam("wjm",wjqm,"string"));
                paramList.add(esbService.addParam("wjlj",wjlj,"string"));
                paramList.add(esbService.addParam("wjurl","","string"));
                paramList.add(esbService.addParam("wjlx",wjlx,"string"));
                paramList.add(esbService.addParam("wjdx",wjdx,"string"));
                paramList.add(esbService.addParam("wjnr","" ,"string"));
                paramList.add(esbService.addParam("lrrdm",userCode,"string"));
                paramList.add(esbService.addParam("lrrq", DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("yxbz","Y","string"));
                paramList.add(esbService.addParam("nsrsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("sfxyswqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfyqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("gdzt","未归档","string"));
                paramList.add(esbService.addParam("gldh","","string"));
                paramList.add(esbService.addParam("zldjxh","","string"));
                paramList.add(esbService.addParam("zlurlnw","","string"));
                paramList.add(esbService.addParam("sfts",qzflag ? "否" : "-","string"));
                rtFile.setSuccess(esbService.paramListToMap(paramList));
            }else {
                rtFile.setFail("上传失败："+rtFile.getMsg());
            }
        }catch (Exception e){
            rtFile.setFail("上传失败："+e.getMessage());
        }
        return rtFile;
    }
}