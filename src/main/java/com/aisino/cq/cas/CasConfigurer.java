 package com.aisino.cq.cas;

 import org.jasig.cas.client.authentication.AuthenticationFilter;
 import org.jasig.cas.client.session.SingleSignOutFilter;
 import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
 import org.jasig.cas.client.util.AssertionThreadLocalFilter;
 import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
 import org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.boot.web.servlet.FilterRegistrationBean;
 import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
 import org.springframework.security.web.authentication.logout.LogoutFilter;
 import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;

 @Configuration
 public class CasConfigurer {
     @Autowired
     private SpringCasAutoConfig autoConfig;

     /**
       * 用于实现单点登出功能
       */
     @Bean
     public ServletListenerRegistrationBean<SingleSignOutHttpSessionListener> singleSignOutHttpSessionListener() {
         ServletListenerRegistrationBean<SingleSignOutHttpSessionListener> listener = new ServletListenerRegistrationBean<>();
         listener.setEnabled(autoConfig.getCasEnabled());
         listener.setListener(new SingleSignOutHttpSessionListener());
         listener.setOrder(1);
         return listener;
     }

     /**
       * 该过滤器用于实现单点登出功能，单点退出配置，一定要放在其他filter之前
       */
     @Bean
     public FilterRegistrationBean logOutFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         LogoutFilter logoutFilter = new LogoutFilter(autoConfig.getCasServerUrlPrefix() + "/logout?service=" + autoConfig.getServerName(), new SecurityContextLogoutHandler());
         filterRegistration.setFilter(logoutFilter);
         filterRegistration.setEnabled(autoConfig.getCasEnabled());
         if(autoConfig.getSignOutFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getSignOutFilters());
         } else {
             filterRegistration.addUrlPatterns("/logout");
         }
         filterRegistration.addInitParameter("casServerUrlPrefix", autoConfig.getCasServerUrlPrefix());
         filterRegistration.addInitParameter("serverName", autoConfig.getServerName());
         filterRegistration.setOrder(2);
         return filterRegistration;
     }

     /*
      * 该过滤器用于实现单点登出功能，单点退出配置，一定要放在其他filter之前
      */
     @Bean
     public FilterRegistrationBean singleSignOutFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         filterRegistration.setFilter(new SingleSignOutFilter());
         filterRegistration.setEnabled(autoConfig.getCasEnabled());
         if(autoConfig.getSignOutFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getSignOutFilters());
         } else {
             filterRegistration.addUrlPatterns("/*");
         }
         filterRegistration.addInitParameter("casServerUrlPrefix", autoConfig.getCasServerUrlPrefix());
         filterRegistration.addInitParameter("serverName", autoConfig.getServerName());
         filterRegistration.setOrder(3);
         return filterRegistration;
     }

     /**
       * 该过滤器负责用户的认证工作
       */
     @Bean
     public FilterRegistrationBean authenticationFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         filterRegistration.setFilter(new AuthenticationFilter());
         filterRegistration.setEnabled(autoConfig.getCasEnabled());
         if(autoConfig.getAuthFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getAuthFilters());
         } else {
             filterRegistration.addUrlPatterns("/*");
         }
         // casServerLoginUrl:cas服务的登陆url
         filterRegistration.addInitParameter("casServerLoginUrl", autoConfig.getCasServerLoginUrl());
         // 本项目登录ip+port
         if(autoConfig.getIgnorePattern().length() > 0) {
             filterRegistration.addInitParameter("ignorePattern", autoConfig.getIgnorePattern());
         }
         filterRegistration.addInitParameter("serverName", autoConfig.getServerName());
         filterRegistration.addInitParameter("useSession", autoConfig.isUseSession() ? "true" : "false");
         filterRegistration.addInitParameter("redirectAfterValidation", autoConfig.isRedirectAfterValidation() ? "true" : "false");
         filterRegistration.setOrder(4);
         return filterRegistration;
     }

     /**
      * 该过滤器负责对Ticket的校验工作
      */
     @Bean
     public FilterRegistrationBean cas20ProxyReceivingTicketValidationFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         Cas20ProxyReceivingTicketValidationFilter cas20ProxyReceivingTicketValidationFilter = new Cas20ProxyReceivingTicketValidationFilter();
         cas20ProxyReceivingTicketValidationFilter.setServerName(autoConfig.getServerName());
         filterRegistration.setFilter(cas20ProxyReceivingTicketValidationFilter);
         filterRegistration.setEnabled(autoConfig.getCasEnabled());
         if (autoConfig.getValidateFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getValidateFilters());
         } else {
             filterRegistration.addUrlPatterns("/*");
         }
         filterRegistration.addInitParameter("casServerUrlPrefix", autoConfig.getCasServerUrlPrefix());
         filterRegistration.addInitParameter("serverName", autoConfig.getServerName());
         filterRegistration.addInitParameter("encoding", autoConfig.getEncoding());
         filterRegistration.addInitParameter("JrLx",autoConfig.getJrlx());
         filterRegistration.addInitParameter("GzLm",autoConfig.getGzlm());
         filterRegistration.addInitParameter("GzFfm",autoConfig.getGzffm());
         filterRegistration.setOrder(5);
         return filterRegistration;
     }


     /**
       * 该过滤器对HttpServletRequest请求包装，
       * 可通过HttpServletRequest的getRemoteUser()方法获得登录用户的登录名
       */
     @Bean
     public FilterRegistrationBean httpServletRequestWrapperFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         filterRegistration.setFilter(new HttpServletRequestWrapperFilter());
         filterRegistration.setEnabled(true);
         if (autoConfig.getRequestWrapperFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getRequestWrapperFilters());
         } else {
             filterRegistration.addUrlPatterns("/login");
         }
         filterRegistration.setOrder(6);
         return filterRegistration;
     }

     /*
      * 该过滤器使得可以通过org.jasig.cas.client.util.AssertionHolder来获取用户的登录名。
      * 比如AssertionHolder.getAssertion().getPrincipal().getName()。
      * 这个类把Assertion信息放在ThreadLocal变量中，这样应用程序不在web层也能够获取到当前登录信息
      */
     @Bean
     public FilterRegistrationBean assertionThreadLocalFilter() {
         FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
         filterRegistration.setFilter(new AssertionThreadLocalFilter());
         filterRegistration.setEnabled(true);
         if (autoConfig.getAssertionFilters().size() > 0) {
             filterRegistration.setUrlPatterns(autoConfig.getAssertionFilters());
         } else {
             filterRegistration.addUrlPatterns("/*");
         }
         filterRegistration.setOrder(7);
         return filterRegistration;
     }
 }
