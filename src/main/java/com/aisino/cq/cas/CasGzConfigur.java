 package com.aisino.cq.cas;

 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;

 import javax.servlet.http.HttpServletRequest;
 import java.util.Map;

 /**
  * CAS session设置方法
  */
 public class CasGzConfigur
 {

     Logger logger = LoggerFactory.getLogger("CasGzConfigur");
     public void CasSessionConfigur(Map<String,Object>map){
         String usercode = (String)map.get("swrydm");
         String username = (String)map.get("swrysfmc");
         // 税务人员所在上级机构代码
         String sjswjgdm = (String) map.get("sjswjgdm");
         // 当前人所在税务机关代码
         String swjgdm = (String) map.get("swjgdm");
         logger.info("单点登录：usercode:"+usercode+",username:"+username+",sjswjgdm:"+sjswjgdm+",swjgdm:"+swjgdm);
         HttpServletRequest request = (HttpServletRequest)map.get("request");
         request.getSession().setAttribute("username", username);
         request.getSession().setAttribute("usercode", usercode);
         request.getSession().setAttribute("sjswjgdm", sjswjgdm);
         request.getSession().setAttribute("swjgdm", swjgdm);
     }
 }

