package com.aisino.cq.pdf;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

@Service
public class ImageToPdfService {

    private static final Logger logger = LoggerFactory.getLogger(ImageToPdfService.class);

    /**
     * 单张图片转PDF
     */
    public byte[] convertSingleImageToPdf(MultipartFile imageFile) throws IOException {
        logger.info("开始转换单张图片: {}", imageFile.getOriginalFilename());

        PDDocument document = new PDDocument();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            // 读取图片
            BufferedImage bufferedImage = ImageIO.read(imageFile.getInputStream());
            if (bufferedImage == null) {
                throw new IOException("无法读取图片文件");
            }

            // 创建PDF页面，设置页面大小与图片一致
            float width = bufferedImage.getWidth();
            float height = bufferedImage.getHeight();

            // 如果图片太大，按比例缩放到合适大小
            if (width > 1200 || height > 1600) {
                float scale = Math.min(1200f / width, 1600f / height);
                width *= scale;
                height *= scale;
            }

            PDRectangle pageSize = new PDRectangle(width, height);
            PDPage page = new PDPage(pageSize);
            document.addPage(page);

            // 创建图片对象
            PDImageXObject pdImage = LosslessFactory.createFromImage(document, bufferedImage);

            // 在页面上绘制图片
            PDPageContentStream contentStream = new PDPageContentStream(document, page);
            contentStream.drawImage(pdImage, 0, 0, width, height);
            contentStream.close();

            // 保存PDF
            document.save(baos);
            logger.info("图片转换完成");

        } finally {
            document.close();
        }

        return baos.toByteArray();
    }

    /**
     * 多张图片转PDF
     */
    public byte[] convertMultipleImagesToPdf(List<MultipartFile> imageFiles) throws IOException {
        logger.info("开始转换多张图片，共{}张", imageFiles.size());

        PDDocument document = new PDDocument();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            for (int i = 0; i < imageFiles.size(); i++) {
                MultipartFile imageFile = imageFiles.get(i);
                logger.info("处理第{}张图片: {}", i + 1, imageFile.getOriginalFilename());

                // 读取图片
                BufferedImage bufferedImage = ImageIO.read(imageFile.getInputStream());
                if (bufferedImage == null) {
                    logger.warn("跳过无法读取的图片: {}", imageFile.getOriginalFilename());
                    continue;
                }

                // 计算页面大小
                float width = bufferedImage.getWidth();
                float height = bufferedImage.getHeight();

                // 限制最大尺寸
                if (width > 1200 || height > 1600) {
                    float scale = Math.min(1200f / width, 1600f / height);
                    width *= scale;
                    height *= scale;
                }

                // 创建新页面
                PDRectangle pageSize = new PDRectangle(width, height);
                PDPage page = new PDPage(pageSize);
                document.addPage(page);

                // 创建图片对象并添加到页面
                PDImageXObject pdImage = LosslessFactory.createFromImage(document, bufferedImage);
                PDPageContentStream contentStream = new PDPageContentStream(document, page);
                contentStream.drawImage(pdImage, 0, 0, width, height);
                contentStream.close();
            }

            // 保存PDF
            document.save(baos);
            logger.info("多张图片转换完成");

        } finally {
            document.close();
        }

        return baos.toByteArray();
    }

    /**
     * 验证图片文件
     */
    public boolean isValidImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String contentType = file.getContentType();
        return contentType != null && (
                contentType.equals("image/jpeg") ||
                        contentType.equals("image/jpg") ||
                        contentType.equals("image/png") ||
                        contentType.equals("image/bmp") ||
                        contentType.equals("image/gif")
        );
    }
}
