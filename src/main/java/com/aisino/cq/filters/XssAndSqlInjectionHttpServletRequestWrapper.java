package com.aisino.cq.filters;




import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class XssAndSqlInjectionHttpServletRequestWrapper extends HttpServletRequestWrapper
{

    HttpServletRequest orgRequest;

    //BaseContoller需要的参数
    private Map<String,String[]> parameterMap;

    public XssAndSqlInjectionHttpServletRequestWrapper(final HttpServletRequest request) {
        super(request);
        this.orgRequest = null;
        this.orgRequest = request;
        parameterMap=request.getParameterMap();
    }

    public String getParameter(final String name) {
        String value = super.getParameter(xssEncode(name));
        if (StringUtils.isNotEmpty(value)) {
            value = xssEncode(value);
        }
        return value;
    }

    public Map<String,String[]> getParameterMap(){
        final Map<String,String[]> values = new HashMap<>(parameterMap.size());
        if (parameterMap == null || parameterMap.size()<1) {
            return values;
        }
        for (String key : parameterMap.keySet())
        {
            Object val = parameterMap.get(key)[0];
            if (val instanceof String)
            {
                values.put(key,new String[]{xssEncode(val.toString())});
            }
        }
        return values;
    }

    public String[] getParameterValues(final String parameter) {
        final String[] values = super.getParameterValues(parameter);
        if (values == null) {
            return null;
        }
        final int count = values.length;
        final String[] xssEncodeValues = new String[count];
        for (int i = 0; i < count; ++i) {
            if (StringUtils.isNotEmpty(values[i])) {
                xssEncodeValues[i] = xssEncode(values[i]);
            }
        }
        return xssEncodeValues;
    }

    public String getHeader(final String name) {
        String value = super.getHeader(xssEncode(name));
        if (StringUtils.isNotEmpty(value)) {
            value = xssEncode(value);
        }
        return value;
    }

    private static String xssEncode(String s) {
        if (StringUtils.isEmpty(s)) {
            return s;
        }
        s = stripXSSAndSql(s);
        final StringBuilder sb = new StringBuilder(s.length() + 16);
        for (int i = 0; i < s.length(); ++i) {
            final char c = s.charAt(i);
            switch (c) {
                case '>': {
                    sb.append('\uff1e');
                    continue;
                }
                case '<': {
                    sb.append('\uff1c');
                    continue;
                }
                case '\'': {
                    sb.append('\u2018');
                    continue;
                }
                case '\"': {
                    sb.append('\u201c');
                    continue;
                }
                case '&': {
                    sb.append('\uff06');
                    continue;
                }
                case '\\': {
                    sb.append('\uff3c');
                    continue;
                }
                case '/': {
                    sb.append('\uff0f');
                    continue;
                }
                case '#': {
                    sb.append('\uff03');
                    continue;
                }
                case '(': {
                    sb.append('\uff08');
                    continue;
                }
                case ')': {
                    sb.append('\uff09');
                    continue;
                }
                case ';': {
                    sb.append('\uff1b');
                    continue;
                }
                case ':': {
                    sb.append('\uff1a');
                    break;
                }
                case '.': {
                    sb.append('\u3002');
                    break;
                }
                case '\r': {
                    sb.append('\u3002');
                    break;
                }
                case '\n': {
                    sb.append('\u3002');
                    break;
                }
            }
            sb.append(c);
        }
        return sb.toString();
    }

    public HttpServletRequest getOrgRequest() {
        return this.orgRequest;
    }

    public static HttpServletRequest getOrgRequest(final HttpServletRequest req) {
        if (req instanceof XssAndSqlInjectionHttpServletRequestWrapper) {
            return ((XssAndSqlInjectionHttpServletRequestWrapper)req).getOrgRequest();
        }
        return req;
    }

    public static String stripXSSAndSql(String value) {
        if (StringUtils.isNotEmpty(value)) {
            Pattern scriptPattern = Pattern.compile("<[\r\n| | ]*script[\r\n| | ]*>(.*?)</[\r\n| | ]*script[\r\n| | ]*>", 2);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("src[\r\n| | ]*=[\r\n| | ]*[\\\"|\\'](.*?)[\\\"|\\']", 42);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("</[\r\n| | ]*script[\r\n| | ]*>", 2);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("<[\r\n| | ]*script(.*?)>", 42);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("eval\\((.*?)\\)", 42);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("e-xpression\\((.*?)\\)", 42);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("javascript[\r\n| | ]*:[\r\n| | ]*", 2);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("vbscript[\r\n| | ]*:[\r\n| | ]*", 2);
            value = scriptPattern.matcher(value).replaceAll("");
            scriptPattern = Pattern.compile("onload(.*?)=", 42);
            value = scriptPattern.matcher(value).replaceAll("");
        }
        return value;
    }

    public static void main(final String[] args) {
        final String key = "Javascript:<script>alert('yes');</script>";
        final String key2 = xssEncode(key);
        System.out.println(key);
        System.out.println(key2);
        System.out.println(key.equals(key2));
    }
}
