package com.aisino.cq.filters;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @ClassName : XssAndSqlInjectionFilter
 * @Description :
 * <AUTHOR> 码哥
 * @Date : 2020-06-17  10:17
 */
@WebFilter(filterName = "xssAndSqlFilter",urlPatterns = "/*")
public class XssAndSqlInjectionFilter implements Filter
{

    Logger logger = LoggerFactory.getLogger(XssAndSqlInjectionFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException
    {
        HttpServletRequest request =(HttpServletRequest)servletRequest;
        MDC.put("request_id",request.getSession().getId());
        String path=request.getServletPath();
        logger.info("进入xssAndSql过滤器，请求路径"+path);
        String[] exclusionsUrls = {".js",".css",".jpg",".png",".ico",".jpeg","fxcl/zhcl","query","invoke"};
        for (String str : exclusionsUrls)
        {
            if (path.contains(str))
            {
                filterChain.doFilter(servletRequest,servletResponse);
                return;
            }
        }
        filterChain.doFilter(new XssAndSqlInjectionHttpServletRequestWrapper(request),servletResponse);
    }

    @Override
    public void destroy()
    {

    }
}
