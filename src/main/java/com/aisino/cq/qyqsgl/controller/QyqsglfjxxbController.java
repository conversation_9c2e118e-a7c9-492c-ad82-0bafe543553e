package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.util.IdUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// "管理后台 - 企业欠税管理-附件信息")
@RestController
@RequestMapping("/qyqsgl/qyqsglfjxxb")
@Validated
public class QyqsglfjxxbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglfjxxbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;
    final private String qyqsglPath = "/qyqsgl";

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("企业欠税管理-附件信息-列表查询");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"zbuuid", param.get("zbuuid"), "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000086",pageNo, pageSize,"0","true",paramList);
    }

    /**
     * function 文件上传
     */
    @RequestMapping("/upload")
    @ResponseBody
    public Object upload(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("企业欠税管理-附件信息-文件上传");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String fileStr = Base64Utils.encodeToString(byes);

            String uuid = IdUtil.simpleUUID();
            String pzbh = "0";
            String zbuuid = param.get("zbuuid");
//            String wjzllx = param.get("wjzllx");
//            String wjgs = param.get("wjgs");
            String wjzllx = "大额欠税";
            String wjgs = "1";
            String wjm = file.getOriginalFilename();
            String wjlj = DateUtils.strNow("yyyyMM");
//            String wjurl = param.get("wjurl");
            String wjlx = wjm.substring(wjm.lastIndexOf(".")+1);
            String wjdx = String.valueOf(file.getSize());
//            String wjnr = param.get("wjnr");

            FileUtils.writeStringToFile(new File(filePath + qyqsglPath + File.separator + wjlj + File.separator + uuid),fileStr);

            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid",uuid,"string"));
            paramList.add(esbService.addParam("pzbh", pzbh,"string"));
            paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
            paramList.add(esbService.addParam("wjzllx",wjzllx,"string"));
            paramList.add(esbService.addParam("wjgs",wjgs,"string"));
            paramList.add(esbService.addParam("wjm",wjm,"string"));
            paramList.add(esbService.addParam("wjlj",wjlj,"string"));
            paramList.add(esbService.addParam("wjurl","","string"));
            paramList.add(esbService.addParam("wjlx",wjlx,"string"));
            paramList.add(esbService.addParam("wjdx",wjdx,"string"));
            paramList.add(esbService.addParam("wjnr","","string"));
            paramList.add(esbService.addParam("nsrsfqz","","string"));
            paramList.add(esbService.addParam("sfxyswqr","","string"));
            paramList.add(esbService.addParam("swsfyqr","","string"));
            paramList.add(esbService.addParam("swsfqz","","string"));
            paramList.add(esbService.addParam("gdzt","未归档","string"));
            paramList.add(esbService.addParam("gldh","","string"));
            paramList.add(esbService.addParam("zldjxh","","string"));
            paramList.add(esbService.addParam("zlurlnw","","string"));
            paramList.add(esbService.addParam("sfts","","string"));
            paramList.add(esbService.addParam("lrrdm",userCode,"string"));
            paramList.add(esbService.addParam("xgrdm",userCode,"string"));
            paramList.add(esbService.addParam("yxbz","Y","string"));
//            esbService.addParam(paramList,"lrrdm", userCode, "string");
//            esbService.addParam(paramList,"xgrdm", userCode, "string");
//            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult rtFile = esbService.queryEsb("30000084","1", "10","0","false",paramList);
            System.out.println(rtFile);
            rtFile.setSuccess(esbService.paramListToMap(paramList));
            return rtFile;
        }catch (Exception e){
            e.printStackTrace();
        }
        return new CommonResult();
    }

    /**
     * function 文件下载
     */
    @RequestMapping("download")
    @ResponseBody
    public void download(String uuid, HttpServletResponse response){
        log.info("企业欠税管理-附件信息-文件下载" + uuid);
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid", uuid,"string"));
            CommonResult rtFile = esbService.queryEsb("30000086","1", "10","0","false",paramList);
            if(rtFile.isSuccess()){
                JSONArray ja = (JSONArray)rtFile.getData();
                if(ja.size()>0) {
                    JSONObject jo = ja.getJSONObject(0);
                    String fileName = jo.getString("wjm");
                    String wjlj = jo.getString("wjlj");
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-Disposition","attachment;filename=\""+fileName+"\"");
                    ServletOutputStream out =  response.getOutputStream();
                    byte[] b = FileUtils.readFileToByteArray(new File(filePath + qyqsglPath + File.separator + wjlj + File.separator + uuid));

                    b = Base64Utils.decode(b);
                    out.write(b);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 文件预览
     */
    @RequestMapping("preview")
    @ResponseBody
    public void preview(String uuid, HttpServletResponse response){
        log.info("企业欠税管理-附件信息-文件预览"+ uuid);
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid", uuid,"string"));
            CommonResult rtFile = esbService.queryEsb("30000086","1", "10","0","false",paramList);
            if(rtFile.isSuccess()){
                JSONArray ja = (JSONArray)rtFile.getData();
                if(ja.size()>0) {
                    JSONObject jo = ja.getJSONObject(0);
                    String wjlx = jo.getString("wjlx");
                    String wjlj = jo.getString("wjlj");
                    if("pdf".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                    }else if("png".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_PNG_VALUE);
                    }else if("jpg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("jpeg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("gif".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_GIF_VALUE);
                    }
                    ServletOutputStream out =  response.getOutputStream();
                    byte[] b = FileUtils.readFileToByteArray(new File(filePath + qyqsglPath + File.separator + wjlj + File.separator + uuid));
                    b = Base64Utils.decode(b);
                    out.write(b);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @RequestMapping("delete")
    @ResponseBody
    public Object deleteQyqsglfjxxb(String uuid, HttpServletRequest request) {
        log.info("企业欠税管理-附件信息-文件刪除"+ uuid);
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", uuid,"string"));
        paramList.add(esbService.addParam("yxbz", "Y","string"));
        result = esbService.queryEsb("30000086","1", "10","0","false",paramList);
        if(result.isSuccess() && ((JSONArray)result.getData()).size()>0){
            JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);

            paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid", uuid,"string"));
            paramList.add(esbService.addParam("yxbz", "N","string"));
            result = esbService.queryEsb("30000085","1", "10","0","false",paramList);
        }
        return result;
    }

}