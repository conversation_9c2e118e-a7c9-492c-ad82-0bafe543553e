package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.qyqsgl.constant.DmMaps;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// "管理后台 - 企业欠税管理-追征任务清单")
@RestController
@RequestMapping("/qyqsgl/qyqsglzzrwqdb")
public class QyqsglzzrwqdbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglzzrwqdbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("追征任务清单-列表查询");
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String shxydm = param.get("shxydm");
        String nsrmc = param.get("nsrmc");
        String qslb = param.get("qslb");
//        String zgswks = param.get("zgswks");
        String ssgly = param.get("ssgly");
        String sfhdlhd = param.get("sfhdlhd");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"shxydm", shxydm, "string");
        esbService.addParam(paramList,"nsrmc", nsrmc, "string");
        esbService.addParam(paramList,"qslb", qslb, "string");
        esbService.addParam(paramList,"zgswks", zgswks, "string");
        esbService.addParam(paramList,"ssgly", ssgly, "string");
        esbService.addParam(paramList,"sfhdlhd", sfhdlhd, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000073",pageNo, pageSize,"0","true",paramList);
//
//        CommonResult result = esbService.queryEsb("30000073",pageNo, "999","0","true",paramList);
//        if (null == result.getData()){
//            result.setSuccess(null);
//        }
//        JSONArray rows = (JSONArray)result.getData();
//
//        for (Object obj: rows) {
//            JSONObject jo = (JSONObject) obj;
//            String uuid = jo.getString("uuid");
//            paramList = new ArrayList<>();
//
//            esbService.addParam(paramList,"uuid", uuid, "string");
//            esbService.addParam(paramList,"sfxsj", "Y", "string");
//            result = esbService.queryEsb("30000072", paramList);
//        }
//        return null;
    }


    @PostMapping("/zzrwfk")
    @ResponseBody
    public Object zzrwfk(HttpServletRequest request) {
        log.info("追征任务清单-追征任务反馈");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();
//        追征任务 - 反馈
        paramList = new ArrayList<>();
        esbService.addParam(paramList,"uuid", param.get("uuid"), "string");
        esbService.addParam(paramList,"zzrwwcrq", param.get("zzrwwcrq"), "string");
        esbService.addParam(paramList,"zzcs", param.get("zzcs"), "string");
        esbService.addParam(paramList,"zzjg", param.get("zzjg"), "string");
        esbService.addParam(paramList,"zzry", param.get("zzry"), "string");
        esbService.addParam(paramList,"zzrwzt", "已处理", "string");
        esbService.addParam(paramList,"fkr", userCode, "string");
        esbService.addParam(paramList,"fkrq", DateUtils.strNow(), "string");
        esbService.addParam(paramList,"xgrdm", userCode, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");

        result = esbService.queryEsb("30000072", paramList);

        result.setSuccess(null);
        return result;
    }

    @PostMapping("/zzjg")
    @ResponseBody
    public Object zzjg(HttpServletRequest request) {
        log.info("公共-追征结果");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"zbuuid", param.get("zbuuid"), "string");
        esbService.addParam(paramList,"zzrwzt", "已处理", "string");
        esbService.addParam(paramList,"sfxsj", "Y", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000073",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/zzcs")
    @ResponseBody
    public Object zzcs(HttpServletRequest request) {
        log.info("欠税清单-追征措施");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"zbuuid", param.get("zbuuid"), "string");
        esbService.addParam(paramList,"zzrwzt", "已处理", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000073",pageNo, pageSize,"0","true",paramList);
    }








    @PostMapping("/plxgshuj")
    @ResponseBody
    public Object plxgshuj(HttpServletRequest request) {
        log.info("追征企业清单-批量修改数据");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        result = esbService.queryEsb("30000073",pageNo, pageSize,"0","true",paramList);
        if (null == result.getData()){
            result.setSuccess(null);
        }
        JSONArray rows = (JSONArray)result.getData();

        int tt = 0;
        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
            String uuid = jo.getString("uuid");

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            paramList.add(esbService.addParam("jkqx", "2025-02-02", "string"));
            paramList.add(esbService.addParam("sfzrr", "N", "string"));
            paramList.add(esbService.addParam("fddbrsfzhm", "法人身份证号码"+ tt, "string"));
            paramList.add(esbService.addParam("fddbrxm", "法人姓名"+ tt, "string"));
            result = esbService.queryEsb("30000072",pageNo, pageSize,"0","true",paramList);
        }

        return null;
    }


    /**
     * function 追征任务清单-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletRequest request, HttpServletResponse response){
        log.info("追征任务清单-导出: " + jsonData );
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String shxydm = param.getString("shxydm");
            String nsrmc = param.getString("nsrmc");
            String qslb = param.getString("qslb");
            String ssgly = param.getString("ssgly");
            String sfhdlhd = param.getString("sfhdlhd");

            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"shxydm", shxydm, "string");
            esbService.addParam(paramList,"nsrmc", nsrmc, "string");
            esbService.addParam(paramList,"qslb", qslb, "string");
            esbService.addParam(paramList,"zgswks", zgswks, "string");
            esbService.addParam(paramList,"ssgly", ssgly, "string");
            esbService.addParam(paramList,"sfhdlhd", sfhdlhd, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000073", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "统计日期", "社会信用代码", "纳税人名称", "纳税人状态", "法定代表人姓名", "法定代表人身份证号码", "欠税类别", "经营情况", "主管税务科所", "街道乡镇", "税收管理员", "欠税余额", "往年陈欠", "本年新欠", "备注" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("tjrq"));
                    r.add(jo.getString("shxydm"));
                    r.add(jo.getString("nsrmc"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("fddbrxm"));
                    r.add(jo.getString("fddbrsfzhm"));
                    r.add(jo.getString("qslb"));
                    r.add(jo.getString("jyqk"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("qsye"));
                    r.add(jo.getString("wncq"));
                    r.add(jo.getString("bnxq"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("追征任务清单", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("追征任务清单-导出失败", e);
        }
    }

}