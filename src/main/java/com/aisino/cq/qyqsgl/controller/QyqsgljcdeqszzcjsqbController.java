package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// "管理后台 - 企业欠税管理-解除大额欠税阻止出境申请")
@RestController
@RequestMapping("/qyqsgl/qyqsgljcdeqszzcjsqb")
public class QyqsgljcdeqszzcjsqbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsgljcdeqszzcjsqbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("阻止出境人员-列表查询");

        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String nsrmc = param.get("nsrmc");
        String nsrzt = param.get("nsrzt");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"nsrmc", nsrmc, "string");
        esbService.addParam(paramList,"nsrzt", nsrzt, "string");
        esbService.addParam(paramList, "zzcjzt", "已阻止", "string");
        esbService.addParam(paramList,"fyxbz", "Y", "string");
        return esbService.queryEsb("30000088",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/jczzcjtsga")
    @ResponseBody
    public Object jczzcjtsga(HttpServletRequest request) {
        log.info("阻止出境人员-解除阻止出境-推送公安");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String uuid = param.get("uuid");
        String jczzcjryxm = param.get("zzcjryxm");
        String lxdh = param.get("lxdh");
        String sfzh = param.get("sfzh");
        String tsrq = param.get("tsrq");
        String swgly = param.get("swgly");
        String glylxfs = param.get("glylxfs");
        String jczzcjtsrbz = param.get("zzcjtsrbz");
        List<Map<String,Object>> paramList = new ArrayList<>();

//        企业欠税管理-大额欠税阻止出境申请表 - 查询
        paramList = new ArrayList<>();
        esbService.addParam(paramList, "uuid", uuid, "string");
//        esbService.addParam(paramList, "yxbz", "Y", "string");
        result = esbService.queryEsb("30000080", paramList);
        JSONObject zzcjObj = null;
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            zzcjObj = ((JSONArray)result.getData()).getJSONObject(0);
//            企业欠税管理-解除大额欠税阻止出境申请表 - 更新
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", zzcjObj.getString("uuid"), "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            esbService.addParam(paramList,"yxbz", "N", "string");
            result = esbService.queryEsb("30000079", paramList);
        } else {
            return result;
        }

//        企业欠税管理-解除大额欠税阻止出境申请表 - 新增
        paramList = new ArrayList<>();
        esbService.addParam(paramList, "uuid", IdUtil.simpleUUID(), "string");
        esbService.addParam(paramList, "zbuuid", zzcjObj.getString("zbuuid"), "string");
        esbService.addParam(paramList, "zzcjsbjfkuuid", zzcjObj.getString("zzcjsbjfkuuid"), "string");
        esbService.addParam(paramList, "jczzcjtsr", userCode, "string");
        esbService.addParam(paramList, "jczzcjtsrrq", DateUtils.strNow(), "string");
        esbService.addParam(paramList, "jczzcjryxm", jczzcjryxm, "string");
        esbService.addParam(paramList, "lxdh", lxdh, "string");
        esbService.addParam(paramList, "sfzh", sfzh, "string");
        esbService.addParam(paramList, "tsrq", tsrq, "string");
        esbService.addParam(paramList, "swgly", swgly, "string");
        esbService.addParam(paramList, "glylxfs", glylxfs, "string");
        paramList.add(esbService.addParam("jczzcjtsrbz",jczzcjtsrbz,"string"));
        esbService.addParam(paramList, "jczzcjzt", "已解除", "string");
        esbService.addParam(paramList, "lrrdm", userCode, "string");
        esbService.addParam(paramList, "xgrdm", userCode, "string");
        esbService.addParam(paramList, "yxbz", "Y", "string");
        result = esbService.queryEsb("30000081", paramList);

        result.setSuccess(null);
        return result;
    }





    /**
     * function 阻止出境人员-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletResponse response){
        log.info("阻止出境人员-导出: " + jsonData );

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String nsrmc = param.getString("nsrmc");
            String nsrzt = param.getString("nsrzt");
            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"nsrmc", nsrmc, "string");
            esbService.addParam(paramList,"nsrzt", nsrzt, "string");
            esbService.addParam(paramList,"fyxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000088", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "社会信用代码", "纳税人名称", "纳税人状态", "主管税务科所", "街道乡镇", "税收管理员", "欠税余额", "阻止出境人员名称", "阻止出境开始日期", "阻止出境结束日期" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("shxydm"));
                    r.add(jo.getString("nsrmc"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("qsye"));
                    r.add(jo.getString("zzcjryxm"));
                    r.add(jo.getString("zzcjksrq"));
                    r.add(jo.getString("zzcjjsrq"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("阻止出境人员", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("阻止出境人员-导出失败", e);
        }
    }

}