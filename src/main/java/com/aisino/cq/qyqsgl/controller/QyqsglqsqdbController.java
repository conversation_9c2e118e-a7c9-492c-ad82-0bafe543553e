package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


//"管理后台 - 企业欠税管理-欠税清单")
@RestController
@RequestMapping("/qyqsgl/qyqsglqsqdb")
public class QyqsglqsqdbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglqsqdbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("欠税清单-列表");
//        this.plxgshuj(request);
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String shxydm = param.get("shxydm");
        String nsrmc = param.get("nsrmc");
        String qslb = param.get("qslb");
        String zgswks = param.get("zgswks");
        String ssgly = param.get("ssgly");
        String sfhdlhd = param.get("sfhdlhd");
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"shxydm", shxydm, "string");
        esbService.addParam(paramList,"nsrmc", nsrmc, "string");
        esbService.addParam(paramList,"qslb", qslb, "string");
        esbService.addParam(paramList,"zgswks", zgswks, "string");
        esbService.addParam(paramList,"ssgly", ssgly, "string");
        esbService.addParam(paramList,"sfhdlhd", sfhdlhd, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000063",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/kkjs")
    @ResponseBody
    public Object kkjs(HttpServletRequest request) {
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("欠税清单-扣款解锁");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";
        String yxbz = "Y";
        String kkjssfqr = "-";
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", yxbz, "string");
        esbService.addParam(paramList,"kkjssfqr", kkjssfqr, "string");
        result = esbService.queryEsb("30000063",pageNo, pageSize,"0","true",paramList);
        if (null == result.getData()){
            result.setSuccess(null);
        }
        JSONArray rows = (JSONArray)result.getData();

        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
            String uuid = jo.getString("uuid");
            kkjssfqr = "否";
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            esbService.addParam(paramList,"yxbz", yxbz, "string");
            esbService.addParam(paramList,"kkjssfqr", kkjssfqr, "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            result = esbService.queryEsb("30000062",pageNo, pageSize,"0","true",paramList);
        }
        result.setSuccess(null);
        return result;
    }

    @PostMapping("/kkjsqd")
    @ResponseBody
    public Object kkjsqd(HttpServletRequest request) {
        log.info("欠税清单-扣款解锁清单");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";
        String yxbz = "Y";
        String kkjssfqr = "否";
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", yxbz, "string");
        esbService.addParam(paramList,"kkjssfqr", kkjssfqr, "string");
        return esbService.queryEsb("30000063",pageNo, pageSize,"0","true",paramList);
     }

    @PostMapping("/kkjsqdqr")
    @ResponseBody
    public Object kkjsqdqr(HttpServletRequest request) {
        log.info("欠税清单-扣款解锁清单确认");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
//            欠税清册数据 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            result = esbService.queryEsb("30000063", paramList);
            if (null == result.getData()){
                continue;
            }

            JSONArray rows = (JSONArray)result.getData();
            if(rows.size()==0) {
                continue;
            }

            JSONObject qsqc = rows.getJSONObject(0);
            if (!"扣款锁定".equals(qsqc.getString("skcllx"))){
                continue;
            }

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            esbService.addParam(paramList,"skcllx", "正常", "string");
            esbService.addParam(paramList,"kkjssfqr", "是", "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            result = esbService.queryEsb("30000062",paramList);

        }
        result.setSuccess(null);
        return result;
    }

    @PostMapping("/zzrwxf")
    @ResponseBody
    public Object zzrwxf(HttpServletRequest request) {
        log.info("欠税清单-追征任务下发");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
//            欠税清册数据 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            result = esbService.queryEsb("30000063", paramList);
            if (null == result.getData()){
                continue;
            }

            JSONArray rows = (JSONArray)result.getData();
            if(rows.size()==0) {
                continue;
            }

            JSONObject qsqc = rows.getJSONObject(0);
            if (!"未下发".equals(qsqc.getString("zt"))){
                continue;
            }

//            欠税清册数据 - 修改状态
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"zt", "已下发", "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            result = esbService.queryEsb("30000062", paramList);

//            追征任务 - 查询所有 sfxsj=Y 的数据；修改未N
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"zbuuid", uuid, "string");
            esbService.addParam(paramList,"sfxsj", "Y", "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            result = esbService.queryEsb("30000073", paramList);
            if (null == result.getData()){
                continue;
            }

            rows = (JSONArray)result.getData();
            if(rows.size() > 0) {
                JSONObject jo = rows.getJSONObject(0);
//            追征任务 - 修改 sfxsj=N
                paramList = new ArrayList<>();
                esbService.addParam(paramList,"uuid", jo.getString("uuid"), "string");
                esbService.addParam(paramList,"sfxsj", "N", "string");
                esbService.addParam(paramList,"yxbz", "Y", "string");
                esbService.addParam(paramList,"xgrdm", userCode, "string");
                result = esbService.queryEsb("30000072", paramList);
            }

            //增加新的追征任务

            paramList = new ArrayList<>();

            paramList.add(esbService.addParam("uuid", IdUtil.simpleUUID(), "string"));
            paramList.add(esbService.addParam("zbuuid", qsqc.getString("uuid"), "string"));
            paramList.add(esbService.addParam("xfr", userCode, "string"));
            paramList.add(esbService.addParam("xfrq", DateUtils.strNow(), "string"));
            paramList.add(esbService.addParam("xfbz", "null", "string"));
            paramList.add(esbService.addParam("tjrq", qsqc.getString("tjrq"), "string"));
            paramList.add(esbService.addParam("shxydm", qsqc.getString("shxydm"), "string"));
            paramList.add(esbService.addParam("nsrmc", qsqc.getString("nsrmc"), "string"));
            paramList.add(esbService.addParam("nsrzt", qsqc.getString("nsrzt"), "string"));
            paramList.add(esbService.addParam("jkqx", qsqc.getString("jkqx"), "string"));
            paramList.add(esbService.addParam("sfzrr", qsqc.getString("sfzrr"), "string"));
            paramList.add(esbService.addParam("fddbrsfzhm", qsqc.getString("fddbrsfzhm"), "string"));
            paramList.add(esbService.addParam("fddbrxm", qsqc.getString("fddbrxm"), "string"));
            paramList.add(esbService.addParam("qslb", qsqc.getString("qslb"), "string"));
            paramList.add(esbService.addParam("jyqk", qsqc.getString("jyqk"), "string"));
            paramList.add(esbService.addParam("zgswksdm", qsqc.getString("zgswksdm"), "string"));
            paramList.add(esbService.addParam("zgswks", qsqc.getString("zgswks"), "string"));
            paramList.add(esbService.addParam("jdxzdm", qsqc.getString("jdxzdm"), "string"));
            paramList.add(esbService.addParam("jdxz", qsqc.getString("jdxz"), "string"));
            paramList.add(esbService.addParam("ssglydm", qsqc.getString("ssglydm"), "string"));
            paramList.add(esbService.addParam("ssgly", qsqc.getString("ssgly"), "string"));
            paramList.add(esbService.addParam("qsye", qsqc.getString("qsye"), "string"));
            paramList.add(esbService.addParam("wncq", qsqc.getString("wncq"), "string"));
            paramList.add(esbService.addParam("bnxq", qsqc.getString("bnxq"), "string"));
            paramList.add(esbService.addParam("sfjc", qsqc.getString("sfjc"), "string"));
            paramList.add(esbService.addParam("sfhdlhd", qsqc.getString("sfhdlhd"), "string"));
            paramList.add(esbService.addParam("bz", qsqc.getString("bz"), "string"));
            paramList.add(esbService.addParam("zzrwwcrq", "null", "string"));
            paramList.add(esbService.addParam("zzcs", "null", "string"));
            paramList.add(esbService.addParam("zzjg", "null", "string"));
            paramList.add(esbService.addParam("zzry", "null", "string"));
            paramList.add(esbService.addParam("zzrwzt", "未处理", "string"));
            paramList.add(esbService.addParam("fkr", "null", "string"));
            paramList.add(esbService.addParam("fkrq", "null", "string"));
            paramList.add(esbService.addParam("fkbz", "null", "string"));
            paramList.add(esbService.addParam("sfxsj", "Y", "string"));
            paramList.add(esbService.addParam("lrrdm", userCode, "string"));
            paramList.add(esbService.addParam("xgrDm", userCode, "string"));
            paramList.add(esbService.addParam("yxbz", "Y", "string"));
            result = esbService.queryEsb("30000071", paramList);


        }
        result.setSuccess(null);
        return result;
    }

    @PostMapping("/update")
    @ResponseBody
    public Object update(HttpServletRequest request) {
        log.info("欠税清单-编辑");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"uuid", param.get("uuid"), "string");
        esbService.addParam(paramList,"jyqk", param.get("jyqk"), "string");
        esbService.addParam(paramList,"bz", param.get("bz"), "string");
        esbService.addParam(paramList,"xgrdm", userCode, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000062", paramList);
    }

    @PostMapping("/qstj")
    @ResponseBody
    public Object qstj(HttpServletRequest request) {
        log.info("欠税统计-查询");
        List<Object> list = new ArrayList<>();

        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", "Y", "string");
        esbService.addParam(paramList,"bz", "总计", "string");
        result = esbService.queryEsb("30000064", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            list.add(((JSONArray)result.getData()).getJSONObject(0));
        }

        paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", "Y", "string");
        esbService.addParam(paramList,"bz", "稽查类", "string");
        esbService.addParam(paramList,"sfjc", "是", "string");
        result = esbService.queryEsb("30000064", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            list.add(((JSONArray)result.getData()).getJSONObject(0));
        }

        paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", "Y", "string");
        esbService.addParam(paramList,"bz", "恒大类", "string");
        esbService.addParam(paramList,"sfhdlhd", "是", "string");
        result = esbService.queryEsb("30000064", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            list.add(((JSONArray)result.getData()).getJSONObject(0));
        }

        paramList = new ArrayList<>();
        esbService.addParam(paramList,"yxbz", "Y", "string");
        esbService.addParam(paramList,"bz", "剔稽查恒大类", "string");
        esbService.addParam(paramList,"sfjc", "否", "string");
        esbService.addParam(paramList,"sfhdlhd", "否", "string");
        result = esbService.queryEsb("30000064", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            list.add(((JSONArray)result.getData()).getJSONObject(0));
        }

        result.setSuccess(list);
        return result;
    }

















    /**
     * function 获取列表信息
     */
    @RequestMapping("add")
    @ResponseBody
    public Object add(HttpServletRequest request) {
        log.info("新增数据");
        String tt = DateUtils.strNow("yyMMddHHmmss");
        List<Map<String, Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", tt, "string"));
        paramList.add(esbService.addParam("tjrq", "2025-03-01", "string"));
        paramList.add(esbService.addParam("sjly", "核心征管", "string"));
        paramList.add(esbService.addParam("shxydm", "shxydm" + tt, "string"));
        paramList.add(esbService.addParam("nsrmc", "nsrmc" + tt, "string"));
        paramList.add(esbService.addParam("nsrxydj", "nsrxydj", "string"));
        paramList.add(esbService.addParam("nsrzt", "nsrzt", "string"));
        paramList.add(esbService.addParam("jkqx", "2025-02-02", "string"));
        paramList.add(esbService.addParam("sfzrr", "N", "string"));
        paramList.add(esbService.addParam("fddbrsfzhm", "法人身份证号码"+ tt, "string"));
        paramList.add(esbService.addParam("fddbrxm", "法人姓名"+ tt, "string"));
        paramList.add(esbService.addParam("yzfsrq", "yzfsrq", "string"));
        paramList.add(esbService.addParam("qslb", "大额欠税", "string"));
        paramList.add(esbService.addParam("jyqk", "jyqk", "string"));
        paramList.add(esbService.addParam("zgswksdm", "zgswksdm", "string"));
        paramList.add(esbService.addParam("zgswks", "zgswks", "string"));
        paramList.add(esbService.addParam("jdxzdm", "jdxzdm", "string"));
        paramList.add(esbService.addParam("jdxz", "jdxz", "string"));
        paramList.add(esbService.addParam("ssglydm", "ssglydm", "string"));
        paramList.add(esbService.addParam("ssgly", "ssgly", "string"));
        paramList.add(esbService.addParam("qsye", "333333", "string"));
        paramList.add(esbService.addParam("wncq", "222222", "string"));
        paramList.add(esbService.addParam("bnxq", "111111", "string"));
        paramList.add(esbService.addParam("qsfxdj", "qsfxdj", "string"));
        paramList.add(esbService.addParam("qcnldf", "qcnldf", "string"));
        paramList.add(esbService.addParam("djje", "44444", "string"));
        paramList.add(esbService.addParam("zzsldje", "55555", "string"));
        paramList.add(esbService.addParam("skcllx", "扣款锁定", "string"));
        paramList.add(esbService.addParam("kkjssfqr", "-", "string"));
        paramList.add(esbService.addParam("sfxq", "是", "string"));
        paramList.add(esbService.addParam("sfjc", "否", "string"));
        paramList.add(esbService.addParam("sfhdlhd", "是", "string"));
        paramList.add(esbService.addParam("cjcs", "催缴措施1", "string"));
        paramList.add(esbService.addParam("cjjg", "催缴结果1", "string"));
        paramList.add(esbService.addParam("cjzt", "催缴状态1", "string"));
        paramList.add(esbService.addParam("gdzt", "WGD", "string"));
        paramList.add(esbService.addParam("zzcjzt", "zzcjzt", "string"));
        paramList.add(esbService.addParam("bz", "bz", "string"));
        paramList.add(esbService.addParam("sffsswtzs", "sffsswtzs", "string"));
        paramList.add(esbService.addParam("zt", "未下发", "string"));
        paramList.add(esbService.addParam("lrrdm", "xgrDm", "string"));
        paramList.add(esbService.addParam("xgrdm", "xgrDm", "string"));
//        paramList.add(esbService.addParam("lrrq",DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string"));
//        paramList.add(esbService.addParam("xgrq",DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        return esbService.queryEsb("30000061", paramList);
    }

//    @PostMapping("/plxgshuj")
//    @ResponseBody
    public Object plxgshuj(HttpServletRequest request) {
        log.info("欠税清单-批量修改数据");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "10";
        String pageNo = "49";
        List<Map<String,Object>> paramList = new ArrayList<>();

        result = esbService.queryEsb("30000063",pageNo, pageSize,"0","true",paramList);
        if (null == result.getData()){
            result.setSuccess(null);
        }
        JSONArray rows = (JSONArray)result.getData();

        int tt = 0;
        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
            String uuid = jo.getString("uuid");

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            paramList.add(esbService.addParam("sfjc", "否", "string"));
            paramList.add(esbService.addParam("sfhdlhd", "否", "string"));
            result = esbService.queryEsb("30000062",pageNo, pageSize,"0","true",paramList);
        }

        return null;
    }

    @PostMapping("/HXZG30000096")
    @ResponseBody
    public Object HXZG30000096(HttpServletRequest request) {
        log.info("HXZG30000096");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

            paramList.add(esbService.addParam("NSRSBH", "91500107MAC60QN447","string"));
        result = esbService.queryEsb("30000096",pageNo, pageSize,"0","false",paramList);
        if (null == result.getData()){
            result.setSuccess(null);
        }
        JSONArray rows = (JSONArray)result.getData();

        int tt = 0;
        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
            log.info(jo.toString());
        }

        return null;
    }






    /**
     * function 欠税清单-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletResponse response){
        log.info("欠税清单-导出: " + jsonData );

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String shxydm = param.getString("shxydm");
            String nsrmc = param.getString("nsrmc");
            String qslb = param.getString("qslb");
            String zgswks = param.getString("zgswks");
            String ssgly = param.getString("ssgly");
            String sfhdlhd = param.getString("sfhdlhd");
            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"shxydm", shxydm, "string");
            esbService.addParam(paramList,"nsrmc", nsrmc, "string");
            esbService.addParam(paramList,"qslb", qslb, "string");
            esbService.addParam(paramList,"zgswks", zgswks, "string");
            esbService.addParam(paramList,"ssgly", ssgly, "string");
            esbService.addParam(paramList,"sfhdlhd", sfhdlhd, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000063", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "统计日期", "数据来源", "社会信用代码", "纳税人名称", "纳税人信用等级", "纳税人状态", "法定代表人姓名", "法定代表人身份证号码", "应征发生日期", "欠税类别", "经营情况", "主管税务科所", "街道乡镇", "税收管理员", "欠税余额", "往年陈欠", "本年新欠", "欠税风险等级", "清偿能力得分", "多缴金额", "增值税留抵金额", "税款处理类型", "是否新欠", "是否稽查", "是否恒大类恒大", "催缴措施", "催缴结果", "催缴状态", "备注");
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("tjrq"));
                    r.add(jo.getString("sjly"));
                    r.add(jo.getString("shxydm"));
                    r.add(jo.getString("nsrmc"));
                    r.add(jo.getString("nsrxydj"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("fddbrxm"));
                    r.add(jo.getString("fddbrsfzhm"));
                    r.add(jo.getString("yzfsrq"));
                    r.add(jo.getString("qslb"));
                    r.add(jo.getString("jyqk"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("qsye"));
                    r.add(jo.getString("wncq"));
                    r.add(jo.getString("bnxq"));
                    r.add(jo.getString("qsfxdj"));
                    r.add(jo.getString("qcnldf"));
                    r.add(jo.getString("djje"));
                    r.add(jo.getString("zzsldje"));
                    r.add(jo.getString("skcllx"));
                    r.add(jo.getString("sfxq"));
                    r.add(jo.getString("sfjc"));
                    r.add(jo.getString("sfhdlhd"));
                    r.add(jo.getString("cjcs"));
                    r.add(jo.getString("cjjg"));
                    r.add(jo.getString("cjzt"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("欠税清单", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("欠税清单-导出失败", e);
        }
    }


    /**
     * function 欠税统计-导出
     */
    @RequestMapping("qstjdcFn")
    @ResponseBody
    public void qstjdcFn(String jsonData, HttpServletResponse response){
        log.info("欠税统计-导出: " + jsonData );
        List<Object> list = new ArrayList<>();

        try{

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("", "陈欠总额", "欠税总额", "新欠总额");
            sheetRows.add(headers);

            CommonResult result = new CommonResult();
            List<Map<String,Object>> paramList = new ArrayList<>();
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"bz", "总计", "string");
            result = esbService.queryEsb("30000064", paramList);
            List<String> r1 = new ArrayList<>();
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);
                r1.add(jo.getString("fl"));
                r1.add(jo.getString("qsze"));
                r1.add(jo.getString("cqze"));
                r1.add(jo.getString("xqze"));
            } else {
                r1.add("总计");
                r1.add("");
                r1.add("");
                r1.add("");
            }
            sheetRows.add(r1);

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"bz", "稽查类", "string");
            esbService.addParam(paramList,"sfjc", "是", "string");
            result = esbService.queryEsb("30000064", paramList);
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                list.add(((JSONArray)result.getData()).getJSONObject(0));
            }
            List<String> r2 = new ArrayList<>();
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);
                r2.add(jo.getString("fl"));
                r2.add(jo.getString("qsze"));
                r2.add(jo.getString("cqze"));
                r2.add(jo.getString("xqze"));
            } else {
                r2.add("稽查类");
                r2.add("");
                r2.add("");
                r2.add("");
            }
            sheetRows.add(r2);

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"bz", "恒大类", "string");
            esbService.addParam(paramList,"sfhdlhd", "是", "string");
            result = esbService.queryEsb("30000064", paramList);
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                list.add(((JSONArray)result.getData()).getJSONObject(0));
            }
            List<String> r3 = new ArrayList<>();
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);
                r3.add(jo.getString("fl"));
                r3.add(jo.getString("qsze"));
                r3.add(jo.getString("cqze"));
                r3.add(jo.getString("xqze"));
            } else {
                r3.add("恒大类");
                r3.add("");
                r3.add("");
                r3.add("");
            }
            sheetRows.add(r3);

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"bz", "剔稽查恒大类", "string");
            esbService.addParam(paramList,"sfjc", "否", "string");
            esbService.addParam(paramList,"sfhdlhd", "否", "string");
            result = esbService.queryEsb("30000064", paramList);
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                list.add(((JSONArray)result.getData()).getJSONObject(0));
            }
            List<String> r4 = new ArrayList<>();
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);
                r4.add(jo.getString("fl"));
                r4.add(jo.getString("qsze"));
                r4.add(jo.getString("cqze"));
                r4.add(jo.getString("xqze"));
            } else {
                r4.add("剔稽查恒大类");
                r4.add("");
                r4.add("");
                r4.add("");
            }
            sheetRows.add(r4);

            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("欠税统计", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("欠税统计-导出失败", e);
        }
    }





}