package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

//"管理后台 - 企业欠税管理-追征任务清单-阻止出境上报及反馈")
@RestController
@RequestMapping("/qyqsgl/qyqsglzzrwqdzzcjsbjfkb")
public class QyqsglzzrwqdzzcjsbjfkbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglzzrwqdzzcjsbjfkbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("大额欠税阻止出境企业上报-列表查询");

        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String nsrmc = param.get("nsrmc");
        String nsrzt = param.get("nsrzt");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"nsrmc", nsrmc, "string");
        esbService.addParam(paramList,"nsrzt", nsrzt, "string");
//        esbService.addParam(paramList,"sfqrzzcj", "-", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000087",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/zzcjqysb")
    @ResponseBody
    public Object zzcjqysb(HttpServletRequest request) {
        log.info("大额欠税阻止出境企业上报-阻止出境企业上报");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String zzrwqduuid: ids) {
            String zbuuid = null;
//            追征任务清单 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", zzrwqduuid, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000073", paramList);
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                JSONObject jo = ((JSONArray)result.getData()).getJSONObject(0);
                zbuuid = jo.getString("zbuuid");
            } else {
                continue;
            }

//            追征任务清单-阻止出境上报及反馈 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "zzrwqduuid", zzrwqduuid, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000077", paramList);
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                continue;
            }

//            追征任务清单-阻止出境上报及反馈 - 新增
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", IdUtil.simpleUUID(), "string");
            esbService.addParam(paramList, "zbuuid", zbuuid, "string");
            esbService.addParam(paramList, "zzrwqduuid", zzrwqduuid, "string");
            esbService.addParam(paramList, "zzcjqysbr", userCode, "string");
            esbService.addParam(paramList, "zzcjqysbrq", DateUtils.strNow(), "string");
            esbService.addParam(paramList, "zzcjqysbbz", "null", "string");
            esbService.addParam(paramList, "zzcjqyqrr", "null", "string");
            esbService.addParam(paramList, "zzcjqyqrrq", "null", "string");
            esbService.addParam(paramList, "zzcjqyqrbz", "null", "string");
            esbService.addParam(paramList, "zzcjfkr", "null", "string");
            esbService.addParam(paramList, "zzcjfkrq", "null", "string");
            esbService.addParam(paramList, "zzcjfkbz", "null", "string");
            esbService.addParam(paramList, "sfysb", "是", "string");
            esbService.addParam(paramList, "sfqrzzcj", "-", "string");
            esbService.addParam(paramList, "zzcjrwfkzt", "WFK", "string");
            esbService.addParam(paramList, "bz", "null", "string");
            esbService.addParam(paramList, "lrrdm", userCode, "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000075", paramList);
        }

        result.setSuccess(null);
        return result;
    }

    @PostMapping("/zzcjqymd")
    @ResponseBody
    public Object zzcjqymd(HttpServletRequest request) {
        log.info("大额欠税阻止出境-阻止出境企业名单-列表查询");

        String pageSize = "9999";
        String pageNo = "1";

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"sfysb", "是", "string");
        esbService.addParam(paramList,"sfqrzzcj", "-", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000087",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/qrzzcj")
    @ResponseBody
    public Object qrzzcj(HttpServletRequest request) {
        log.info("大额欠税阻止出境-阻止出境企业名单-确认阻止");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
//            追征任务清单-阻止出境上报及反馈 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000077", paramList);
            JSONObject jo = null;
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                jo = ((JSONArray)result.getData()).getJSONObject(0);
            } else {
                continue;
            }

            if (!"-".equals(jo.getString("sfqrzzcj"))){
                continue;
            }

//            追征任务清单-阻止出境上报及反馈 - 更新
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "sfqrzzcj", "是", "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000076", paramList);
        }

        result.setSuccess(null);
        return result;
    }

    @PostMapping("/qxzzcj")
    @ResponseBody
    public Object qxzzcj(HttpServletRequest request) {
        log.info("大额欠税阻止出境-阻止出境企业名单-取消阻止");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
//            追征任务清单-阻止出境上报及反馈 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000077", paramList);
            JSONObject jo = null;
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                jo = ((JSONArray)result.getData()).getJSONObject(0);
            } else {
                continue;
            }

            if (!"-".equals(jo.getString("sfqrzzcj"))){
                continue;
            }

//            追征任务清单-阻止出境上报及反馈 - 更新
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "sfqrzzcj", "否", "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000076", paramList);
        }

        result.setSuccess(null);
        return result;
    }

    @PostMapping("/zzcjrwfkqr")
    @ResponseBody
    public Object zzcjrwfkqr(HttpServletRequest request) {
        log.info("大额欠税阻止出境企业上报-阻止出境任务反馈-确认");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String uuid = param.get("uuid");
        List<Map<String,Object>> paramList = new ArrayList<>();

//            大额欠税阻止出境企业上报-阻止出境任务反馈 - 查询
        paramList = new ArrayList<>();
        esbService.addParam(paramList,"uuid", uuid, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        result = esbService.queryEsb("30000077", paramList);
        JSONObject fkObj = null;
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
            fkObj = ((JSONArray)result.getData()).getJSONObject(0);
        } else {
            return result;
        }

        if ("否".equals(fkObj.getString("sfysb")) || "否".equals(fkObj.getString("sfqrzzcj"))
                || "YFK".equals(fkObj.getString("zzcjrwfkzt")) ){
            return result;
        }
//            追征任务清单-阻止出境上报及反馈 - 更新
        paramList = new ArrayList<>();
        esbService.addParam(paramList, "uuid", uuid, "string");
        esbService.addParam(paramList, "zzcjfkr", userCode, "string");
        esbService.addParam(paramList, "zzcjfkrq", DateUtils.strNow(), "string");
        esbService.addParam(paramList, "zzcjrwfkzt", "YFK", "string");
        esbService.addParam(paramList, "xgrdm", userCode, "string");
        esbService.addParam(paramList, "yxbz", "Y", "string");
        result = esbService.queryEsb("30000076", paramList);

//            企业欠税管理-大额欠税阻止出境申请表 - 查询
        paramList = new ArrayList<>();
        esbService.addParam(paramList, "zzcjsbjfkuuid", uuid, "string");
        esbService.addParam(paramList, "yxbz", "Y", "string");
        result = esbService.queryEsb("30000080", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
//            企业欠税管理-大额欠税阻止出境申请表 - 更新
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", ((JSONArray)result.getData()).getJSONObject(0).getString("uuid"), "string");
            esbService.addParam(paramList, "yxbz", "N", "string");
            result = esbService.queryEsb("30000079", paramList);
        } else {
        }


//            企业欠税管理-大额欠税阻止出境申请表 - 新增
        paramList = new ArrayList<>();
        esbService.addParam(paramList, "uuid", IdUtil.simpleUUID(), "string");
        esbService.addParam(paramList, "zbuuid", fkObj.getString("zbuuid"), "string");
        esbService.addParam(paramList, "zzcjsbjfkuuid", uuid, "string");
        esbService.addParam(paramList, "zzcjtsr", "null", "string");
        esbService.addParam(paramList, "zzcjtsrrq", "null", "string");
        esbService.addParam(paramList, "zzcjryxm", "null", "string");
        esbService.addParam(paramList, "lxdh", "null", "string");
        esbService.addParam(paramList, "sfzh", "null", "string");
        esbService.addParam(paramList, "tsrq", "null", "string");
        esbService.addParam(paramList, "swgly", "null", "string");
        esbService.addParam(paramList, "glylxfs", "null", "string");
        esbService.addParam(paramList, "zzcjtsrbz", "null", "string");
        esbService.addParam(paramList, "zzcjzt", "未阻止", "string");
        esbService.addParam(paramList, "zzcjksrq", "null", "string");
        esbService.addParam(paramList, "zzcjjsrq", "null", "string");
        esbService.addParam(paramList,"lrrdm", userCode, "string");
        esbService.addParam(paramList,"xgrdm", userCode, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        result = esbService.queryEsb("30000078", paramList);

        return result;
    }



    /**
     * function 大额欠税阻止出境企业上报-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletResponse response){
        log.info("大额欠税阻止出境企业上报-导出: " + jsonData );

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String nsrmc = param.getString("nsrmc");
            String nsrzt = param.getString("nsrzt");

            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"nsrmc", nsrmc, "string");
            esbService.addParam(paramList,"nsrzt", nsrzt, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000087", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "统计日期", "社会信用代码", "纳税人名称", "纳税人状态", "法定代表人姓名", "法定代表人身份证号码", "欠税类别", "经营情况", "主管税务科所", "街道乡镇", "税收管理员", "欠税余额", "往年陈欠", "本年新欠", "税款处理类型", "是否已上报", "是否确认阻止出境", "备注" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("tjrq"));
                    r.add(jo.getString("shxydm"));
                    r.add(jo.getString("nsrmc"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("fddbrxm"));
                    r.add(jo.getString("fddbrsfzhm"));
                    r.add(jo.getString("qslb"));
                    r.add(jo.getString("jyqk"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("qsye"));
                    r.add(jo.getString("wncq"));
                    r.add(jo.getString("bnxq"));
                    r.add(jo.getString("skcllx"));
                    r.add(jo.getString("sfysb"));
                    r.add(jo.getString("sfqrzzcj"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("大额欠税阻止出境企业上报", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("大额欠税阻止出境企业上报-导出失败", e);
        }
    }

}