package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.qyqsgl.constant.DmMaps;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

// "管理后台 - 企业欠税管理-大额欠税阻止出境申请")
@RestController
@RequestMapping("/qyqsgl/qyqsgldeqszzcjsqb")
public class QyqsgldeqszzcjsqbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsgldeqszzcjsqbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;
    @Resource(name = "sfzzThreadPool")
    private Executor executor;
    final private String qyqsglPath = "/qyqsgl";

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("大额欠税阻止出境-列表查询");
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String nsrmc = param.get("nsrmc");
        String nsrzt = param.get("nsrzt");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"nsrmc", nsrmc, "string");
        esbService.addParam(paramList,"nsrzt", nsrzt, "string");
        esbService.addParam(paramList,"zgswks", zgswks, "string");
        esbService.addParam(paramList,"fyxbz", "Y", "string");
        return esbService.queryEsb("30000088",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/zzcjtsga")
    @ResponseBody
    public Object zzcjtsga(HttpServletRequest request) {
        log.info("大额欠税阻止出境-阻止出境-推送公安");

        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String uuid = param.get("uuid");
        String zzcjryxm = param.get("zzcjryxm");
        String lxdh = param.get("lxdh");
        String sfzh = param.get("sfzh");
        String tsrq = param.get("tsrq");
        String swgly = param.get("swgly");
        String glylxfs = param.get("glylxfs");
        String zzcjtsrbz = param.get("zzcjtsrbz");
        List<Map<String,Object>> paramList = new ArrayList<>();

//            企业欠税管理-大额欠税阻止出境申请表 - 查询
        esbService.addParam(paramList, "uuid", uuid, "string");
        esbService.addParam(paramList, "yxbz", "Y", "string");
        result = esbService.queryEsb("30000080", paramList);
        if (null != result.getData()
                && ((JSONArray)result.getData()).size()>0){
//            企业欠税管理-大额欠税阻止出境申请表 - 更新
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", ((JSONArray)result.getData()).getJSONObject(0).getString("uuid"), "string");

            esbService.addParam(paramList,"zzcjryxm", zzcjryxm, "string");
            esbService.addParam(paramList,"lxdh", lxdh, "string");
            esbService.addParam(paramList,"sfzh", sfzh, "string");
            esbService.addParam(paramList,"tsrq", tsrq, "string");
            esbService.addParam(paramList,"swgly", swgly, "string");
            esbService.addParam(paramList,"glylxfs", glylxfs, "string");
            paramList.add(esbService.addParam("zzcjtsrbz",zzcjtsrbz,"string"));

            esbService.addParam(paramList,"zzcjtsr", userCode, "string");
            esbService.addParam(paramList,"zzcjtsrrq", DateUtils.strNow(), "string");

            esbService.addParam(paramList, "zzcjzt", "已阻止", "string");
            esbService.addParam(paramList,"zzcjksrq", DateUtils.strNow(), "string");
            esbService.addParam(paramList,"zzcjjsrq", DateUtils.strNowAddYear(1), "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            result = esbService.queryEsb("30000079", paramList);
        } else {}

        result.setSuccess(null);
        return result;

    }





    /**
     * function 大额欠税阻止出境-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletRequest request, HttpServletResponse response){
        log.info("大额欠税阻止出境-导出: " + jsonData );
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String nsrmc = param.getString("nsrmc");
            String nsrzt = param.getString("nsrzt");
            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"nsrmc", nsrmc, "string");
            esbService.addParam(paramList,"nsrzt", nsrzt, "string");
            esbService.addParam(paramList,"zgswks", zgswks, "string");
            esbService.addParam(paramList,"fyxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000088", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "社会信用代码", "纳税人名称", "纳税人状态", "法定代表人姓名", "法定代表人身份证号码", "经营情况", "主管税务科所", "街道乡镇", "税收管理员", "欠税余额", "往年陈欠", "本年新欠", "欠税风险等级", "清偿能力得分", "多缴金额", "增值税留抵金额", "是否恒大类恒大", "催缴状态", "催缴措施", "催缴结果", "归档状态", "阻止出境状态", "备注" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("shxydm"));
                    r.add(jo.getString("nsrmc"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("fddbrxm"));
                    r.add(jo.getString("fddbrsfzhm"));
                    r.add(jo.getString("jyqk"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("qsye"));
                    r.add(jo.getString("wncq"));
                    r.add(jo.getString("bnxq"));
                    r.add(jo.getString("qsfxdj"));
                    r.add(jo.getString("qcnldf"));
                    r.add(jo.getString("djje"));
                    r.add(jo.getString("zzsldje"));
                    r.add(jo.getString("sfhdlhd"));
                    r.add(jo.getString("cjzt"));
                    r.add(jo.getString("cjcs"));
                    r.add(jo.getString("cjjg"));
                    r.add(jo.getString("gdzt"));
                    r.add(jo.getString("zzcjzt"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("大额欠税阻止出境", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("大额欠税阻止出境-导出失败", e);
        }
    }

    @PostMapping("/guidang")
    @ResponseBody
    public Object guidang(HttpServletRequest request) {
        log.info("大额欠税阻止出境-归档");
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String zzcjsbjfkuuid: ids) {
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"zzcjsbjfkuuid", zzcjsbjfkuuid, "string");
//            esbService.addParam(paramList,"fyxbz", "Y", "string");
            result = esbService.queryEsb("30000088", paramList);
            if (null == result.getData()){
                continue;
            }

            JSONArray rows = (JSONArray)result.getData();
            if(rows.size()==0) {
                continue;
            }

            JSONObject obj = rows.getJSONObject(0);
            result = this.wjzlgd(zzcjsbjfkuuid, obj.getString("shxydm"), obj.getString("nsrmc"),  userCode,userName);
        }
        return result;
    }
    /**
     *function 文件资料归档，先获取需要归档数据，然后分别进行电子档案系统归档和本地归档
     * @param zzcjsbjfkuuid 阻止出境上报及反馈表uuid
     * @param nsrsbh 纳税人识别号
     * @param nsrmc 纳税人名称
     * @param userCode 办理人员编码
     */
    public CommonResult wjzlgd(String zzcjsbjfkuuid, String nsrsbh, String nsrmc, String userCode, String userName){
        CommonResult rtn = new CommonResult();
        String djxh = nsrsbh;
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"zbuuid", zzcjsbjfkuuid, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result =  esbService.queryEsb("30000086", paramList);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){
                result.setSuccess(null);
                return result;
            } else {
                JSONArray rows = new JSONArray();
                JSONArray ja = (JSONArray) result.getData();
                for(Object data : ja) {
                    JSONObject jsonObject = (JSONObject) data;
                    if ("已归档".equals(jsonObject.getString("gdzt"))) {
                        continue;
                    }
                    rows.add(data);
                }
                if (rows.size()>0){
                    //电子档案系统初始化并归档主表附件
                    rtn = dzdaxtGd(rows,zzcjsbjfkuuid,djxh,nsrsbh,nsrmc);

                    //本地档案系统归档
                    bddaxtGd(rows,"DEQS"+DateUtils.strNow("yyyyMMddHHmmss"),zzcjsbjfkuuid,userCode,nsrmc,nsrsbh,userName);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail(e.getMessage());
        }
        return rtn;
    }

    /**
     * 本地档案系统归档
     * @param dataList 要归档的数据集合
     * @param sqbh 申请编号
     * @param zbuuid zbuuid
     * @param userCode 办理人员代码
     * @return
     */
    private CommonResult bddaxtGd(JSONArray dataList,String sqbh,String zbuuid,String userCode,String nsrmc,String nsrsbh,String userName){
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        int fjsl = dataList.size();
        boolean existFlag = false;

        //写入档案表信息
        paramList.clear();
        zbuuid = UUID.randomUUID().toString().replaceAll("-", "");
        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
        paramList.add(esbService.addParam("ywbh",sqbh,"string"));
        paramList.add(esbService.addParam("ywlx","大额欠税","string"));
        paramList.add(esbService.addParam("gdnd", String.valueOf(LocalDate.now().getYear()),"string"));
        paramList.add(esbService.addParam("gdrq", LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"),"string"));
        paramList.add(esbService.addParam("blry", StringUtils.isBlank(userName) ? "管理员" : userName,"string"));
        paramList.add(esbService.addParam("fjsl",String.valueOf(fjsl),"string"));
        paramList.add(esbService.addParam("gdfs","自动归档","string"));
        paramList.add(esbService.addParam("ywid",zbuuid,"string"));
        paramList.add(esbService.addParam("gdzt","已归档","string"));
        paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
        paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
        paramList.add(esbService.addParam("lrrdm",userCode,"string"));
        paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
        paramList.add(esbService.addParam("xgrdm",userCode,"string"));
        paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
        paramList.add(esbService.addParam("yxbz","Y","string"));
        rtn = esbService.queryEsb("30000055","1", "10","0","false",paramList);

        for(Object data : dataList){
            JSONObject fjdata = (JSONObject) data;
            //写入档案明细表信息
            paramList.clear();
            paramList.add(esbService.addParam("uuid",fjdata.getString("uuid"),"string"));
            paramList.add(esbService.addParam("pzbh","0","string"));
            paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
            paramList.add(esbService.addParam("wjzllx",fjdata.getString("wjzllx"),"string"));
            paramList.add(esbService.addParam("wjgs",fjdata.getString("wjgs") ,"string"));
            paramList.add(esbService.addParam("wjm",fjdata.getString("wjm"),"string"));
            paramList.add(esbService.addParam("wjlj",fjdata.getString("wjlj"),"string"));
            paramList.add(esbService.addParam("wjurl","","string"));
            paramList.add(esbService.addParam("wjlx",fjdata.getString("wjlx"),"string"));
            paramList.add(esbService.addParam("wjdx",fjdata.getString("wjdx"),"string"));
            paramList.add(esbService.addParam("wjnr","","string"));
            paramList.add(esbService.addParam("lrrdm",userCode,"string"));
            paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
            paramList.add(esbService.addParam("xgrdm",userCode,"string"));
            paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
            paramList.add(esbService.addParam("yxbz","Y","string"));
            paramList.add(esbService.addParam("nsrsfqz",fjdata.getString("nsrsfqz"),"string"));
            paramList.add(esbService.addParam("sfxyswqr",fjdata.getString("sfxyswqr"),"string"));
            paramList.add(esbService.addParam("swsfyqr",fjdata.getString("swsfyqr"),"string"));
            paramList.add(esbService.addParam("swsfqz",fjdata.getString("swsfqz"),"string"));
            paramList.add(esbService.addParam("gdzt","已归档","string"));
            paramList.add(esbService.addParam("gldh",fjdata.getString("gldh"),"string"));
            paramList.add(esbService.addParam("zldjxh",fjdata.getString("zldjxh"),"string"));
            paramList.add(esbService.addParam("zlurlnw",fjdata.getString("zlurlnw"),"string"));
            paramList.add(esbService.addParam("sfts",fjdata.getString("sfts"),"string"));
            rtn = esbService.queryEsb("30000056","1", "10","0","false",paramList);
        }
        return rtn;
    }
    /**
     * 电子档案系统对要归档的数据进行初始化并归档
     * @param dataList 要归档的数据集合
     * @param uuid   是主表附件还是房屋附件，分别取对应的uuid
     * @return
     */
    private CommonResult dzdaxtGd(JSONArray dataList,String uuid,String djxh,String nsrsbh,String nsrmc){
        String gldh = esbService.MD5(uuid);
        CommonResult result = new CommonResult();
        try {
            //初始化
            CommonResult init = dzzlkService.init(gldh, djxh, nsrsbh, nsrmc);
            if(init.isSuccess()) {
                List<CompletableFuture<String>> futures = new ArrayList<>();
                for(Object data : dataList){
                    JSONObject jsonObject = (JSONObject) data;
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                        List<Map<String,Object>> paramList = new ArrayList<>();
                        CommonResult rtn = new CommonResult();
                        String fjuuid = jsonObject.getString("uuid");
                        String wjlx=jsonObject.getString("wjlx");
                        String wjlj=jsonObject.getString("wjlj");
                        String wjzllx=jsonObject.getString("wjzllx");
                        String zllxdm = "";
                        wjzllx=wjzllx == null?"":wjzllx;
                        if(wjzllx.contains("大额欠税")){//大额欠税  阻止出境
                            zllxdm = "000000";
                        }else{
                            zllxdm = "000000";//其他资料
                        }
                        if(StringUtils.isNotEmpty(zllxdm)){
                            try{
                                String zldata = FileUtils.readFileToString(new File(filePath + qyqsglPath + File.separator+wjlj+File.separator+fjuuid));
                                if(StringUtils.isNotEmpty(zldata)){
                                    rtn.addResult(dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata,wjlx));
                                    if(rtn.isSuccess()){
                                        Map<String, Object> zlscObj = (Map<String, Object>)rtn.getData();
                                        String zldjxh = (String)zlscObj.get("zldjxh");
                                        String zlurlnw = (String)zlscObj.get("zlurlnw");
                                        paramList.clear();
                                        paramList = new ArrayList<>();
                                        paramList.add(esbService.addParam("uuid", jsonObject.getString("uuid"),"string"));
                                        paramList.add(esbService.addParam("gdzt", "已归档","string"));
                                        paramList.add(esbService.addParam("zlurlnw", zlurlnw,"string"));
                                        paramList.add(esbService.addParam("zldjxh", zldjxh,"string"));
                                        paramList.add(esbService.addParam("gldh", gldh,"string"));
                                        rtn.addResult(esbService.queryEsb("30000085", paramList));
                                    }
                                }else{
                                    rtn.setFail(fjuuid+"文件不存在");
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                                log.info(uuid+"归档时发生异常"+e.getMessage());
                                rtn.setFail(e.getMessage());
                            }
                        }
                        return "success";
                    },executor).exceptionally(ex -> {
                        String fjuuid = jsonObject.getString("uuid");
                        log.error("uuid为：【" + fjuuid + "】的数据电子归档失败，原因为：" + ex.getMessage());
                        return "【uuid："+fjuuid+"】";
                    });
                    futures.add(future);
                }
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                //默认推送成功
                boolean flag = true;
                StringBuffer stringBuffer = new StringBuffer("以下数据归档异常：");
                //等待所有异步任务完成
                allFutures.get(300L, TimeUnit.SECONDS);
                for(CompletableFuture<String> future : futures){
                    String taskResult = future.get();
                    if(!StringUtils.equals(taskResult,"success")){
                        //有推送失败的，则整体标记为失败
                        flag = false;
                        stringBuffer.append(taskResult);
                    }
                }
                if(flag){
                    result = dzzlkService.sxzttz(gldh, djxh, nsrsbh, nsrmc);
                }
            }
        } catch (Exception e) {
            log.error("归档发生异常：{}",e.getMessage());
            result.setFail(e.getMessage());
        }
        return result;
    }

}