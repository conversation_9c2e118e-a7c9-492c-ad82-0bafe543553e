package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.util.IdUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

//"管理后台 - 征期维护")
@RestController
@RequestMapping("/qyqsgl/zqwh")
public class ZqwhController {
    private static  final Logger log = LoggerFactory.getLogger(ZqwhController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("欠税清单-征期维护查询");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();
//        征期维护查询
        paramList = new ArrayList<>();
        esbService.addParam(paramList,"nd", param.get("nd"), "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000067", paramList);
    }

    @PostMapping("/save")
    @ResponseBody
    public Object createZqwh(HttpServletRequest request) {
        log.info("欠税清单-征期维护保存");

        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();
//            征期维护查询
        esbService.addParam(paramList,"nd", param.get("nd"), "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        JSONArray dataList = (JSONArray)esbService.queryEsb("30000067", paramList).getData();

        int sort = 11;
        for (int i = 1; i <13 ; i++) {
            if (StringUtils.isBlank(param.get("zzrq"+i))) {
                continue;
            }
            String uuid = null;
            for(Object obj : dataList){
                //写入档案明细表信息
                JSONObject jo = (JSONObject) obj;
                if (sort == jo.getInteger("bz")){
                    uuid = jo.getString("uuid");
                }
            }

//            征期维护 - 保存
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"nd", param.get("nd"), "string");
            if (i<10) {
                esbService.addParam(paramList,"qqrq", param.get("nd")+"-0"+i+"-01 00:00:00", "string");
            } else {
                esbService.addParam(paramList,"qqrq", param.get("nd")+"-"+i+"-01 00:00:00", "string");
            }
            esbService.addParam(paramList,"zzrq", param.get("zzrq"+i).replaceAll("：",""), "string");
            esbService.addParam(paramList,"bz", sort+"", "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            if (null != uuid){
                esbService.addParam(paramList,"uuid", uuid, "string");
                result = esbService.queryEsb("30000066", paramList);
            } else {
                //新增
                esbService.addParam(paramList,"uuid", IdUtil.simpleUUID(), "string");
                esbService.addParam(paramList,"lrrdm", userCode, "string");
                result = esbService.queryEsb("30000065", paramList);
            }
            sort++;
        }
        result.setSuccess(null);
        return result;
    }


}