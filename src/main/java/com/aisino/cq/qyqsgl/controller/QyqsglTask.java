package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.util.IdUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.qyqsgl.constant.DmMaps;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

//@Component
@RestController
@RequestMapping("/t")
public class QyqsglTask {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglTask.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    /**
     * 企业欠税管理 - 欠税清单数据获取
     *  cron = "0 0 0 * * ?"   每天0点执行一次（执行时判断是不是1号和征期结束）
     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    @Scheduled(cron = "0 23 11 * * ?")
    @GetMapping("/p")
    @ResponseBody
    public void qyqsgl_qsqd_jinsan(Integer retry){
        if (!this.sf_zhixing()){
            log.info("企业欠税管理 - 欠税清单数据获取 - HXZG30000097 - 非月初、非征期截止日");
            return;
        }

        if (retry==null){
            retry = 0;
        }
        log.info("企业欠税管理 - 欠税清单数据获取 - HXZG30000097");

        List<Map<String,Object>> paramList = new ArrayList<>();

        paramList.add(esbService.addParam("YZFSRQQ", "2025-01-01","string"));
        paramList.add(esbService.addParam("YZFSRQZ", "2025-03-31","string"));
//        paramList.add(esbService.addParam("NSRSBH", "91500107596703654B","string"));
        CommonResult result = esbService.queryEsb("30000097", paramList);
        if (!"00".equals(result.getCode())){
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (retry<1){
                this.qyqsgl_qsqd_jinsan(retry+1);
            }
            return;
        }

//        欠税清单 数据库 已有数据
        JSONArray yysjData = this.qsqd_cx_all();
        Map<String, List<Object>> qsqdDataMap = yysjData.stream().collect(Collectors.groupingBy(item -> ((JSONObject)item).getString("shxydm")));
        JSONArray rows = (JSONArray)result.getData();
//        csye 对应qsye
        Map<String, List<Object>> rowsMap = rows.stream()
                .filter(item -> !((JSONObject)item).getString("csye").startsWith("0")
                        || !((JSONObject)item).getString("bnxq").startsWith("0")
                        || !((JSONObject)item).getString("wncq").startsWith("0"))
                .collect(Collectors.groupingBy(item -> ((JSONObject)item).getString("nsrsbh")));
        for (Map.Entry<String,List<Object>> entry: rowsMap.entrySet()) {
            log.info("-------------------------------" + entry.getKey());
            BigDecimal qsye = BigDecimal.ZERO;
            BigDecimal wncq = BigDecimal.ZERO;
            BigDecimal bnxq = BigDecimal.ZERO;
            JSONObject qsqdData = null;
            String zbuuid = IdUtil.simpleUUID();
            if (qsqdDataMap.containsKey(entry.getKey())){
                qsqdData = (JSONObject)qsqdDataMap.get(entry.getKey()).get(0);
                zbuuid = qsqdData.getString("uuid");
            }
//        欠税明细 数据库 已有数据
            JSONArray yymxData = this.qsmx_cx_all(qsqdData);
            Map<String, List<Object>> qsmxDataMap = new HashMap<>();
            if (null!=yymxData){
                qsmxDataMap = yymxData.stream().collect(Collectors.groupingBy(item -> ((JSONObject)item).getString("qsrq")+((JSONObject)item).getString("yzfsrq")));
            }
            for (Object obj: entry.getValue()) {
                JSONObject mxObj = (JSONObject) obj;
                if (qsmxDataMap.containsKey(mxObj.getString("zspmDm") + mxObj.getString("yzfsrq"))){
                    JSONObject oldObj = (JSONObject) qsmxDataMap.get(mxObj.getString("zspmDm") + mxObj.getString("yzfsrq")).get(0);
                    oldObj.put("qszl", "false");//不删除标记
//                    修改
                    this.qsmx_cx_update(mxObj, oldObj);
                } else {
//                    新增
                    mxObj.put("zbuuid", zbuuid);
                    this.qsmx_cx_add(mxObj);
                }
                qsye = qsye.add(StringUtils.isBlank(mxObj.getString("csye")) ? BigDecimal.ZERO : mxObj.getBigDecimal("csye"));//        csye 对应qsye
                wncq = wncq.add(StringUtils.isBlank(mxObj.getString("wncq")) ? BigDecimal.ZERO : mxObj.getBigDecimal("wncq"));
                bnxq = bnxq.add(StringUtils.isBlank(mxObj.getString("bnxq")) ? BigDecimal.ZERO : mxObj.getBigDecimal("bnxq"));
            }

//          删除 本次未提供欠税明细的数据
            for (Map.Entry<String,List<Object>> oldMx: qsmxDataMap.entrySet()) {
                JSONObject delData = (JSONObject) oldMx.getValue().get(0);
                if ("false".equals(delData.getString("qszl"))){
                    continue;
                }
//                 删除
                this.qsmx_cx_delete(delData);
            }

            JSONObject qdObj = (JSONObject) entry.getValue().get(0);
            qdObj.put("uuid", zbuuid);
            qdObj.put("shxydm", qdObj.getString("nsrsbh"));
            qdObj.put("qsye", qsye.toString());
            qdObj.put("wncq", wncq.toString());
            qdObj.put("bnxq", bnxq.toString());
            if (qsye.compareTo(new BigDecimal("100000"))==1){
                qdObj.put("qslb", "大额欠税");
            } else if (qsye.compareTo(new BigDecimal("50000"))<0){
                qdObj.put("qslb", "小额欠税");
            } else {
                qdObj.put("qslb", "一般欠税");
            }

            qdObj.put("sfjc", "否");
            if ("0111".equals(qdObj.getString("sksxDm"))
                    || "0401".equals(qdObj.getString("sksxDm"))){
                qdObj.put("sfjc", "是");
            }
            qdObj.put("sfhdlhd", DmMaps.sfhdlhd.containsKey("shxydm") ? "是":"否");
            if (null == qsqdData){
//                新增
                this.qsqd_add(qdObj);
            } else {
//                修改
                this.qsqd_update(qdObj, qsqdData);
            }
        }

    }

    private boolean sf_zhixing(){
        boolean sf_zhixing = false;
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
//            征期维护查询
        esbService.addParam(paramList,"nd", DateUtils.strNow("yyyy"), "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        JSONArray dataList = (JSONArray)esbService.queryEsb("30000067", paramList).getData();
        String date = DateUtils.strNow("yyyy-MM-dd");
//        date = "2025-02-16" ;
        for(Object obj : dataList){
            //写入档案明细表信息
            JSONObject jo = (JSONObject) obj;
            if (jo.getString("qqrq").startsWith(date) || jo.getString("zzrq").startsWith(date)){
                sf_zhixing = true;
                break;
            }
        }

        return sf_zhixing;
    }

    private JSONArray qsqd_cx_all(){
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        result = esbService.queryEsb("30000063", paramList);
        if (null == result.getData()){
            return null;
        }
        return (JSONArray)result.getData();
    }
    private void qsqd_add(JSONObject qsqdData){
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", qsqdData.getString("uuid"), "string"));
        paramList.add(esbService.addParam("tjrq", DateUtils.strNow("yyyy-MM-dd"), "string"));
        paramList.add(esbService.addParam("sjly", "核心征管", "string"));
        paramList.add(esbService.addParam("shxydm", qsqdData.getString("shxydm"), "string"));
        paramList.add(esbService.addParam("nsrmc", qsqdData.getString("nsrmc"), "string"));
        paramList.add(esbService.addParam("nsrxydj", "-", "string"));
        paramList.add(esbService.addParam("nsrzt", qsqdData.getString("nsrzt"), "string"));
        paramList.add(esbService.addParam("jkqx", qsqdData.getString("jkqx"), "string"));
        paramList.add(esbService.addParam("sfzrr", "N", "string"));
        paramList.add(esbService.addParam("fddbrsfzhm", qsqdData.getString("fddbrsfzhm"), "string"));
        paramList.add(esbService.addParam("fddbrxm", qsqdData.getString("fddbrxm"), "string"));
        paramList.add(esbService.addParam("yzfsrq", qsqdData.getString("yzfsrq"), "string"));
        paramList.add(esbService.addParam("qslb", qsqdData.getString("qslb"), "string"));
        paramList.add(esbService.addParam("jyqk", "-", "string"));
        paramList.add(esbService.addParam("zgswksdm", "-", "string"));
        paramList.add(esbService.addParam("zgswks", qsqdData.getString("zgswks"), "string"));
        paramList.add(esbService.addParam("jdxzdm", "-", "string"));
        paramList.add(esbService.addParam("jdxz", qsqdData.getString("jdxz"), "string"));
        paramList.add(esbService.addParam("ssglydm", "-", "string"));
        paramList.add(esbService.addParam("ssgly", qsqdData.getString("gly"), "string"));
        paramList.add(esbService.addParam("qsye", qsqdData.getString("qsye"), "string"));
        paramList.add(esbService.addParam("wncq", qsqdData.getString("wncq"), "string"));
        paramList.add(esbService.addParam("bnxq", qsqdData.getString("bnxq"), "string"));
        paramList.add(esbService.addParam("qsfxdj", "-", "string"));
        paramList.add(esbService.addParam("qcnldf", "-", "string"));
        paramList.add(esbService.addParam("djje", "-", "string"));
        paramList.add(esbService.addParam("zzsldje", "-", "string"));
        paramList.add(esbService.addParam("skcllx", DmMaps.skcllx_dm.get(qsqdData.getString("skcllxDm")), "string"));
        paramList.add(esbService.addParam("kkjssfqr", "-", "string"));
        paramList.add(esbService.addParam("sfxq", "是", "string"));
        paramList.add(esbService.addParam("sfjc", qsqdData.getString("sfjc"), "string"));
        paramList.add(esbService.addParam("sfhdlhd",qsqdData.getString("sfhdlhd"), "string"));
        paramList.add(esbService.addParam("cjcs", "-", "string"));
        paramList.add(esbService.addParam("cjjg", "-", "string"));
        paramList.add(esbService.addParam("cjzt", "-", "string"));
        paramList.add(esbService.addParam("gdzt", "WGD", "string"));
        paramList.add(esbService.addParam("zzcjzt", "-", "string"));
        paramList.add(esbService.addParam("bz", "", "string"));
        paramList.add(esbService.addParam("sffsswtzs", "-", "string"));
        paramList.add(esbService.addParam("zt", "未下发", "string"));
        paramList.add(esbService.addParam("lrrdm", "110", "string"));
        paramList.add(esbService.addParam("xgrdm", "110", "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        esbService.queryEsb("30000061", paramList);
    }
    private void qsqd_update(JSONObject qsqdData, JSONObject oldData){
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", oldData.getString("uuid"), "string"));
        paramList.add(esbService.addParam("tjrq", DateUtils.strNow("yyyy-MM-dd"), "string"));
        paramList.add(esbService.addParam("shxydm", qsqdData.getString("shxydm"), "string"));
        paramList.add(esbService.addParam("nsrmc", qsqdData.getString("nsrmc"), "string"));
        paramList.add(esbService.addParam("nsrzt", qsqdData.getString("nsrzt"), "string"));
        paramList.add(esbService.addParam("jkqx", qsqdData.getString("jkqx"), "string"));
        paramList.add(esbService.addParam("fddbrsfzhm", qsqdData.getString("fddbrsfzhm"), "string"));
        paramList.add(esbService.addParam("fddbrxm", qsqdData.getString("fddbrxm"), "string"));
        paramList.add(esbService.addParam("yzfsrq", qsqdData.getString("yzfsrq"), "string"));
        paramList.add(esbService.addParam("qslb", qsqdData.getString("qslb"), "string"));
        paramList.add(esbService.addParam("zgswks", qsqdData.getString("zgswks"), "string"));
        paramList.add(esbService.addParam("jdxz", qsqdData.getString("jdxz"), "string"));
        paramList.add(esbService.addParam("ssgly", qsqdData.getString("gly"), "string"));
        paramList.add(esbService.addParam("qsye", qsqdData.getString("qsye"), "string"));
        paramList.add(esbService.addParam("wncq", qsqdData.getString("wncq"), "string"));
        paramList.add(esbService.addParam("bnxq", qsqdData.getString("bnxq"), "string"));
        paramList.add(esbService.addParam("skcllx", DmMaps.skcllx_dm.get(qsqdData.getString("skcllxDm")), "string"));
        paramList.add(esbService.addParam("kkjssfqr", "-", "string"));
        paramList.add(esbService.addParam("sfjc", qsqdData.getString("sfjc"), "string"));
        paramList.add(esbService.addParam("sfhdlhd",qsqdData.getString("sfhdlhd"), "string"));
        paramList.add(esbService.addParam("gdzt", "WGD", "string"));
        paramList.add(esbService.addParam("zt", "未下发", "string"));
        paramList.add(esbService.addParam("xgrdm", "110", "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        esbService.queryEsb("30000062", paramList);
    }


    private JSONArray qsmx_cx_all(JSONObject qsqdData){
        if (null == qsqdData){
            return null;
        }

        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("zbuuid", qsqdData.getString("uuid"),"string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        result = esbService.queryEsb("30000070", paramList);
        if (null == result.getData()){
            return null;
        }
        return (JSONArray)result.getData();
    }
    private void qsmx_cx_add(JSONObject qsmxData){
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", IdUtil.simpleUUID(), "string"));
        paramList.add(esbService.addParam("zbuuid", qsmxData.getString("zbuuid"), "string"));
        paramList.add(esbService.addParam("nsrmc", qsmxData.getString("nsrmc"), "string"));
        paramList.add(esbService.addParam("qszl", "", "string"));
        paramList.add(esbService.addParam("qsje", "", "string"));
        paramList.add(esbService.addParam("qsrq", qsmxData.getString("zspmDm"), "string"));
        paramList.add(esbService.addParam("yzfsrq", qsmxData.getString("yzfsrq"), "string"));
        paramList.add(esbService.addParam("zsxm", DmMaps.zsxm_dm.get(qsmxData.getString("zsxmDm")), "string"));
        paramList.add(esbService.addParam("zspm", DmMaps.zspm_dm.get(qsmxData.getString("zspmDm")), "string"));
        paramList.add(esbService.addParam("xmmc", DmMaps.zszm_dm.get(qsmxData.getString("zszmDm")), "string"));
        paramList.add(esbService.addParam("ybtse", qsmxData.getString("csye"), "string"));
        paramList.add(esbService.addParam("skssqq", qsmxData.getString("skssqq"), "string"));
        paramList.add(esbService.addParam("skssqz", qsmxData.getString("skssqz"), "string"));
        paramList.add(esbService.addParam("jkqx", qsmxData.getString("jkqx"), "string"));
        paramList.add(esbService.addParam("lrrdm", "110", "string"));
        paramList.add(esbService.addParam("xgrdm", "110", "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        esbService.queryEsb("30000074", paramList);
    }
    private void qsmx_cx_update(JSONObject qsmxData, JSONObject oldData){
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", oldData.getString("uuid"), "string"));
        paramList.add(esbService.addParam("ybtse", qsmxData.getString("csye"), "string"));
        paramList.add(esbService.addParam("jkqx", qsmxData.getString("jkqx"), "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        esbService.queryEsb("30000069", paramList);
    }
    private void qsmx_cx_delete(JSONObject delData){
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", delData.getString("uuid"), "string"));
        paramList.add(esbService.addParam("yxbz", "N", "string"));
        esbService.queryEsb("30000069", paramList);
    }

}
