package com.aisino.cq.qyqsgl.controller;

import cn.hutool.core.util.IdUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.RequestUtil;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

// "管理后台 - 企业欠税管理-欠税清单明细")
@RestController
@RequestMapping("/qyqsgl/qyqsglqsqdmxb")
@Validated
public class QyqsglqsqdmxbController {
    private static  final Logger log = LoggerFactory.getLogger(QyqsglqsqdmxbController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        log.info("欠税清单-欠税明细");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
//        this.add(param.get("zbuuid"));
        List<Map<String,Object>> paramList = new ArrayList<>();
//        欠税清单-欠税明细查询
        paramList = new ArrayList<>();
        esbService.addParam(paramList,"zbuuid", param.get("zbuuid"), "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");

        return esbService.queryEsb("30000070", pageNo, pageSize,"0","true",paramList);
    }


    /**
     * function 获取列表信息
     */
//    @RequestMapping("add")
//    @ResponseBody
//    public Object add(HttpServletRequest request) {
    public Object add(String zbuuid) {
        log.info("新增数据");
        List<Map<String, Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", IdUtil.simpleUUID(), "string"));
        paramList.add(esbService.addParam("zbuuid", zbuuid, "string"));
        paramList.add(esbService.addParam("nsrmc", "nsrmc", "string"));
        paramList.add(esbService.addParam("qszl", "增值税", "string"));
        paramList.add(esbService.addParam("qsje", "11111", "string"));
        paramList.add(esbService.addParam("qsrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd"), "string"));
        paramList.add(esbService.addParam("yzfsrq", "yzfsrq", "string"));
        paramList.add(esbService.addParam("zsxm", "zsxm", "string"));
        paramList.add(esbService.addParam("zspm", "zspm", "string"));
        paramList.add(esbService.addParam("xmmc", "xmmc", "string"));
        paramList.add(esbService.addParam("ybtse", "222", "string"));
        paramList.add(esbService.addParam("skssqq", "2025-03-01", "string"));
        paramList.add(esbService.addParam("skssqz", "2025-03-01", "string"));
        paramList.add(esbService.addParam("jkqx", "2025-04-01", "string"));
        paramList.add(esbService.addParam("lrrdm", "110", "string"));
        paramList.add(esbService.addParam("xgrdm", "110", "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        return esbService.queryEsb("30000074", paramList);
    }



}