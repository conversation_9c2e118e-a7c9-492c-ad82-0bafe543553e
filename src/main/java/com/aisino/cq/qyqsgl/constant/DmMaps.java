package com.aisino.cq.qyqsgl.constant;

import java.util.HashMap;
import java.util.Map;

public class DmMaps{

    public static final Map<String, String> swjgdm = new HashMap<String, String>(){{
        put("15009033300","国家税务总局重庆高新技术产业开发区税务局税源管理一科");
        put("15009033500","国家税务总局重庆高新技术产业开发区税务局税源管理二科");
        put("---","---");
        put("15009033100","国家税务总局重庆西永综合保税区税务局第二税务所");
        put("15009032000","国家税务总局重庆高新技术产业开发区税务局金凤税务所");
    }};

    public static final Map<String, String> sfhdlhd = new HashMap<String, String>(){{
        put("91500000565636735M","重庆恒阳房地产开发有限公司");
        put("91500000585737218E","重庆睿阳房地产开发有限公司");
        put("91500000585737250R","重庆台阳房地产开发有限公司");
        put("91500000585737242Y","重庆开阳房地产开发有限公司");
        put("91500107062856138G","重庆恺成房地产开发有限公司");
        put("915001077562413148","重庆腾翔实业有限公司");
        put("91500107MA61D4UYXX","重庆渝祥兆实业有限公司");
        put("91500000798015585Q","广州富力地产（重庆）有限公司");
    }};


    public static final Map<String, String> sksx_dm = new HashMap<String, String>(){{
        put("0101", "一般申报");
        put("0102", "零散税收");
        put("0103", "呆账税金");
        put("0104", "清算税（费）款");
        put("0105", "委托代征（办）税款");
        put("0106", "代收代缴税款");
        put("0107", "代扣代缴税款");
        put("0108", "代售印花税票");
        put("0109", "出口退税退运补税");
        put("0110", "特别纳税调整自行调整税款");
        put("0111", "随机抽查自查补税");
        put("0112", "增值税留抵退税缴回");
        put("0113", "多退手续费返纳（个人所得税）");
        put("0114", "增值税即征即退缴回");
        put("0201", "分期预缴税款");
        put("0202", "延期申报预收税款");
        put("0203", "出口货物预收税款");
        put("0204", "代开发票预收税款");
        put("0205", "查补预收税款");
        put("0206", "特别纳税调整自行缴纳（预缴）");
        put("0207", "预缴费款");
        put("0208", "辅导期一般纳税人预缴税款");
        put("0209", "项目部预缴");
        put("0210", "预约定价安排预缴");
        put("0211", "乐企反向开票预收税款");
        put("0299", "其他预收税款");
        put("0301", "纳税评定税款");
        put("0401", "稽查查补税（费）款");
        put("0402", "稽查查退税（费）款");
        put("0403", "行政复议应补税（费）");
        put("0404", "行政诉讼应补税（费）");
        put("0405", "特别纳税调整补税");
        put("0406", "其他出口退税追缴税款");
        put("0407", "政策性补缴");
        put("0408", "税务其他部门查补税（费）款");
        put("0409", "外部门查补税（费）款");
        put("0410", "税务其他部门查退税（费）款");
        put("0411", "外部门查退税（费）款");
        put("0412", "预约定价安排补退税");
        put("0413", "稽查查补-留抵退税缴回");
        put("0414", "查补预缴税款-留抵退税缴回");
        put("0415", "稽查自查补税-留抵退税缴回");
        put("0416", "自查辅导补税");
        put("0501", "行为罚款");
        put("0502", "没收违法所得");
        put("0503", "涉税罚款");
        put("0504", "没收非法财物的拍卖款");
        put("0601", "海关代征");
        put("0701", "征前减免");
        put("0801", "提退税款");
        put("9999", "其他");
        put("A001", "一次性缴费");
        put("A002", "补记年金");
        put("A003", "虚账做实");
        put("A004", "赎回征缴");
    }};

    public static final Map<String, String> zsxm_dm = new HashMap<String, String>(){{
        put("10109", "城市维护建设税");
        put("10113", "土地增值税");
        put("10114", "车船税");
        put("10117", "关税");
        put("10104", "企业所得税");
        put("10107", "资源税");
        put("10101", "增值税");
        put("10115", "船舶吨税");
        put("10111", "印花税");
        put("10100", "税收收入");
        put("10119", "契税");
        put("10120", "烟叶税");
        put("10125", "屠宰税（废止）");
        put("10199", "其他税收收入");
        put("10102", "消费税");
        put("10105", "企业所得税退税");
        put("10112", "城镇土地使用税");
        put("10116", "车辆购置税");
        put("10118", "耕地占用税");
        put("10106", "个人所得税");
        put("10103", "营业税");
        put("10121", "环境保护税");
        put("10110", "房产税");
    }};


    public static final Map<String, String> zspm_dm = new HashMap<String, String>(){{
        put("101060899", "其他财产租赁所得");
        put("101073219", "天然卤水");
        put("101017603", "土地使用权（11%、10%、9%、3%）");
        put("101120304", "建制镇土地使用税等级4");
        put("101140635", "辅助动力帆艇");
        put("101019002", "其他行业（11%、10%、9%）");
        put("101140634", "艇身长度超过30米的游艇");
        put("101190500", "房屋交换");
        put("101090302", "非市区、县城、镇（营业税附征）");
        put("101020111", "电子烟（批发环节，从价计征）");
        put("101076155", "石膏(2020)");
        put("101076221", "含钾岩石(2020)");
        put("101020200", "酒");
        put("101020903", "1.5升＜气缸容量≤2.0升的乘用车");
        put("101030601", "音乐茶座");
        put("101212236", "邻－二甲苯（水）");
        put("101120111", "城市土地使用税等级11");
        put("101194100", "住房作价入股");
        put("101073204", "石英砂");
        put("101073220", "沸石");
        put("101017100", "建筑服务");
        put("101073107", "磷矿");
        put("101120206", "县城土地使用税等级6");
        put("101130000", "土地增值税");
        put("101070622", "一等锑矿石");
        put("101075214", "金(2020)");
        put("101075238", "镉(2020)");
        put("101076219", "麦饭石(2020)");
        put("101070439", "石灰石");
        put("101259601", "屠宰税废止品目");
        put("101212239", "氯苯（水）");
        put("101213001", "煤矸石（固）");
        put("101213003", "危险废物（固）");
        put("101190234", "土地使用权交换（综合用地）");
        put("101073256", "二氧化碳");
        put("101017520", "体育服务");
        put("101180502", "渔业水域滩涂");
        put("101016205", "工程勘察勘探服务");
        put("101030603", "高尔夫球");
        put("101030700", "服务业");
        put("101074031", "煤成（层）气(2020)");
        put("101075236", "铪(2020)");
        put("101070521", "五等铁矿石（非重点矿山入选露天矿）");
        put("101010206", "饮料");
        put("101011299", "稀有稀土金属");
        put("101020606", "溶剂油");
        put("101060999", "其他转让所得");
        put("101030801", "转让土地使用权");
        put("101212228", "三氯甲烷（水）");
        put("101073234", "毒重石");
        put("101130602", "非普通住宅（清算）");
        put("101072204", "汞锑矿");
        put("101180700", "草地");
        put("101110109", "财产保险合同");
        put("101120104", "城市土地使用税等级4");
        put("101017001", "基础电信服务");
        put("101140220", "货车");
        put("101070612", "五等铅锌矿石");
        put("101076123", "钠硝石(2020)");
        put("101077201", "钠盐(2020)");
        put("101070506", "六等铁矿石（重点矿山入选露天矿）");
        put("101020499", "其他贵重首饰和珠宝玉石");
        put("101020608", "燃料油");
        put("101060902", "个人房屋转让所得");
        put("101030501", "文化业");
        put("101030606", "网吧");
        put("101070422", "凹凸棒石粘土");
        put("101073258", "黑矅岩");
        put("101212201", "悬浮物(SS)（水）");
        put("101212220", "总磷（水）");
        put("101212234", "甲苯（水）");
        put("101089903", "固定资产投资方向调节税停征品目");
        put("101073218", "耐火粘土");
        put("101072215", "铁铜矿");
        put("101076300", "宝玉石类(2020)");
        put("101077206", "海盐(2020)");
        put("101196112", "增量房买卖（非住房买卖）");
        put("101070434", "五等石棉");
        put("101010302", "食用植物油");
        put("101030300", "金融保险业");
        put("101211100", "大气污染物");
        put("101211131", "苯胺类（气）");
        put("101212221", "单质磷(以P计)（水）");
        put("101212249", "邻苯二甲酸二辛酯（水）");
        put("101213006", "炉渣（固）");
        put("101190103", "商业用地");
        put("101191119", "增量房（其他住房买卖）");
        put("101191123", "增量房非住房买卖（商住）");
        put("101073214", "珍珠岩");
        put("101017104", "装饰服务");
        put("101017550", "旅游服务");
        put("101120202", "县城土地使用税等级2");
        put("101016500", "物流辅助服务");
        put("101120299", "县城其他等级");
        put("101070627", "一等钼矿石");
        put("101070647", "五等砂金矿");
        put("101070701", "北方海盐");
        put("101070703", "井矿盐");
        put("101090303", "非市区、县城、镇（消费税附征）");
        put("101075233", "镓(2020)");
        put("101076111", "水晶(2020)");
        put("101076116", "长石(2020)");
        put("101076122", "芒硝(2020)");
        put("101070427", "石膏");
        put("101016803", "广播影视节目（作品）播映服务");
        put("101010104", "烟叶");
        put("101010602", "皮革、毛皮");
        put("101070402", "硅藻土");
        put("101073271", "砷矿");
        put("101211133", "硝基苯（气）");
        put("101191229", "存量房非住房买卖（其他）");
        put("101192210", "存量房赠与（住房赠与）");
        put("101070530", "三等铁矿石（非重点矿山入炉地下矿）");
        put("101070532", "锰矿石");
        put("101090201", "县城、镇（增值税附征）");
        put("101110116", "保管合同");
        put("101075210", "汞(2020)");
        put("101076151", "石棉(2020)");
        put("101076229", "正长岩(2020)");
        put("101250000", "屠宰税（废止）");
        put("101010502", "纸板");
        put("101070403", "高铝粘土");
        put("101212214", "硝基苯类（水）");
        put("101180101", "耕地（基本农田）");
        put("101191121", "增量房非住房买卖（商业）");
        put("101191210", "存量房买卖（住房买卖）");
        put("101191219", "存量房（其他住房买卖）");
        put("101071101", "煤炭");
        put("101072107", "锡矿");
        put("101110307", "社保基金证券交易B种股票印花税");
        put("101120303", "建制镇土地使用税等级3");
        put("101190301", "住房买卖");
        put("101070528", "三等铁矿石（非重点矿山入炉露天矿）");
        put("101090200", "县城、镇");
        put("101075239", "硒(2020)");
        put("101076201", "大理岩(2020)");
        put("101076202", "花岗岩(2020)");
        put("101076302", "玉石(2020)");
        put("101077204", "锂盐(2020)");
        put("101010300", "食品加工制造业");
        put("101011401", "家用电器");
        put("101020204", "薯类白酒（从量计算）");
        put("101192122", "增量房非住房赠与（办公）");
        put("101120214", "县城土地使用税等级14");
        put("101120315", "建制镇土地使用税等级15");
        put("101190222", "土地使用权赠与（商业用地）");
        put("101073229", "矽线石");
        put("101130601", "普通住宅（清算）");
        put("101017490", "其他现代服务");
        put("101017306", "经纪代理服务（6%、5%）");
        put("101072208", "锶矿");
        put("101072214", "铍矿");
        put("101119902", "罚款");
        put("101190300", "房屋买卖");
        put("101013902", "其他电力煤气及水的生产和供应业（13%）");
        put("101016202", "技术转让服务");
        put("101149601", "停征品目1");
        put("101090203", "县城、镇（消费税附征）");
        put("101077200", "盐(2020)");
        put("101190250", "集体土地使用权出售");
        put("101190270", "集体土地使用权互换");
        put("101196312", "增量房互换（非住房互换）");
        put("101196320", "存量房互换");
        put("101010402", "毛纺织品");
        put("101010405", "化纤织品");
        put("101070415", "重晶石");
        put("101020609", "汽油");
        put("101120411", "工矿区土地使用税等级11");
        put("101191200", "存量房买卖");
        put("101130500", "房地产纳税人预征");
        put("101017599", "其他生活服务");
        put("101192224", "存量房非住房赠与（附属建筑）");
        put("101110302", "B种股票");
        put("101140631", "艇身长度不超过10米的游艇");
        put("101013901", "其他电力煤气及水的生产和供应业（17%、16%、13%）");
        put("101099902", "罚款");
        put("101075218", "钌(2020)");
        put("101076218", "蛇纹岩(2020)");
        put("101076224", "橄榄岩(2020)");
        put("101070520", "四等铁矿石（非重点矿山入选露天矿）");
        put("101010499", "其他纺织业");
        put("101020907", "气缸容量＞4.0升的乘用车");
        put("101073273", "水晶");
        put("101214000", "噪声");
        put("101211134", "丙烯腈（气）");
        put("101212248", "邻苯二甲酸二丁酯（水）");
        put("101212405", "饮食娱乐服务业（水）");
        put("101020800", "摩托车");
        put("101020802", "摩托车（气缸容量＞250毫升）");
        put("101030402", "电信");
        put("101211119", "锡及其化合物（气）");
        put("101212999", "其他水污染物");
        put("101190244", "土地使用权作价投资、入股（综合用地）");
        put("101191220", "存量房买卖（非住房买卖）");
        put("101017540", "医疗服务");
        put("101072203", "锑矿");
        put("101140212", "核定载客人数20人（含）以上客车");
        put("101180100", "耕地");
        put("101079602", "停征品目2");
        put("101100100", "生产用房");
        put("101074012", "天然气(2020)");
        put("101075102", "锰(2020)");
        put("101190272", "集体土地使用权互换（非居住用地）");
        put("101070523", "三等铁矿石（非重点矿山入选地下矿）");
        put("101010204", "其他酒");
        put("101060500", "稿酬所得");
        put("101030605", "游艺厅");
        put("101049901", "滞纳金");
        put("101211136", "光气（气）");
        put("101212499", "其他畜禽（水）");
        put("101192121", "增量房非住房赠与（商业）");
        put("101021699", "铅蓄电池");
        put("101191111", "增量房（商品住房买卖）");
        put("101191129", "增量房非住房买卖（其他）");
        put("101017600", "销售无形资产");
        put("101070527", "二等铁矿石（非重点矿山入炉露天矿）");
        put("101070643", "一等砂金矿");
        put("101075209", "铋(2020)");
        put("101076153", "红柱石(2020)");
        put("101076225", "松脂岩(2020)");
        put("101077000", "水气矿产及盐(2020)");
        put("101070501", "一等铁矿石（重点矿山入选露天矿）");
        put("101011899", "其他制造业（11%、10%、9%）");
        put("101070408", "沸石");
        put("101030804", "转让非专利权");
        put("101060000", "个人所得税");
        put("101195000", "房屋其他权属转移方式");
        put("101017205", "金融商品转让");
        put("101017604", "土地使用权（11%、10%、9%、5%）");
        put("101073111", "砂石");
        put("101189800", "其他");
        put("101070513", "二等铁矿石（重点矿山入炉露天矿）");
        put("101010208", "茶（11%、10%、9%）");
        put("101060800", "财产租赁所得");
        put("101030600", "娱乐业");
        put("101030999", "其他销售不动产");
        put("101073276", "皂石");
        put("101211109", "铬酸雾（气）");
        put("101211130", "沥青烟（气）");
        put("101212110", "总银（水）");
        put("101212225", "马拉硫磷（水）");
        put("101020803", "摩托车（气缸容量（排气量）=250毫升）");
        put("101194203", "非住房作价入股（商住）");
        put("101073206", "白云岩");
        put("101073250", "明矾石");
        put("101017103", "修缮服务");
        put("101017500", "生活服务");
        put("101071102", "原油");
        put("101072100", "中央列名税目");
        put("101072207", "钒矿");
        put("101192125", "增量房非住房赠与（工业）");
        put("101060903", "财产拍卖所得及回流文物拍卖所得");
        put("101140104", "2.0升以上至2.5升(含)的乘用车");
        put("101140400", "摩托车");
        put("101190200", "土地使用权转让（出售、赠与和互换）");
        put("101070533", "铬矿石");
        put("101070623", "二等锑矿石");
        put("101070631", "五等钼矿石");
        put("101070641", "六等岩金矿石");
        put("101090102", "市区（营业税附征）");
        put("101075212", "钨(2020)");
        put("101075232", "锗(2020)");
        put("101076156", "其他粘土（铸型用粘土、砖瓦用粘土、陶粒用粘土、水泥配料用粘土、水泥配料用红土、水泥配料用黄土、水泥配料用泥岩、保温材料用粘土）(2020)");
        put("101076228", "辉石岩(2020)");
        put("101196200", "房屋赠与");
        put("101190219", "土地使用权出售（其他用地）");
        put("101190240", "土地使用权作价投资、入股");
        put("101190242", "土地使用权作价投资、入股（商业用地）");
        put("101194000", "房屋作价入股");
        put("101073215", "玉石");
        put("101130502", "非普通住宅（预征）");
        put("101017202", "直接收费金融服务");
        put("101017602", "商标和著作权");
        put("101071000", "能源矿");
        put("101072210", "铂矿");
        put("101120208", "县城土地使用税等级8");
        put("101190503", "其他交换");
        put("101016601", "有形动产融资租赁");
        put("101075211", "铝土矿(2020)");
        put("101075227", "锶(2020)");
        put("101076112", "工业用金刚石(2020)");
        put("101076129", "膨润土(2020)");
        put("101076137", "累托石粘土(2020)");
        put("101076220", "泥灰岩(2020)");
        put("101076233", "砂石(2020)");
        put("101070515", "四等铁矿石（重点矿山入炉露天矿）");
        put("101060400", "劳务报酬所得");
        put("101079994", "煤炭资源税罚款");
        put("101211104", "氯气（气）");
        put("101070512", "一等铁矿石（重点矿山入炉露天矿）");
        put("101013100", "发电");
        put("101029900", "滞纳金、罚款");
        put("101030303", "证券");
        put("101073267", "木纹石");
        put("101180102", "耕地（非基本农田）");
        put("101194204", "非住房作价入股（附属建筑）");
        put("101017302", "经纪代理服务（6%、3%）");
        put("101017510", "文化服务");
        put("101072106", "镍矿");
        put("101073108", "氯化钾");
        put("101110599", "其他营业账簿");
        put("101010398", "其他食品加工制造业（17%、16%、13%）");
        put("101075221", "铑(2020)");
        put("101076210", "玄武岩(2020)");
        put("101077104", "氡气(2020)");
        put("101190120", "集体土地使用权出让");
        put("101020102", "乙类卷烟[调拨价70元(不含增值税)/条以下，从价计算]");
        put("101070419", "滑石");
        put("101073281", "巴林石");
        put("101212216", "总铜（水）");
        put("101190212", "土地使用权出售（商业用地）");
        put("101191120", "增量房（非住房买卖）");
        put("101120108", "城市土地使用税等级8");
        put("101120109", "城市土地使用税等级9");
        put("101120207", "县城土地使用税等级7");
        put("101070529", "四等铁矿石（非重点矿山入炉露天矿）");
        put("101061300", "手续费退库");
        put("101110113", "建设工程合同");
        put("101010205", "酒精");
        put("101013200", "供电");
        put("101019000", "其他行业");
        put("101020107", "雪茄烟");
        put("101020607", "润滑油");
        put("101073263", "金刚石");
        put("101070820", "按售水量计税");
        put("101211105", "氯化氢（气）");
        put("101211118", "镍及其化合物（气）");
        put("101212212", "甲醛（水）");
        put("101212213", "苯胺类（水）");
        put("101060904", "股票转让所得");
        put("101073210", "硅灰石");
        put("101073216", "页岩");
        put("101130900", "房地产开发纳税人核定");
        put("101072101", "铁矿");
        put("101072102", "金矿");
        put("101090103", "市区（消费税附征）");
        put("101074042", "钍(2020)");
        put("101070500", "黑色金属矿原矿");
        put("101070507", "二等铁矿石（重点矿山入选地下矿 ）");
        put("101212406", "医院(消毒-床)（水）");
        put("101194201", "非住房作价入股（商业）");
        put("101130600", "房地产纳税人清算");
        put("101110301", "A种股票");
        put("101120106", "城市土地使用税等级6");
        put("101016405", "会议展览服务");
        put("101120499", "工矿区其他等级");
        put("101070602", "中重稀土矿");
        put("101070637", "二等岩金矿石");
        put("101079900", "滞纳金、罚款");
        put("101075235", "铊(2020)");
        put("101076136", "伊利石粘土(2020)");
        put("101196100", "房屋买卖");
        put("101070811", "地表水水资源税");
        put("101219902", "罚款");
        put("101021901", "超豪华乘用车");
        put("101211135", "氯乙烯（气）");
        put("101069903", "利息");
        put("101100600", "个人住房");
        put("101190224", "土地使用权赠与（综合用地）");
        put("101073211", "玄武岩");
        put("101017721", "构筑物（11%、10%、9%、3%）-增量房");
        put("101073115", "提取地下卤水晒制的盐");
        put("101120302", "建制镇土地使用税等级2");
        put("101016509", "装卸搬运服务");
        put("101070632", "二等镍矿石");
        put("101099901", "滞纳金");
        put("101075216", "铂(2020)");
        put("101076214", "浮石(2020)");
        put("101070425", "菱镁矿");
        put("101070433", "四等石棉");
        put("101070504", "四等铁矿石（重点矿山入选露天矿）");
        put("101011003", "平板玻璃");
        put("101011200", "有色金属冶炼及压延加工业");
        put("101011301", "汽车");
        put("101012500", "天然气");
        put("101020101", "甲类卷烟[调拨价70元(不含增值税)/条以上(含70元)，从价计算]");
        put("101060700", "利息，股息，红利所得");
        put("101020905", "2.5升＜气缸容量≤3.0升的乘用车");
        put("101070418", "长石");
        put("101212219", "彩色显影剂(CD-2)（水）");
        put("101212302", "色度（水）");
        put("101190102", "居住用地");
        put("101191122", "增量房非住房买卖（办公）");
        put("101073244", "浮石");
        put("101073114", "海盐");
        put("101120400", "工矿区");
        put("101169900", "滞纳金、罚款");
        put("101014006", "商业(3%)");
        put("101030502", "体育业");
        put("101061200", "综合所得");
        put("101076143", "沸石(2020)");
        put("101076205", "砂岩(2020)");
        put("101076230", "火山灰(2020)");
        put("101077102", "硫化氢气(2020)");
        put("101012400", "原油");
        put("101060799", "其他利息、股息、红利所得");
        put("101214101", "超标1-3分贝（噪）");
        put("101191221", "存量房非住房买卖（商业）");
        put("101194202", "非住房作价入股（办公）");
        put("101016403", "知识产权服务");
        put("101016502", "航空服务");
        put("101016504", "货运客运场站服务");
        put("101075228", "铷(2020)");
        put("101076211", "片麻岩(2020)");
        put("101070423", "高岭土（瓷土）");
        put("101020300", "化妆品");
        put("101030706", "租赁业");
        put("101212205", "石油类（水）");
        put("101212400", "禽畜养殖业、小型企业和第三产业");
        put("101213004", "冶炼渣（固）");
        put("101192111", "增量房（商品住房赠与）");
        put("101120314", "建制镇土地使用税等级14");
        put("101191110", "增量房买卖（住房买卖）");
        put("101070611", "四等铅锌矿石");
        put("101074021", "煤(2020)");
        put("101070429", "工业用金刚石");
        put("101011402", "电机");
        put("101214100", "工业噪声");
        put("101212238", "间－二甲苯（水）");
        put("101130901", "普通住宅（核定）");
        put("101017102", "安装服务");
        put("101017303", "人力资源服务（6%、3%）");
        put("101017713", "建筑物（11%、10%、9%、5%）-增量房");
        put("101120406", "工矿区土地使用税等级6");
        put("101200100", "晾晒烟叶");
        put("101200200", "烤烟叶");
        put("101109900", "滞纳金、罚款");
        put("101076304", "玛瑙(2020)");
        put("101190271", "集体土地使用权互换（居住用地）");
        put("101011399", "其他交通运输工具（11%、10%、9%）");
        put("101049902", "罚款");
        put("101073261", "辉长岩");
        put("101073270", "佘太玉");
        put("101073278", "贺兰石");
        put("101212207", "挥发酚（水）");
        put("101212402", "禽畜养殖场(猪)（水）");
        put("101070821", "自来水水资源税");
        put("101192120", "增量房赠与（非住房赠与）");
        put("101120211", "县城土地使用税等级11");
        put("101073230", "凝灰岩");
        put("101130801", "普通住宅（尾盘）");
        put("101180501", "养殖水面");
        put("101110101", "购销合同");
        put("101090202", "县城、镇（营业税附征）");
        put("101100300", "办公用房");
        put("101020610", "废矿物油");
        put("101074011", "原油(2020)");
        put("101075203", "锌(2020)");
        put("101075219", "锇(2020)");
        put("101075230", "铌(2020)");
        put("101075231", "钽(2020)");
        put("101070431", "二等石棉");
        put("101020400", "首饰和珠宝玉石");
        put("101070400", "其他非金属矿原矿");
        put("101070650", "钼矿");
        put("101129901", "滞纳金");
        put("101150000", "船舶吨税");
        put("101013500", "自来水");
        put("101120399", "建制镇其他等级");
        put("101030708", "广告业");
        put("101075202", "铅(2020)");
        put("101075208", "钴(2020)");
        put("101075223", "中重稀土(2020)");
        put("101077105", "矿泉水(2020)");
        put("101070436", "硫铁矿");
        put("101070502", "二等铁矿石（重点矿山入选露天矿）");
        put("101070516", "二等铁矿石（重点矿山入炉地下矿）");
        put("101011303", "飞机");
        put("101020500", "鞭炮、焰火");
        put("101069901", "滞纳金");
        put("101020904", "2.0升＜气缸容量≤2.5升的乘用车");
        put("101030800", "转让无形资产");
        put("101030900", "销售不动产");
        put("101079992", "水资源税罚款");
        put("101211129", "酚类（气）");
        put("101212246", "2,4－二氯酚（水）");
        put("101073208", "膨润土");
        put("101073228", "凹凸棒石粘土");
        put("101016305", "信息系统增值服务");
        put("101016604", "不动产融资租赁（11%、10%、9%、5%）");
        put("101071100", "中央列名税目");
        put("101073101", "石墨");
        put("101140210", "客车");
        put("101209901", "滞纳金");
        put("101070610", "三等铅锌矿石");
        put("101070630", "四等钼矿石");
        put("101020210", "白酒（从量计算）");
        put("101010201", "白酒");
        put("101010599", "其他造纸及纸制品业");
        put("101010601", "服装");
        put("101019900", "滞纳金、罚款");
        put("101060901", "限售股转让所得");
        put("101030200", "建筑业");
        put("101030299", "其他工程作业");
        put("101030703", "饮食业");
        put("101211138", "氨（气）");
        put("101212108", "苯并(a)芘（水）");
        put("101212200", "第二类水污染物");
        put("101212211", "氟化物（水）");
        put("101190233", "土地使用权交换（工业用地）");
        put("101072299", "其他未列名金属矿");
        put("101140500", "其他车辆");
        put("101070600", "有色金属矿原矿");
        put("101070629", "三等钼矿石");
        put("101070638", "三等岩金矿石");
        put("101110117", "仓储合同");
        put("101011099", "其他非金属矿物制品业");
        put("101039900", "滞纳金、罚款");
        put("101021902", "超豪华中轻型商用客车");
        put("101212304", "余氯量(用氯消毒的医院废水)（水）");
        put("101120313", "建制镇土地使用税等级13");
        put("101190229", "土地使用权赠与（其他用地）");
        put("101100900", "个人出租住房");
        put("101017400", "其他现代服务");
        put("101017710", "建筑物");
        put("101072105", "铅锌矿");
        put("101192124", "增量房非住房赠与（附属建筑）");
        put("101110305", "无偿划拨国有股权的股数");
        put("101190303", "其他买卖");
        put("101140103", "1.6升以上至2.0升(含)的乘用车");
        put("101159901", "滞纳金");
        put("101016304", "业务流程管理服务");
        put("101070606", "四等铜矿石");
        put("101020209", "白酒（从价计算）");
        put("101070438", "磷铁矿");
        put("101011302", "摩托车");
        put("101011305", "自行车");
        put("101012900", "其他采矿业");
        put("101030101", "陆路运输");
        put("101030102", "水路运输");
        put("101211110", "汞及其化合物（气）");
        put("101213002", "尾矿（固）");
        put("101073248", "泥炭");
        put("101072103", "铜矿");
        put("101192129", "增量房非住房赠与（其他）");
        put("101110107", "仓储保管合同");
        put("101199902", "罚款");
        put("101079603", "停征品目3");
        put("101079901", "滞纳金");
        put("101075207", "镁(2020)");
        put("101075234", "铟(2020)");
        put("101076108", "天然石英砂(2020)");
        put("101029901", "滞纳金");
        put("101070413", "石墨");
        put("101073262", "火山灰");
        put("101212408", "医院(不消毒-床)（水）");
        put("101100601", "个人住房房产税");
        put("101071203", "油页岩");
        put("101072205", "铬矿");
        put("101073202", "大理岩");
        put("101180600", "园地");
        put("101140623", "净吨位超过2000吨但不超过10000吨的拖船、非机动驳船");
        put("101140624", "净吨位超过10000吨的拖船、非机动驳船");
        put("101149901", "滞纳金");
        put("101190400", "房屋赠与");
        put("101070621", "五等锡矿石");
        put("101079902", "罚款");
        put("101090101", "市区（增值税附征）");
        put("101074051", "油页岩(2020)");
        put("101076120", "颜料矿物(2020)");
        put("101076216", "黑曜岩(2020)");
        put("101076222", "含钾砂页岩(2020)");
        put("101070508", "三等铁矿石（重点矿山入选地下矿 ）");
        put("101070510", "五等铁矿石（重点矿山入选地下矿 ）");
        put("101259600", "屠宰税（废止）");
        put("101010403", "丝织品");
        put("101011103", "钢材");
        put("101011500", "电子通信设备制造业");
        put("101050000", "企业所得税退税");
        put("101070812", "地下水水资源税");
        put("101212240", "邻二氯苯（水）");
        put("101120112", "城市土地使用税等级12");
        put("101073249", "闪长岩");
        put("101194290", "非住房作价入股（其他）");
        put("101016606", "不动产经营租赁（11%、10%、9%、5%）");
        put("101071200", "地方列名税目");
        put("101110108", "借款合同");
        put("101140107", "4.0升以上的乘用车");
        put("101140610", "机动船舶");
        put("101070625", "四等锑矿石");
        put("101090100", "市区");
        put("101076113", "冰洲石(2020)");
        put("101076149", "工业用电气石(2020)");
        put("101196110", "增量房买卖");
        put("101070432", "三等石棉");
        put("101070499", "其他未列名非金属矿原矿");
        put("101011300", "机械制造业");
        put("101020206", "甲类啤酒[出厂价格3000元(不含增值税)/吨以上(含3000元)]");
        put("101212102", "总镉（水）");
        put("101212224", "甲基对硫磷（水）");
        put("101212404", "小型企业（水）");
        put("101120213", "县城土地使用税等级13");
        put("101073241", "自然硫");
        put("101130501", "普通住宅（预征）");
        put("101130503", "其他类型房地产（预征）");
        put("101017201", "贷款服务");
        put("101073112", "井矿盐");
        put("101110300", "股权转移书据（沪深交易）");
        put("101120301", "建制镇土地使用税等级1");
        put("101120404", "工矿区土地使用税等级4");
        put("101140620", "拖船、非机动驳船");
        put("101149902", "罚款");
        put("101209900", "滞纳金、罚款");
        put("101016702", "鉴证服务");
        put("101070619", "三等锡矿石");
        put("101020611", "纯生物柴油");
        put("101190111", "国有土地使用权出让（居住用地）");
        put("101196212", "增量房赠与（非住房赠与）");
        put("101010500", "造纸及纸制品业");
        put("101011000", "非金属矿物制品业");
        put("101012700", "有色金属矿产品");
        put("101030807", "转让动漫版权");
        put("101212233", "苯（水）");
        put("101191000", "房屋买卖");
        put("101193000", "房屋交换");
        put("101016106", "陆路旅客运输服务");
        put("101072104", "铝土矿");
        put("101072202", "银矿");
        put("101119800", "其他凭证");
        put("101140000", "车船税");
        put("101070607", "五等铜矿石");
        put("101070628", "二等钼矿石");
        put("101180500", "养殖水面以及渔业水域滩涂");
        put("101076117", "滑石(2020)");
        put("101076144", "重晶石(2020)");
        put("101076152", "蓝石棉(2020)");
        put("101010501", "机制纸");
        put("101011502", "电子计算机");
        put("101020106", "卷烟（商业批发，从价计算）");
        put("101030302", "保险");
        put("101070412", "耐火粘土");
        put("101214102", "超标4-6分贝（噪）");
        put("101212107", "总镍（水）");
        put("101190230", "国有土地使用权互换");
        put("101191223", "存量房非住房买卖（商住）");
        put("101191224", "存量房非住房买卖（附属建筑）");
        put("101073245", "片麻岩");
        put("101017203", "人身保险服务");
        put("101071204", "石煤");
        put("101073106", "硫铁矿");
        put("101120102", "城市土地使用税等级2");
        put("101120305", "建制镇土地使用税等级5");
        put("101110000", "印花税");
        put("101110111", "买卖合同");
        put("101076110", "粉石英(2020)");
        put("101196222", "存量房赠与（非住房赠与）");
        put("101212204", "总有机碳（TOC）（水）");
        put("101120114", "城市土地使用税等级14");
        put("101190232", "土地使用权交换（商业用地）");
        put("101130700", "旧房转让（非核定方式）");
        put("101017712", "建筑物（11%、10%、9%、3%）-二手房");
        put("101119900", "滞纳金、罚款");
        put("101120308", "建制镇土地使用税等级8");
        put("101016505", "打捞救助服务");
        put("101016701", "认证服务");
        put("101070624", "三等锑矿石");
        put("101074014", "天然气水合物(2020)");
        put("101075217", "钯(2020)");
        put("101076127", "碘(2020)");
        put("101070503", "三等铁矿石（重点矿山入选露天矿）");
        put("101011002", "水泥制品");
        put("101011201", "常用金属");
        put("101073268", "钠硝石");
        put("101073285", "天然泉水");
        put("101211132", "氯苯类（气）");
        put("101073247", "麦饭石");
        put("101131100", "旧房转让（核定方式）");
        put("101017300", "商务辅助服务");
        put("101017700", "销售不动产");
        put("101120110", "城市土地使用税等级10");
        put("101120201", "县城土地使用税等级1");
        put("101140105", "2.5升以上至3.0升(含)的乘用车");
        put("101140502", "轮式专用机械车");
        put("101016301", "软件服务");
        put("101070644", "二等砂金矿");
        put("101075101", "铁(2020)");
        put("101077203", "镁盐(2020)");
        put("101190262", "集体土地使用权赠与（非居住用地）");
        put("101196221", "存量房赠与（住房赠与）");
        put("101196311", "增量房互换（住房互换）");
        put("101070509", "四等铁矿石（重点矿山入选地下矿 ）");
        put("101016801", "广播影视节目（作品）制作服务");
        put("101010701", "成品油");
        put("101011199", "其他黑色金属冶炼及压延加工业");
        put("101012200", "洗煤");
        put("101020605", "石脑油");
        put("101020906", "3.0升＜气缸容量≤4.0升的乘用车");
        put("101021300", "游艇");
        put("101030707", "房屋租赁");
        put("101211112", "石棉尘（气）");
        put("101211114", "碳黑尘（气）");
        put("101120415", "工矿区土地使用税等级15");
        put("101192000", "房屋赠与");
        put("101072108", "中重稀土矿");
        put("101072212", "钽铌矿");
        put("101180800", "苇田");
        put("101120203", "县城土地使用税等级3");
        put("101013600", "暖气、热水、冷气等的生产和供应");
        put("101019001", "其他行业（17%、16%、13%）");
        put("101070617", "一等锡矿石");
        put("101110502", "营业账簿");
        put("101076103", "磷(2020)");
        put("101077101", "二氧化碳气(2020)");
        put("101077205", "天然卤水(2020)");
        put("101190252", "集体土地使用权出售（非居住用地）");
        put("101011499", "其他电气机械及器材制造业");
        put("101011700", "医药制造业");
        put("101012800", "非金属矿产品");
        put("101020901", "气缸容量≤1.0升的乘用车");
        put("101021100", "高尔夫球及球具");
        put("101039902", "罚款");
        put("101073282", "火山石");
        put("101192100", "增量房赠与");
        put("101190243", "土地使用权作价投资、入股（工业用地）");
        put("101073233", "板岩");
        put("101017101", "工程服务");
        put("101071201", "地热");
        put("101072109", "轻稀土矿");
        put("101120405", "工矿区土地使用税等级5");
        put("101076106", "硫铁矿(2020)");
        put("101076124", "明矾石(2020)");
        put("101196300", "房屋互换");
        put("101070514", "三等铁矿石（重点矿山入炉露天矿）");
        put("101010804", "化妆品");
        put("101011306", "通用设备");
        put("101030806", "转让商誉");
        put("101212227", "五氯酚及五氯酚钠(以五氯酚计)（水）");
        put("101120312", "建制镇土地使用税等级12");
        put("101190249", "土地使用权作价投资、入股（其他用地）");
        put("101017580", "住宿服务");
        put("101110105", "财产租赁合同");
        put("101140611", "净吨位不超过200吨的机动船舶");
        put("101030105", "装卸搬运");
        put("101076142", "云母(2020)");
        put("101076208", "闪长岩(2020)");
        put("101076227", "辉长岩(2020)");
        put("101011600", "家具制造业");
        put("101020202", "粮食白酒（从量计算）");
        put("101070200", "天然气");
        put("101073279", "绿松石");
        put("101211106", "氟化物（气）");
        put("101211142", "二甲二硫（气）");
        put("101212218", "总锰（水）");
        put("101212223", "乐果（水）");
        put("101192123", "增量房非住房赠与（商住）");
        put("101016206", "专业技术服务");
        put("101073201", "矿泉水");
        put("101110103", "建设工程勘察设计合同");
        put("101011898", "其他制造业（17%、16%、13%）");
        put("101109902", "罚款");
        put("101076105", "萤石(2020)");
        put("101076119", "菱镁矿(2020)");
        put("101196210", "增量房赠与");
        put("101010102", "雪茄烟");
        put("101070100", "原油");
        put("101212203", "化学需氧量(CODcr)（水）");
        put("101120311", "建制镇土地使用税等级11");
        put("101021601", "电池（不含铅蓄电池）");
        put("101191222", "存量房非住房买卖（办公）");
        put("101073223", "菱镁矿");
        put("101017200", "金融服务");
        put("101072000", "金属矿");
        put("101129900", "滞纳金、罚款");
        put("101190101", "国有土地使用权出让契税");
        put("101070615", "四等钨矿石");
        put("101075229", "铯(2020)");
        put("101190261", "集体土地使用权赠与（居住用地）");
        put("101010801", "化学化工产品");
        put("101011307", "专用设备（17%、16%、13%）");
        put("101021500", "实木地板");
        put("101030602", "台球");
        put("101212209", "硫化物（水）");
        put("101212241", "对二氯苯（水）");
        put("101073227", "安山岩");
        put("101073254", "蓝晶石");
        put("101140612", "净吨位超过200吨但不超过2000吨的机动船舶");
        put("101140632", "艇身长度超过10米但不超过18米的游艇");
        put("101190000", "契税");
        put("101070603", "一等铜矿石");
        put("101110115", "运输合同");
        put("101076147", "蛭石(2020)");
        put("101196322", "存量房互换（非住房互换）");
        put("101011004", "陶瓷制品");
        put("101012300", "盐");
        put("101014000", "商业");
        put("101073264", "绿泥石");
        put("101211113", "玻璃棉尘（气）");
        put("101211116", "镉及其化合物（气）");
        put("101212235", "乙苯（水）");
        put("101120212", "县城土地使用税等级12");
        put("101190239", "土地使用权交换（其他用地）");
        put("101073217", "方解石");
        put("101017722", "构筑物（11%、10%、9%、3%）-二手房");
        put("101192220", "存量房赠与（非住房赠与）");
        put("101119901", "滞纳金");
        put("101120408", "工矿区土地使用税等级8");
        put("101140102", "1.0升以上至1.6升(含)的乘用车");
        put("101140200", "商用车");
        put("101149600", "车船税停征品目");
        put("101070636", "一等岩金矿石");
        put("101100200", "营业用房");
        put("101076109", "脉石英(2020)");
        put("101076203", "白云岩(2020)");
        put("101076217", "霞石正长岩(2020)");
        put("101020000", "消费税");
        put("101040001", "应纳税所得额");
        put("101214104", "超标10-12分贝（噪）");
        put("101211125", "甲醛（气）");
        put("101212206", "动植物油（水）");
        put("101120310", "建制镇土地使用税等级10");
        put("101140100", "乘用车");
        put("101159900", "滞纳金、罚款");
        put("101999902", "罚款");
        put("101099900", "滞纳金、罚款");
        put("101075213", "钼(2020)");
        put("101076126", "硼(2020)");
        put("101076139", "硅灰石(2020)");
        put("101070525", "五等铁矿石（非重点矿山入选地下矿）");
        put("101011400", "电气机械及器材制造业");
        put("101211139", "三甲胺（气）");
        put("101017601", "专利或非专利技术");
        put("101017801", "铁路建设基金（中央）");
        put("101071202", "铀矿");
        put("101049903", "利息收入");
        put("101199900", "滞纳金、罚款");
        put("101190201", "土地使用权转让(出售、赠与和交换)契税");
        put("101070634", "四等镍矿石");
        put("101030203", "修缮");
        put("101030301", "金融");
        put("101110118", "融资租赁合同");
        put("101075100", "黑色金属(2020)");
        put("101075224", "铍(2020)");
        put("101076134", "凹凸棒石粘土(2020)");
        put("101010805", "护肤护发品");
        put("101011501", "通信设备");
        put("101070411", "宝石级金刚石");
        put("101030701", "代理业");
        put("101073272", "石榴子石");
        put("101212900", "其他水污染物");
        put("101212000", "水污染物");
        put("101190220", "国有土地使用权赠与");
        put("101191125", "增量房非住房买卖（工业）");
        put("101073205", "石英岩");
        put("101073235", "蛭石");
        put("101071299", "其他未列名能源矿");
        put("101192223", "存量房非住房赠与（商住）");
        put("101120209", "县城土地使用税等级9");
        put("101140633", "艇身长度超过18米但不超过30米的游艇");
        put("101189902", "罚款");
        put("101011308", "专用设备（11%、10%、9%）");
        put("101070613", "三等铝土矿");
        put("101070614", "三等钨矿石");
        put("101070705", "液体盐");
        put("101075240", "碲(2020)");
        put("101070417", "蛭石");
        put("101211107", "氰化氢（气）");
        put("101212242", "对硝基氯苯（水）");
        put("101212301", "pH值（水）");
        put("101070303", "原煤");
        put("101100800", "从租计征");
        put("101017301", "企业管理服务");
        put("101017690", "其他权益性无形资产");
        put("101120309", "建制镇土地使用税等级9");
        put("101120402", "工矿区土地使用税等级2");
        put("101129902", "罚款");
        put("101014004", "商业(4%)");
        put("101011701", "中药原药制造业");
        put("101016200", "研发和技术服务");
        put("101016201", "研发服务");
        put("101070635", "五等镍矿石");
        put("101070639", "四等岩金矿石");
        put("101089901", "滞纳金");
        put("101100500", "房屋出租");
        put("101076100", "矿物类(2020)");
        put("101011503", "家用视听设备");
        put("101020600", "成品油");
        put("101020902", "1.0升＜气缸容量≤1.5升的乘用车");
        put("101211123", "二甲苯（气）");
        put("101212106", "总铅（水）");
        put("101212202", "生化需氧量(BOD₅)（水）");
        put("101020109", "卷烟（商业批发，从量计算）");
        put("101130903", "其他类型房地产（核定）");
        put("101017680", "其他自然资源使用权");
        put("101073200", "地方列名税目");
        put("101190401", "住房赠与");
        put("101180000", "耕地占用税");
        put("101180300", "牧草地");
        put("101190100", "土地使用权出让");
        put("101014003", "商业(6%)");
        put("101030400", "邮电通信业");
        put("101090301", "非市区、县城、镇（增值税附征）");
        put("101074061", "地热(2020)");
        put("101075205", "镍(2020)");
        put("101076132", "耐火粘土(2020)");
        put("101076140", "透辉石(2020)");
        put("101196121", "存量房买卖（住房买卖）");
        put("101010200", "酒、饮料和精制茶制造业");
        put("101020100", "烟");
        put("101021000", "中轻型商用客车");
        put("101029902", "罚款");
        put("101212230", "四氯化碳（水）");
        put("101212303", "大肠菌群数(超标)（水）");
        put("101213007", "海洋工程生活垃圾（固）");
        put("101073242", "硼矿");
        put("101073251", "石英砂岩");
        put("101017204", "财产保险服务");
        put("101192221", "存量房非住房赠与（商业）");
        put("101120306", "建制镇土地使用税等级6");
        put("101120407", "工矿区土地使用税等级7");
        put("101011102", "钢坯");
        put("101020203", "薯类白酒（从价计算）");
        put("101069900", "滞纳金、罚款、利息");
        put("101020900", "乘用车");
        put("101030604", "保龄球");
        put("101039901", "滞纳金");
        put("101070416", "毒重石");
        put("101212100", "第一类水污染物");
        put("101212103", "总铬（水）");
        put("101212226", "对硫磷（水）");
        put("101192229", "存量房非住房赠与（其他）");
        put("101110306", "社保基金证券交易A种股票印花税");
        put("101016400", "文化创意服务");
        put("101079600", "资源税停征品目");
        put("101076226", "粗面岩(2020)");
        put("101070435", "六等石棉");
        put("101010401", "棉纺织品");
        put("101010900", "橡胶制品业");
        put("101014001", "商业(17%、16%、13%)");
        put("101030401", "邮政");
        put("101211140", "甲硫醇（气）");
        put("101190221", "国有土地使用权赠与（居住用地）");
        put("101110200", "产权转移书据");
        put("101016900", "邮政业");
        put("101016903", "其他邮政服务");
        put("101190302", "其他房屋买卖");
        put("101016302", "电路设计及测试服务");
        put("101016602", "有形动产经营租赁");
        put("101070704", "湖盐");
        put("101075222", "轻稀土(2020)");
        put("101076128", "溴(2020)");
        put("101076223", "天然油石(2020)");
        put("101196122", "存量房买卖（非住房买卖）");
        put("101010802", "化肥");
        put("101012302", "食用盐");
        put("101020205", "黄酒");
        put("101060701", "储蓄存款利息所得");
        put("101060900", "财产转让所得");
        put("101030901", "销售建筑物或构筑物");
        put("101073274", "天然沥青矿");
        put("101073299", "其他未列名非金属矿");
        put("101211102", "氮氧化物（气）");
        put("101211115", "铅及其化合物（气）");
        put("101212101", "总汞（水）");
        put("101070304", "洗选煤");
        put("101191225", "存量房非住房买卖（工业）");
        put("101073212", "滑石");
        put("101017714", "建筑物（11%、10%、9%、5%）-二手房");
        put("101073110", "粘土");
        put("101140211", "核定载客人数20人以下客车");
        put("101011398", "其他交通运输工具（17%、16%、13%）");
        put("101070601", "轻稀土矿");
        put("101100501", "个人出租住房");
        put("101075206", "锑(2020)");
        put("101076114", "蓝晶石(2020)");
        put("101076148", "透闪石(2020)");
        put("101010399", "其他食品加工制造业（11%、10%、9%）");
        put("101073260", "灰岩");
        put("101073265", "玛瑙");
        put("101214103", "超标7-9分贝（噪）");
        put("101073237", "脉石英");
        put("101119903", "利息收入");
        put("101120105", "城市土地使用税等级5");
        put("101139901", "滞纳金");
        put("101016203", "技术咨询服务");
        put("101016600", "租赁服务");
        put("101200000", "烟叶税");
        put("101020110", "电子烟（生产环节，从价计征）");
        put("101075220", "铱(2020)");
        put("101075226", "锆(2020)");
        put("101190110", "国有土地使用权出让");
        put("101070511", "六等铁矿石（重点矿山入选地下矿 ）");
        put("101061000", "偶然所得");
        put("101021400", "木制一次性筷子");
        put("101030699", "其他娱乐业");
        put("101212245", "间－甲酚（水）");
        put("101212247", "2,4,6-三氯酚（水）");
        put("101191212", "存量房（保障性住房买卖）");
        put("101071103", "天然气");
        put("101209902", "罚款");
        put("101016100", "交通运输业");
        put("101016503", "港口码头服务");
        put("101190215", "国有土地使用权出售（非居住用地）");
        put("101196220", "存量房赠与");
        put("101030204", "装饰");
        put("101212237", "对－二甲苯（水）");
        put("101190104", "工业用地");
        put("101073207", "石膏");
        put("101016605", "不动产经营租赁（11%、10%、9%、3%）");
        put("101073000", "非金属矿");
        put("101120103", "城市土地使用税等级3");
        put("101060801", "个人房屋出租所得");
        put("101070608", "一等铅锌矿石");
        put("101070645", "三等砂金矿");
        put("101070702", "南方海盐");
        put("101074053", "天然沥青(2020)");
        put("101075104", "钒(2020)");
        put("101076135", "海泡石粘土(2020)");
        put("101070517", "三等铁矿石（重点矿山入炉地下矿）");
        put("101016802", "广播影视节目（作品）发行服务");
        put("101120115", "城市土地使用税等级15");
        put("101191211", "存量房（商品住房买卖）");
        put("101073209", "长石");
        put("101120205", "县城土地使用税等级5");
        put("101016102", "水路运输服务");
        put("101070618", "二等锡矿石");
        put("101010303", "糖");
        put("101211127", "丙烯醛（气）");
        put("101211128", "甲醇（气）");
        put("101074052", "油砂(2020)");
        put("101076306", "碧玺(2020)");
        put("101077103", "氦气(2020)");
        put("101010700", "石油加工及炼焦业");
        put("101011801", "贵重首饰");
        put("101020299", "其他酒");
        put("101073283", "黄龙玉");
        put("101211999", "其他大气污染物");
        put("101212231", "三氯乙烯（水）");
        put("101192119", "增量房（其他住房赠与）");
        put("101073240", "透辉石");
        put("101130800", "房地产纳税人清算后尾盘销售");
        put("101120401", "工矿区土地使用税等级1");
        put("101120410", "工矿区土地使用税等级10");
        put("101190501", "住房交换");
        put("101079800", "其他");
        put("101075201", "铜(2020)");
        put("101075225", "锂(2020)");
        put("101075237", "铼(2020)");
        put("101076101", "高岭土(2020)");
        put("101190251", "集体土地使用权出售（居住用地）");
        put("101196120", "存量房买卖");
        put("101070430", "一等石棉");
        put("101010301", "粮食、饲料");
        put("101019901", "滞纳金");
        put("101019902", "罚款");
        put("101061100", "其他所得");
        put("101211143", "苯乙烯（气）");
        put("101190214", "土地使用权出售（综合用地）");
        put("101016603", "不动产融资租赁（11%、10%、9%、3%）");
        put("101072209", "钛矿");
        put("101073109", "硫酸钾");
        put("101110501", "资金账簿");
        put("101140614", "净吨位超过10000吨的机动船舶");
        put("101189901", "滞纳金");
        put("101016703", "咨询服务");
        put("101089900", "滞纳金、罚款");
        put("101089902", "罚款");
        put("101110114", "租赁合同");
        put("101196321", "存量房互换（住房互换）");
        put("101070428", "硅线石");
        put("101010100", "烟草制品业");
        put("101010600", "服装及其他制品业");
        put("101010901", "汽车轮胎");
        put("101020207", "乙类啤酒[出厂价格3000元(不含增值税)/吨以下]");
        put("101070414", "石英砂");
        put("101219901", "滞纳金");
        put("101210000", "环境保护税");
        put("101211111", "一般性粉尘（气）");
        put("101212232", "四氯乙烯（水）");
        put("101192112", "增量房（保障性住房赠与）");
        put("101190211", "国有土地使用权出售（居住用地）");
        put("101017590", "居民日常服务");
        put("101072111", "钼矿");
        put("101016105", "铁路运输服务");
        put("101016901", "邮政普遍服务");
        put("101070626", "五等锑矿石");
        put("101030802", "转让商标权");
        put("101070699", "其他未列名有色金属矿原矿");
        put("101076000", "非金属矿产(2020)");
        put("101076131", "陶瓷土(2020)");
        put("101010404", "针织品");
        put("101073266", "矸石");
        put("101212229", "可吸附有机卤化物(AOX)(以Cl计)（水）");
        put("101212243", "2,4－二硝基氯苯（水）");
        put("101060905", "股权转让所得");
        put("101190223", "土地使用权赠与（工业用地）");
        put("101072213", "锗矿");
        put("101110104", "建筑安装工程承包合同");
        put("101070604", "二等铜矿石");
        put("101074041", "铀(2020)");
        put("101075215", "银(2020)");
        put("101076207", "安山岩(2020)");
        put("101076209", "板岩(2020)");
        put("101077202", "钾盐(2020)");
        put("101190235", "国有土地使用权互换（非居住用地）");
        put("101011800", "其他未列明制造业");
        put("101030902", "销售其他土地附着物");
        put("101060100", "工资薪金所得");
        put("101070810", "按取水量计税");
        put("101211120", "烟尘（气）");
        put("101021800", "高档化妆品");
        put("101212300", "PH值、色度、大肠菌群数、余氯量");
        put("101073238", "蛇纹岩");
        put("101070800", "水资源税");
        put("101190403", "其他赠与");
        put("101016404", "广告服务");
        put("101070616", "五等钨矿石");
        put("101110112", "承揽合同");
        put("101076150", "白垩(2020)");
        put("101070437", "自然硫");
        put("101070519", "二等铁矿石（非重点矿山入选露天矿）");
        put("101010000", "增值税");
        put("101011202", "贵金属");
        put("101060200", "经营所得");
        put("101212251", "总硒（水）");
        put("101100700", "从价计征");
        put("101073221", "辉绿岩");
        put("101073225", "陶瓷土");
        put("101073243", "宝石");
        put("101073252", "橄榄岩");
        put("101017304", "安全保护服务（3%）");
        put("101072200", "地方列名税目");
        put("101070605", "三等铜矿石");
        put("101070620", "四等锡矿石");
        put("101030704", "旅游业");
        put("101075105", "钛(2020)");
        put("101190112", "国有土地使用权出让（非居住用地）");
        put("101020401", "金银首饰、铂金首饰和钻石及钻石饰品");
        put("101060600", "特许权使用费所得");
        put("101070300", "煤炭");
        put("101070409", "珍珠岩");
        put("101030000", "营业税");
        put("101030201", "建筑");
        put("101212215", "阴离子表面活性剂(LAS)（水）");
        put("101212244", "苯酚（水）");
        put("101190210", "国有土地使用权出售");
        put("101191100", "增量房买卖");
        put("101073236", "角闪岩");
        put("101073246", "磷铁矿");
        put("101073255", "冰洲石");
        put("101130902", "非普通住宅（核定）");
        put("101110110", "技术合同");
        put("101140300", "挂车");
        put("101140501", "专用作业车");
        put("101199901", "滞纳金");
        put("101076121", "天然碱(2020)");
        put("101076138", "叶腊石(2020)");
        put("101076204", "石英岩(2020)");
        put("101011001", "水泥");
        put("101020104", "甲类卷烟[调拨价70元(不含增值税)/条以上(含70元)，从量计算]");
        put("101070401", "玉石");
        put("101213099", "其他固体废物（含半固态、液态废物）（固）");
        put("101211108", "硫酸雾（气）");
        put("101211144", "二硫化碳（气）");
        put("101212407", "医院(消毒-污水)（水）");
        put("101017305", "人力资源服务（6%、5%）");
        put("101073102", "硅藻土");
        put("101017002", "增值电信服务");
        put("101140106", "3.0升以上至4.0升(含)的乘用车");
        put("101999900", "滞纳金、罚款");
        put("101070700", "盐");
        put("101090000", "城市维护建设税");
        put("101076130", "硅藻土(2020)");
        put("101076215", "凝灰岩(2020)");
        put("101011100", "黑色金属冶炼及压延加工业");
        put("101011101", "生铁");
        put("101015000", "软件、集成电路");
        put("101211000", "大气污染物");
        put("101212210", "氨氮（水）");
        put("101212217", "总锌（水）");
        put("101212222", "有机磷农药(以P计)（水）");
        put("101190213", "土地使用权出售（工业用地）");
        put("101130803", "其他类型房地产（尾盘）");
        put("101017723", "构筑物（11%、10%、9%、5%）-增量房");
        put("101073113", "湖盐");
        put("101192200", "存量房赠与");
        put("101016902", "邮政特殊服务");
        put("101011799", "其他医药制造业");
        put("101074000", "能源矿产(2020)");
        put("101076212", "角闪岩(2020)");
        put("101190122", "集体土地使用权出让（非居住用地）");
        put("101070518", "四等铁矿石（重点矿山入炉地下矿）");
        put("101010101", "卷烟");
        put("101010800", "化学原料及化学制品业");
        put("101030799", "其他服务业");
        put("101030805", "转让著作权");
        put("101017307", "安全保护服务（5%）");
        put("101212409", "医院(不消毒-污水)（水）");
        put("101194200", "非住房作价入股");
        put("101073222", "芒硝");
        put("101017724", "构筑物（11%、10%、9%、5%）-二手房");
        put("101072211", "钛锆矿");
        put("101140630", "游艇");
        put("101079601", "停征品目1");
        put("101030808", "转让自然资源使用权");
        put("101180200", "林地");
        put("101080000", "固定资产投资方向调节税");
        put("101075000", "金属矿产(2020)");
        put("101076104", "石墨(2020)");
        put("101076107", "自然硫(2020)");
        put("101190121", "集体土地使用权出让（居住用地）");
        put("101010803", "农药");
        put("101020105", "乙类卷烟[调拨价70元(不含增值税)/条以下，从量计算]");
        put("101070410", "宝石");
        put("101073269", "片岩");
        put("101073232", "叶蜡石");
        put("101017711", "建筑物（11%、10%、9%、3%）-增量房");
        put("101073103", "高岭土");
        put("101070526", "六等铁矿石（非重点矿山入选地下矿）");
        put("101100400", "职工用房");
        put("101075103", "铬(2020)");
        put("101076146", "方解石(2020)");
        put("101076200", "岩石类(2020)");
        put("101030705", "仓储业");
        put("101214105", "超标13-15分贝（噪）");
        put("101212104", "六价铬（水）");
        put("101071104", "煤层（成）气");
        put("101110102", "加工承揽合同");
        put("101110304", "非交易转让股票B种股票");
        put("101149900", "滞纳金、罚款");
        put("101013400", "燃气");
        put("101016300", "信息技术服务");
        put("101070640", "五等岩金矿石");
        put("101077100", "水气矿产(2020)");
        put("101190225", "国有土地使用权赠与（非居住用地）");
        put("101196211", "增量房赠与（住房赠与）");
        put("101010103", "烟丝");
        put("101011304", "机动船舶");
        put("101011599", "其他电子通信设备制造业");
        put("101213000", "固体废物");
        put("101120215", "县城土地使用税等级15");
        put("101120414", "工矿区土地使用税等级14");
        put("101190241", "土地使用权作价投资、入股（居住用地）");
        put("101130802", "非普通住宅（尾盘）");
        put("101192225", "存量房非住房赠与（工业）");
        put("101120107", "城市土地使用税等级7");
        put("101120409", "工矿区土地使用税等级9");
        put("101190402", "其他房屋赠与");
        put("101169901", "滞纳金");
        put("101016101", "陆路货物运输服务");
        put("101016401", "设计服务");
        put("101076206", "辉绿岩(2020)");
        put("101030803", "转让专利权");
        put("101214106", "超标16分贝以上（噪）");
        put("101212208", "总氰化物（水）");
        put("101120113", "城市土地使用税等级13");
        put("101073105", "石灰石");
        put("101192211", "存量房（商品住房赠与）");
        put("101120100", "城市");
        put("101070633", "三等镍矿石");
        put("101109901", "滞纳金");
        put("101070424", "云母");
        put("101070426", "天然碱");
        put("101010207", "茶（17%、16%、13%）");
        put("101010799", "其他石油加工及炼焦业");
        put("101010999", "其他橡胶制品业");
        put("101070404", "焦宝石");
        put("101030202", "安装");
        put("101211103", "一氧化碳（气）");
        put("101211137", "硫化氢（气）");
        put("101211141", "甲硫醚（气）");
        put("101212403", "禽畜养殖场(鸡、鸭等家禽)（水）");
        put("101017190", "其他建筑服务");
        put("101073100", "中央列名税目");
        put("101192219", "存量房（其他住房赠与）");
        put("101120210", "县城土地使用税等级10");
        put("101140600", "船舶");
        put("101016508", "仓储服务");
        put("101159902", "罚款");
        put("101075200", "有色金属(2020)");
        put("101075204", "锡(2020)");
        put("101076141", "珍珠岩(2020)");
        put("101010199", "其他烟草加工业");
        put("101011403", "电线电缆");
        put("101013300", "煤气");
        put("101013900", "其他电力煤气及水的生产和供应业");
        put("101030104", "管道运输");
        put("101030500", "文化体育业");
        put("101073259", "红粒石");
        put("101211900", "其他大气污染物");
        put("101212109", "总铍（水）");
        put("101212250", "丙烯腈（水）");
        put("101190190", "其他用地");
        put("101073253", "红柱石");
        put("101192212", "存量房（保障性住房赠与）");
        put("101192222", "存量房非住房赠与（办公）");
        put("101110106", "货物运输合同(按运输费用万分之五贴花)");
        put("101076102", "石灰岩(2020)");
        put("101076115", "硅线石（矽线石）(2020)");
        put("101076303", "宝石级金刚石(2020)");
        put("101190260", "集体土地使用权赠与");
        put("101070524", "四等铁矿石（非重点矿山入选地下矿）");
        put("101012100", "原煤");
        put("101020108", "烟丝");
        put("101030702", "旅店业");
        put("101211117", "铍及其化合物（气）");
        put("101211122", "甲苯（气）");
        put("101213005", "粉煤灰（固）");
        put("101192110", "增量房赠与（住房赠与）");
        put("101190231", "国有土地使用权互换（居住用地）");
        put("101073213", "重晶石");
        put("101073239", "天然碱");
        put("101073257", "海泡石");
        put("101017530", "教育服务");
        put("101073203", "花岗岩");
        put("101110303", "非交易转让股票A种股票");
        put("101120307", "建制镇土地使用税等级7");
        put("101139902", "罚款");
        put("101140101", "1.0升（含）以下的乘用车");
        put("101016204", "合同能源管理服务");
        put("101070531", "四等铁矿石（非重点矿山入炉地下矿）");
        put("101169902", "罚款");
        put("101076145", "毒重石(2020)");
        put("101010699", "其他服装及其他制品业");
        put("101014002", "商业（11%、10%、9%）");
        put("101070405", "萤石");
        put("101070407", "膨润土");
        put("101219900", "滞纳金、罚款");
        put("101120413", "工矿区土地使用税等级13");
        put("101017570", "餐饮服务");
        put("101072110", "钨矿");
        put("101110500", "营业账簿");
        put("101120101", "城市土地使用税等级1");
        put("101120200", "县城");
        put("101120204", "县城土地使用税等级4");
        put("101016700", "鉴证咨询服务");
        put("101120199", "城市其他等级");
        put("101070648", "钒矿石");
        put("101076125", "砷(2020)");
        put("101076133", "铁矾土(2020)");
        put("101076154", "石榴子石(2020)");
        put("101076213", "页岩(2020)");
        put("101196111", "增量房买卖（住房买卖）");
        put("101010899", "其他化学原料及化学制品业");
        put("101030607", "舞厅");
        put("101073280", "叶蜡石");
        put("101073284", "水镁");
        put("101120412", "工矿区土地使用税等级12");
        put("101190105", "综合用地");
        put("101073231", "砂岩");
        put("101072201", "锰矿");
        put("101110100", "经济合同");
        put("101120300", "建制镇");
        put("101190502", "其他房屋交换");
        put("101016510", "收派服务");
        put("101016303", "信息系统服务");
        put("101070609", "二等铅锌矿石");
        put("101070642", "七等岩金矿石");
        put("101030100", "交通运输业");
        put("101030608", "歌厅");
        put("101100000", "房产税");
        put("101074013", "页岩气(2020)");
        put("101074054", "石煤(2020)");
        put("101076231", "火山渣(2020)");
        put("101076232", "泥炭(2020)");
        put("101010203", "啤酒");
        put("101070420", "白云石");
        put("101073275", "霞石正长岩");
        put("101021600", "电池");
        put("101021700", "涂料");
        put("101194205", "非住房作价入股（工业）");
        put("101120403", "工矿区土地使用税等级3");
        put("101017000", "电信服务");
        put("101160000", "车辆购置税");
        put("101076118", "刚玉(2020)");
        put("101076305", "黄玉(2020)");
        put("101070522", "六等铁矿石（非重点矿山入选露天矿）");
        put("101069902", "罚款");
        put("101070406", "磷矿石");
        put("101030899", "其他转让无形资产");
        put("101073277", "黑滑石");
        put("101079993", "煤炭资源税滞纳金");
        put("101211121", "苯（气）");
        put("101211126", "乙醛（气）");
        put("101212105", "总砷（水）");
        put("101017560", "娱乐服务");
        put("101072206", "锂矿");
        put("101140613", "净吨位超过2000吨但不超过10000吨的机动船舶");
        put("101999901", "滞纳金");
        put("101196310", "增量房互换");
        put("101070440", "矿泉水、地下水");
        put("101010202", "黄酒");
        put("101012600", "黑色金属矿产品");
        put("101020201", "粮食白酒（从价计算）");
        put("101070000", "资源税");
        put("101021200", "高档手表");
        put("101030103", "航空运输");
        put("101079991", "水资源税滞纳金");
        put("101070649", "钨矿");
        put("101073224", "云母");
        put("101130603", "其他类型房地产（清算）");
        put("101131000", "整体转让在建工程");
        put("101017720", "构筑物");
        put("101140621", "净吨位不超过200吨的拖船、非机动驳船");
        put("101140622", "净吨位超过200吨但不超过2000吨的拖船、非机动驳船");
        put("101189900", "滞纳金、罚款");
        put("101990000", "其他税收收入");
        put("101016103", "航空运输服务");
        put("101070646", "四等砂金矿");
        put("101076301", "宝石(2020)");
        put("101070505", "五等铁矿石（重点矿山入选露天矿）");
        put("101020603", "柴油");
        put("101020604", "航空煤油");
        put("101030609", "卡拉OK舞厅");
        put("101021900", "超豪华小汽车");
        put("101211101", "二氧化硫（气）");
        put("101211124", "苯并(a)芘（气）");
        put("101212401", "禽畜养殖场(牛)（水）");
        put("101191112", "增量房（保障性住房买卖）");
        put("101191124", "增量房非住房买卖（附属建筑）");
        put("101073226", "石棉");
        put("101017800", "铁路建设基金");
        put("101073104", "萤石");
        put("101110400", "权利、许可证照");
        put("101120000", "城镇土地使用税");
        put("101180400", "农田水利用地");
        put("101016104", "管道运输服务");
        put("101090300", "非市区、县城、镇");
        put("101100599", "其他房屋出租");
        put("101016800", "广播影视服务");
        put("101010400", "纺织业");
        put("101012301", "工业盐");
        put("101070421", "硅灰石");
    }};

    public static final Map<String, String> zszm_dm = new HashMap<String, String>(){{
        put("1010602002500086", "商业销售");
        put("1010602002500129", "其他服务（代开发票） 1.5%");
        put("1010602002500160", "美容美发、洗脚、桑拿、保健按摩（4%）");
        put("1010602002500176", "设计、咨询、代理、旅店、仓储、茶楼、广告（征收率2%）");
        put("1010602002500643", "文化体育业—其他文化业");
        put("1010602002500652", "旅游业(征收率1.20%）");
        put("1010609022500004", "个人房屋转让（住宅）");
        put("HB04300326113411", "电石乙炔法生产醋酸乙烯酯过程中产生的重馏分 ");
        put("HB04300326400412", "锌黄颜料生产过程中产生的废水处理污泥 ");
        put("1010602002500334", "建筑业有外经证（代开发票）");
        put("1010602002500370", "水上运输");
        put("1010602002500400", "未列举其他货物销售0.6%");
        put("1010602002500409", "其它工程作业");
        put("1010602002500439", "非专利技术");
        put("HB09220500000001", "石油类（生产污水和机舱污水-海工）");
        put("1010602002500513", "建筑安装（征收率）");
        put("1011305022500002", "非普通住宅独栋（预征）");
        put("1011009002500001", "住宅出租4%");
        put("HB09112000000004", "烟尘（餐饮业300~500（含）平方米-气）");
        put("1010798002500003", "公路建设(特大桥梁,特长隧道)");
        put("1011805011510001", "一级35元");
        put("1011805021510004", "四级15元");
        put("1021303000000099", "其他（外卖）");
        put("3043313001500003", "居民代征（建制镇）");
        put("1010608002500007", "个人非住宅出租（代开票）0.7%");
        put("1010602002500014", "电力、热力生产和供应业");
        put("HB04300326301104", "农药生产过程中产生的废水处理污泥（不包括赤霉酸生产废水生化处理污泥）和蒸发处理残渣（液）");
        put("HB04300390004649", "离子交换装置（不包括饮用水、工业纯水和锅炉软化水制备装置以及废水处理成套工艺中的离子交换装置）再生过程中产生的废水处理污泥");
        put("HB04300390020108", "清洗金属零部件过程中产生的废弃的煤油、柴油、汽油及其他由石油和煤炼制生产的溶剂油");
        put("HB04300390025612", "使用酸、碱或者有机溶剂清洗容器设备过程中剥离下的废油漆、废染料、废涂料");
        put("1010602002500103", "文化、体育业");
        put("1010602002500622", "电子游艺1.8%");
        put("1010602002500762", "医疗(征收率1.5%)");
        put("1010602002500821", "装卸搬运(个体定税)");
        put("1010603002500020", "音乐茶座");
        put("1010603002500027", "旅游业");
        put("HB04300390001513", "湿法冶金、表面处理和制药行业重金属、抗生素提取、分离过程产生的废弃离子交换树脂，以及工业废水处理过程产生的废弃离子交换树脂");
        put("HB04300307100208", "以矿物油为连续相配制钻井泥浆用于石油开采所产生的钻井岩屑和废弃钻井泥浆");
        put("1010602002500388", "公共交通、装卸、人力三轮、其他交通运输");
        put("HB04300319300221", "皮革、毛皮鞣制及切削过程产生的含铬废碎料");
        put("1010602002500473", "娱乐业（歌厅、舞厅、卡拉OK歌舞厅）");
        put("1011801011510004", "四级15元");
        put("1011905012500007", "公用设施");
        put("1030199012500004", "原煤20");
        put("1010702002500002", "征收率5.32%");
        put("1010798002500014", "独立标段0.1%");
        put("1010602001500001", "代开货运发票减按0.5%");
        put("1021398000000099", "其他");
        put("1010602002500842", "服务业（场地租赁，其他财产租赁）");
        put("1010602002500868", "电子游艺厅");
        put("1010602002500869", "室内娱乐业");
        put("1011203992500002", "土地使用税等级12");
        put("1011204992500001", "土地使用税等级11");
        put("1010602002500202", "医疗服务1.2%");
        put("1010602002500195", "出租扣件");
        put("1010602002500855", "商务服务业");
        put("1010602002500693", "娱乐业--网吧（未批准的）");
        put("1010602002500808", "装修装饰（1.5%）");
        put("1010602002500835", "其他工业产品");
        put("1010603002500034", "客货运输车（征收率）");
        put("HB04300326102911", "α-氯甲苯、苯甲酰氯和含此类官能团的化学品生产过程中产生的蒸馏残渣");
        put("HB04300326110611", "苯和乙烯直接催化、乙苯和丙烯共氧化、乙苯催化脱氢生产苯乙烯过程中产生的重馏分 ");
        put("HB04300326112011", "甲苯光气法生产苯甲酰氯产品精制过程中产生的重馏分 ");
        put("HB04300327500502", "其他兽药生产过程中产生的废脱色过滤介质及吸附剂 ");
        put("1010602002500351", "服务业－－美容美发、洗脚、保健按摩、桑拿");
        put("1010602002500412", "邮电通讯业");
        put("1010602002500418", "广告业(含广告代理业)");
        put("HB04300332200229", "混汞法提金工艺产生的含汞粉尘、残渣 ");
        put("1010602002500527", "其他服务业0.9%");
        put("1010761459900068", "原矿");
        put("1011496012500033", "摩托车(2011)");
        put("1010704992500001", "建筑（南川-税率100%）");
        put("1010400012500036", "运输企业10%");
        put("1010701002500002", "征收率5.39%");
        put("1010608002500017", "个人非住宅租赁");
        put("1010602002500877", "其他软件开发");
        put("1010400002500007", "服务业-房屋租赁");
        put("101070800SZ02002", "城镇公共供水");
        put("HB04300327500202", "使用砷或者有机砷化合物生产兽药过程中产生的蒸馏残余物");
        put("1010602002500100", "二手房交易（非住宅）");
        put("1010602002500112", "客运36座以上(个体定税)");
        put("1010602002500113", "其他文化业(个体定税)");
        put("1010602002500186", "服务业-个体医疗行业月收入额1万元至5万元（含）的（2011年9月1日起）");
        put("1010602002500192", "零份发票－修缮");
        put("1010602002500591", "个人住房出租");
        put("1010602002500856", "专业技术服务业");
        put("1010602002500697", "其他(征收率1.8%)");
        put("1010602002500806", "文化体育业");
        put("1010602002500824", "律师(征收率8%)");
        put("1010603002500064", "总机构、符合条件的二级及以下分支机构及其项目部，应核定征收个人所得税");
        put("HB04300325200511", "煤焦油加工过程中焦油储存设施中的焦油渣");
        put("HB04300326100811", "乙烯法制乙醛生产过程中产生的蒸馏次要馏分 ");
        put("HB04300333606817", "使用铬化合物进行抗蚀层化学硬化产生的废渣和废水处理污泥");
        put("HB04300390022208", "石油炼制废水气浮、隔油、絮凝沉淀等处理过程中产生的浮油和污泥 ");
        put("1010602002500331", "划船、游艇等0.9%");
        put("1010602002500376", "销售不动产(门面征收率3%)");
        put("HB04300326117750", "羟丙腈氨化、加氢生产3-氨基-1-丙醇过程中产生的废催化剂");
        put("1010602002500425", "销售不动产（住宅）");
        put("HB04300390004049", "无机化工行业生产过程中集（除）尘装置收集的粉尘 ");
        put("1010602002500240", "歌厅");
        put("1010602002500487", "旅店业");
        put("1010602002500507", "建筑安装其他工程作业（征收率1%）");
        put("1010400012500016", "批发和零售贸易业");
        put("1011308012500001", "普通住宅（尾盘）");
        put("1011904012500002", "商业用房");
        put("1010704992500002", "建筑（南川-税率35%）");
        put("HB01240500000006", "洗染服务业（水洗机（台）-水）");
        put("1010703032500001", "征收率3%");
        put("1010704992500009", "冰洲石");
        put("1010796012500010", "多渣混合料基层");
        put("1011005992509999", "其他房屋出租");
        put("3017601021500005", "一般性生产建设项目（区县级）");
        put("1010602002500224", "歌厅、舞厅、卡啦OK歌舞厅（包括夜总会、练歌房、恋歌房）、高尔夫球、电子游戏机");
        put("1010602002500005", "服务业（个体定税）");
        put("1010602002500883", "建筑工程机械与设备经营租赁");
        put("HB04300326107240", "醚及醚类化合物生产过程（不包括成醚反应之前的合成过程）中产生的醚类残液、反应残余物、废水处理污泥（不包括废水生化处理污泥）");
        put("1010602002500119", "旅店业（征收率1.2%）");
        put("1010602002500190", "煤炭及金属矿产品采掘业");
        put("1010603002500014", "未列举的其他服务业");
        put("HB04300333606317", "其他电镀工艺产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300390040506", "900-401-06、900-402-06、900-404-06中所列废有机溶剂再生处理过程中产生的废活性炭及其他过滤吸附介质");
        put("HB04300390040806", "90040206 和 90040406 中所列废物分馏再生过程中产生的釜底残渣 ");
        put("1010602002500342", "广 告 业");
        put("HB04300326115250", "有机溶剂生产过程中产生的废催化剂 ");
        put("HB04300326115350", "丙烯腈合成过程中产生的废催化剂 ");
        put("HB04300390035135", "使用氢氧化钠进行丝光处理过程中产生的废碱液 ");
        put("1010602002500498", "原煤1800%");
        put("1011308032500001", "其他类型房地产（尾盘）");
        put("1010752239900031", "选矿");
        put("1010762099900084", "原矿");
        put("1011903012500009", "公用设施");
        put("1011204052500001", "生态体育设施");
        put("HB09112000000005", "烟尘（餐饮业500~1000（含）平方米-气）");
        put("1011803001510003", "三级20元");
        put("1011807001510003", "三级20元");
        put("101070800SZ02001", "农业");
        put("1010602002500182", "服务业（饮食业、旅店业、代理业、仓储业）");
        put("1010602002500559", "服务业（广告业）");
        put("1010602002500589", "原煤1000%");
        put("1010602002500592", "未列举的其他服务业(个体定税)");
        put("1010609022500020", "转让旧房非住宅(零发3%)（征收率）");
        put("1010609022500028", "房屋拍卖所得3%");
        put("1010602002500675", "销售货物(30000元(含)-80000元)");
        put("1010602002500687", "原煤200%");
        put("1010602002500730", "工业生产");
        put("1010604002500014", "建筑、安装、修缮、装饰、其它工程作业");
        put("HB09111700000001", "铍及其化合物（气－海工）");
        put("HB04300332100111", "有色金属火法冶炼过程中产生的焦油状残余物 ");
        put("HB04300390029912", "生产、销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的油墨、染料、颜料、油漆（不包括水性漆）");
        put("HB04300326601016", "显（定）影剂、正负胶片、像纸、感光材料生产过程中产生的残渣和废水处理污泥");
        put("HB04300390001916", "其他行业产生的废显（定）影剂、胶片和废像纸");
        put("HB04300333605317", "使用镉和电镀化学品进行镀镉产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300377200218", "生活垃圾焚烧飞灰 ");
        put("HB04300390041006", "90040206 和 90040406 中所列废物再生处理过程中产生的废水处理浮渣和污泥（不包括废水生化处理污泥） ");
        put("1010602002500303", "服务业--仓储 设计 制图 美容美发、桑拿 洗脚城、保健按摩、茶楼、理发等");
        put("1010602002500304", "服务业--饮食业、租赁业、代理业、旅店业、旅游业、仓储业、广告业等");
        put("1010602002500371", "体育");
        put("HB04300326115950", "二甲苯临氢异构化反应过程中产生的废催化剂 ");
        put("HB04300326113821", "以重铬酸钠和浓硫酸为原料生产铬酸酐过程中产生的含铬废液 ");
        put("HB04300337300236", "拆船过程中产生的石棉废物 ");
        put("HB04300326108847", "钡化合物（不包括硫酸钡）生产过程中产生的熔渣、集（除）尘装置收集的粉尘、反应残余物、废水处理污泥 ");
        put("HB04300332100348", "粗锌精炼加工过程中湿法除尘产生的废水处理污泥");
        put("1011496012500017", "51-150T");
        put("1010762139900086", "原矿");
        put("1011496012500029", "微型客车");
        put("1011901012500004", "综合用途");
        put("1011904012500004", "厂房");
        put("1010704992500019", "大理石");
        put("1011801011510007", "二级25元(1.5倍)");
        put("1010704992500025", "煤矸石20%");
        put("HB04300327600502", "利用生物技术生产生物化学药品、基因工程药物及中间体过程中产生的废弃的产品、原料药和中间体");
        put("1010602002500101", "歌舞表演");
        put("1010602002500144", "无证网吧、卡丁车、飞镖射箭");
        put("1010609022500015", "转让住宅");
        put("1010609022500016", "销售不动产－－非住宅");
        put("1010602002500831", "装饰装修（征收率1.5%）");
        put("1010603002500023", "建筑安装承包经营所得");
        put("HB04300325201211", "焦化粗苯酸洗法精制过程产生的酸焦油及其他精制过程产生的蒸馏残渣");
        put("HB04300325201311", "焦炭生产过程中产生的脱硫废液 ");
        put("HB04300326110011", "苯和丙烯生产苯酚和丙酮过程中产生的重馏分 ");
        put("HB04300326112211", "甲苯连续光氯化法、无光热氯化法生产氯化苄过程中产生的重馏分 ");
        put("HB04300326700115", "炸药生产和加工过程中产生的废水处理污泥 ");
        put("1010602002500335", "其他服务（代开发票）");
        put("1010602002500444", "歌厅、卡拉OK厅(包房）");
        put("HB04300390002123", "使用氢氧化钠、锌粉进行贵金属沉淀过程中产生的废液和废水处理污泥");
        put("HB04300309100329", "汞矿采选过程中产生的尾砂和集（除）尘装置收集的粉尘 ");
        put("HB04300390002229", "废弃的含汞催化剂 ");
        put("1010602002500495", "代理、旅店、饮食、旅游、仓储、租赁业1.2%");
        put("1011303012500002", "房地产项目预征(3.5%)");
        put("1010740219900005", "原矿");
        put("1010751049900018", "选矿");
        put("1011801011510001", "一级35元");
        put("1011905012500006", "车库（车位）");
        put("1010798002500001", "建筑");
        put("HB01240500000008", "美容美发保健业（（座位）-水）");
        put("1010602002500236", "文化体育1.02%");
        put("1010602002500870", "彩票业");
        put("1010602002500009", "其他饮食(个体定税)");
        put("30714A0021500001", "探矿权使用费（部本级）");
        put("HB04300390001714", "研究、开发和教学活动中产生的对人类或者环境影响不明的化学物质废物");
        put("HB04300390005349", "已禁止使用的，所有者申报废弃的，以及有关部门依法收缴或者接收且需要销毁的《关于持久性有机污染物的斯德哥尔摩公约》管控的化学物质（不包括本名录HW04、HW05、HW10类别的危险废物）");
        put("1010602002500072", "安装(个体)");
        put("1010602002500563", "装璜所得（100万以上）");
        put("1010602002500576", "广告、设计、制图");
        put("1010602002500850", "设备租赁业");
        put("HB04300326113211", "乙醛氧化生产醋酸蒸馏过程中产生的重馏分 ");
        put("HB04300332103248", "铜火法冶炼烟气净化产生的污酸处理过程产生的砷渣");
        put("HB04300339800422", "线路板生产过程中产生的废蚀铜液 ");
        put("HB04300326105028", "碲及其化合物生产过程中产生的熔渣、集（除）尘装置收集的粉尘和废水处理污泥 ");
        put("HB04300332101348", "铅锌冶炼过程中，提取金、银、铋、镉、钴、铟、锗、铊、碲等金属过程中产生的废渣 ");
        put("HB09114100000001", "甲硫醚（气－海工）");
        put("1011305032500001", "其他类型房地产（预征）");
        put("1010761339900059", "选矿");
        put("1010772019900098", "选矿");
        put("1020312012500002", "灵活就业人员基本医疗保险(11%)");
        put("1011006012500005", "高档住房1%");
        put("1039901012500005", "工会经费（重庆机场集团系统）");
        put("101110115YH22003", "航空货物运输合同");
        put("1010400012500003", "商业(批发、零售)2%");
        put("101070800SZ02003", "特种取用水");
        put("1010602002500141", "居民服务业0.9%");
        put("1010602002500153", "文化体育业－－表演歌舞");
        put("1010602002500663", "制造业");
        put("1010609022500012", "财产转让所得（非住房）");
        put("1010602002500851", "文化及日用品租赁业");
        put("1010602002500728", "销售不动产（代开发票）住宅");
        put("1010603002500049", "装卸搬运和其他运输服务业");
        put("1010604002500001", "批发、零售");
        put("HB04300326110711", "二硝基甲苯还原催化生产甲苯二胺过程中产生的重馏分 ");
        put("HB04300323100116", "使用显影剂进行胶卷显影，使用定影剂进行胶卷定影，以及使用铁氰化钾、硫代硫酸盐进行影像减薄（漂白）产生的废显（定）影剂、胶片和废像纸");
        put("HB04300333610117", "使用铬酸进行塑料表面粗化产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300326300204", "乙拌磷生产过程中甲苯回收工艺产生的蒸馏残渣 ");
        put("1010602002500305", "文化体育业--歌舞");
        put("1010602002500328", "货物运输(征收率1.5%)");
        put("1010602002500340", "旅店业、代理业");
        put("HB04300319300335", "使用氢氧化钙、硫化钠进行浸灰产生的废碱液 ");
        put("HB09114300000001", "苯乙烯（气－海工）");
        put("1010602002500548", "其他表演(征收率0.60%）");
        put("1011302022500005", "房地产项目清算查账(10%)");
        put("1011311002500003", "旧房转让其他类型房地产（核定）");
        put("1010602001500002", "酒店业");
        put("1011496012500031", "中型客车(2011)");
        put("1010706992509999", "其他");
        put("1010796012500007", "预制板");
        put("1010602002500905", "外来建筑安装无外出经营证明或未报验登记的");
        put("3042401011500003", "三类区域（市级）");
        put("101110112YH22004", "复制合同");
        put("101110113YH22001", "工程勘察合同");
        put("1010602002500208", "租赁业（住房出租）(个体定税)");
        put("1010602002500226", "律师事务所年收入额100万元(含)以下的部分");
        put("1010301012500004", "缆车索道运输");
        put("1010400012500014", "餐饮业");
        put("1010602002500609", "租赁业");
        put("1010602002500625", "销售不动产（代开发票）非住宅");
        put("1010602002500674", "公路旅客运输");
        put("1010602002500793", "美容美发、保健按摩、桑拿足浴及类似经营项目");
        put("1010603002500004", "人力三轮车");
        put("1010603002500051", "煤炭行业销售平均单价100元-200元（含）");
        put("1010603002500054", "转让无形资产");
        put("HB04300326113311", "丁烷液相氧化生产醋酸过程中产生的重馏分 ");
        put("HB04300326510213", "树脂、合成乳胶、增塑剂、胶水/胶合剂生产过程中合成、酯化、缩合等工序产生的废母液");
        put("HB04300326700415", "三硝基甲苯生产过程中产生的粉红水、红水，以及废水处理污泥 ");
        put("HB04300326300904", "农药生产过程中产生的废母液、反应罐及容器清洗废液");
        put("1010602002500367", "医疗(征收率0.60%）");
        put("HB04300326115650", "烷烃脱氢过程中产生的废催化剂 ");
        put("1010602002500407", "(零)其他表演、其他文化业");
        put("1010602002500460", "仓储");
        put("HB04300326105129", "水银电解槽法生产氯气过程中盐水精制产生的盐水提纯污泥 ");
        put("HB09112500000001", "甲醛（气－海工）");
        put("1010602002500288", "销售不动产");
        put("1011911122500001", "历史保障住房买卖");
        put("1011309012500001", "房地产开发普通住宅（核定）");
        put("1011496012500032", "大型客车(2011)");
        put("1011202082500001", "土地使用税等级8");
        put("1010706992500003", "铝钒土");
        put("1011805011510002", "二级25元");
        put("1011808001510003", "三级20元");
        put("1021305000000099", "其他（即时配送）");
        put("1021307000000001", "货物运输及搬运服务");
        put("1010602002500206", "销售货物(个体定税)");
        put("1010608002500013", "租赁");
        put("1010602002500019", "娱乐业(应税所得率20%)");
        put("1010602002500875", "普通货物道路运输");
        put("HB04300326111011", "苯酚、三甲苯水解生产4,4'-二羟基二苯砜过程中产生的重馏分");
        put("HB04300390004149", "含有或者沾染毒性、感染性危险废物的废弃的包装物、容器、过滤吸附介质");
        put("1010602002500041", "销售货物(增值税3%)");
        put("1010602002500051", "交通运输业1.02");
        put("1010602002500055", "贷款利息（代开发票）");
        put("1010602002500094", "零份发票－装卸搬运");
        put("1010602002500095", "修理、修配业");
        put("1010602002500555", "交通运输辅助");
        put("1010602002500677", "出租钢管");
        put("1010602002500679", "摄影");
        put("1010602002500684", "个人房屋转让（住宅）1%");
        put("HB04300325200911", "轻油回收过程中的废水池残渣");
        put("HB04300384100501", "药物性废物 ");
        put("HB04300327200302", "化学药品制剂生产过程中产生的废脱色过滤介质及吸附剂");
        put("HB04300326301004", "农药生产过程中产生的废滤料及吸附剂");
        put("1010602002500341", "舞厅");
        put("1010602002500420", "水路货物运输业");
        put("HB04300309100148", "硫化铜矿、氧化铜矿等铜矿物采选过程中集（除）尘装置收集的粉尘 ");
        put("1010602002500517", "工业生产、加工、修理、修配");
        put("1010301012500001", "铁路运输");
        put("1011496012500021", "非机动船11-50T(外)");
        put("1010752029900021", "原矿");
        put("1011496012500030", "小型客车");
        put("1012596012500002", "牛");
        put("1012596012500003", "羊");
        put("1010703042500002", "征收率1.95%");
        put("1010703042500004", "征收率2.1%");
        put("1010608002500002", "其他财产租赁1.5%");
        put("1011202992500002", "土地使用税等级12");
        put("101070800SZ01005", "疏干排水（回收利用）");
        put("1010602002500145", "从事制造业的以缴纳增值税为主的个体纳税人(0.84%)");
        put("1010602002500580", "建筑安装、装修、装饰、修缮、人力运输、装卸和其他工程作业");
        put("1010602002500649", "非煤矿产");
        put("1010602002500660", "工业");
        put("1010609992500011", "转让无形资产");
        put("1010602002500789", "娱乐业(应税所得率)");
        put("1010603002500066", "市外总机构直接管理的项目部，应核定征收个人所得税");
        put("HB04300390025512", "使用各种颜料进行着色过程中产生的废颜料 ");
        put("HB04300326510113", "树脂、合成乳胶、增塑剂、胶水/胶合剂合成过程产生的不合格产品（不包括热塑型树脂生产过程中聚合产物经脱除单体、低聚物、溶剂及其他助剂后产生的废料，以及热固型树脂固化后的固化体）");
        put("HB04300390002019", "金属羰基化合物生产、使用过程中产生的含有羰基化合物成分的废物 ");
        put("1010602002500312", "加工修理修配业");
        put("1010602002500356", "饮食、住宿");
        put("HB04300307200229", "天然气除汞净化过程中产生的含汞废物 ");
        put("HB04300332100448", "铅锌冶炼过程中，锌焙烧矿、锌氧化矿常规浸出法产生的浸出渣");
        put("HB04300332102048", "铅锌冶炼过程中，阴极铅精炼产生的氧化铅渣及碱渣 ");
        put("HB04300332102248", "铅锌冶炼烟气净化产生的污酸除砷处理过程产生的砷渣");
        put("1010761329900056", "原矿");
        put("1010761449900067", "选矿");
        put("1010701002500005", "征收率4.49%");
        put("1010706992500001", "汞  矿");
        put("1011805021510001", "一级35元");
        put("1010602002500212", "装饰作业有资质(零发1.61%)（征收率）");
        put("1010400002500005", "表演、播映、各类培训等");
        put("HB04300332300148", "以钨精矿为原料生产仲钨酸铵过程中碱分解产生的碱煮渣（钨渣）、除钼过程中产生的除钼渣和废水处理污泥");
        put("HB04300345100211", "固定床气化技术制煤气过程中产生的废水处理污泥（不包括废水生化处理污泥）");
        put("1010602002500046", "歌厅、舞厅、卡拉OK歌舞厅");
        put("1010602002500150", "客车");
        put("1010602002500172", "制造");
        put("1010602002500616", "律师事务所年收入额超过1000万元到1500万元(含)的部分");
        put("1010602002500641", "住宅转让");
        put("1010602002500689", "出租车(个体定税)");
        put("1010602002500737", "设计、制图");
        put("1010602002500801", "茶楼(大厅)");
        put("1010603002500055", "其他工程作业(个人承包承租)");
        put("HB04300326101011", "四氯化碳生产过程中产生的蒸馏残渣和重馏分 ");
        put("HB04300326111111", "甲苯硝基化合物羰基化法、甲苯碳酸二甲酯法生产甲苯二异氰酸酯过程中产生的重馏分 ");
        put("HB04300326112311", "偏二氯乙烯氢氯化法生产1,1,1-三氯乙烷过程中产生的重馏分");
        put("HB04300326112711", "碳五馏分分离生产异戊二烯过程中产生的重馏分 ");
        put("HB04300333605917", "使用钯和锡盐进行活化处理产生的废渣和废水处理污泥 ");
        put("HB04300326300804", "其他农药生产过程中产生的蒸馏及反应残余物（不包括赤霉酸发酵滤渣）");
        put("1010602002500325", "律师事务所年收入100万元至500万元（含）");
        put("HB04300326113924", "硫铁矿制酸过程中烟气净化产生的酸泥 ");
        put("HB04300326104525", "硒及其化合物生产过程中产生的熔渣、集（除）尘装置收集的粉尘和废水处理污泥 ");
        put("HB04300390002531", "使用硬脂酸铅进行抗黏涂层过程中产生的废物 ");
        put("HB04300326106337", "除农药以外其他有机磷化合物生产过程中产生的废水处理污泥 ");
        put("1010602002500479", "零份发票－装饰");
        put("1010602002500493", "商业（小规模纳税人）（1%）");
        put("1010400012500017", "其他行业");
        put("1010400012500030", "建筑装饰业");
        put("1010762219900092", "原矿");
        put("3014901002500001", "汇算清缴");
        put("1011311002500005", "旧房转让-车库（核定）");
        put("1011801021510002", "二级25元");
        put("1030199012500006", "焦炭");
        put("1010711012500004", "洗选煤68%");
        put("1010602002500866", "卫生服务业");
        put("1011201992500002", "土地使用税等级12");
        put("1011204992500003", "土地使用税等级13");
        put("1010400002500008", "转让无形资产-转让专利权、专利技术、著作权等");
        put("1010602002500154", "服务业－－仓储、租赁");
        put("1010602002500167", "货物运输（个体）");
        put("1010602002500556", "修理修配业(0.9%)");
        put("1010602002500683", "娱乐业");
        put("1010602002500741", "服务业－－以上未列举其他服务业");
        put("1010602002500744", "个体医疗");
        put("1010602002500765", "服务业（租赁业）");
        put("1010602002500767", "服务业-个人出租房屋-住宅出租（2011年9月1日起）");
        put("1010602002500771", "零份发票－个人门面出租");
        put("1010602002500776", "歌厅、舞厅、高尔夫球");
        put("HB04300384100401", "化学性废物 ");
        put("HB04300390022108", "废燃料油及燃料油储存过程中产生的油泥 ");
        put("HB04300326115150", "树脂、乳胶、增塑剂、胶水/胶合剂生产过程中合成、酯化、缩合等工序产生的废催化剂 ");
        put("HB04300326117350", "二氧化硫氧化生产硫酸过程中产生的废催化剂 ");
        put("HB04300326106938", "有机氰化物生产过程中产生的废水处理污泥 ");
        put("HB09113800000001", "氨（气－海工）");
        put("1010602002500239", "建筑安装、装饰业");
        put("1010602002500275", "教育");
        put("1010400012500020", "建筑业-发票代开(3%)");
        put("1011302012500004", "旧房查实");
        put("1011311002500004", "旧房转让个人其他类型房地产（核定）");
        put("1010752019900020", "选矿");
        put("1010761059900045", "选矿");
        put("1020316012500002", "职工大额医疗互助保险（个人缴纳）（2元）");
        put("1010702002500003", "征收率5.39%");
        put("1010704992500024", "其他石料");
        put("1010400012500013", "交通运输业");
        put("1010602002500063", "广告设计制图");
        put("1010602002500080", "批发和零售业");
        put("1010602002500574", "文化、体育、歌舞");
        put("1010602002500644", "其他—从事上述未列举的项目");
        put("1010602002500650", "装卸搬运(征收率0.60%）");
        put("1010609022500002", "房产交易核定征收非住房3%");
        put("1010609022500007", "财产转让所得（住房）");
        put("1010602002500718", "美容美发");
        put("1010602002500725", "游艺、高尔夫球等1.8%");
        put("1010602002500729", "转让无形资产（代开发票）");
        put("1010602002500830", "娱乐业(零发2%)（征收率）");
        put("HB04300377200111", "废矿物油再生过程中产生的酸焦油 ");
        put("HB04300326700215", "含爆炸品废水处理过程中产生的废活性炭 ");
        put("HB04300333606717", "使用含重铬酸盐的胶体、有机溶剂、黏合剂进行漩流式抗蚀涂布产生的废渣和废水处理污泥");
        put("1010602002500299", "其他表演(个体定税)");
        put("1010602002500314", "安装");
        put("HB09110800000001", "硫酸雾（气－海工）");
        put("1010602002500455", "住宅出租");
        put("HB04300326500129", "氯乙烯生产过程中含汞废水处理产生的废活性炭 ");
        put("HB04300330400231", "使用铅盐和铅氧化物进行显像管玻璃熔炼过程中产生的废渣 ");
        put("HB04300330800136", "石棉制品生产过程中产生的石棉尘、废石棉 ");
        put("HB09113700000001", "硫化氢（气－海工）");
        put("1010602002500269", "娱乐业（台球、保龄球）");
        put("1010602002500524", "游戏机");
        put("1011496012500010", "机动船301-500T(外)");
        put("1010761349900061", "选矿");
        put("1011904012500003", "办公用房");
        put("1010704992500006", "鹅卵石50%");
        put("1011805021510003", "三级20元");
        put("1010602002500205", "客运35座以下(个体定税)");
        put("1010602002500017", "建筑业、房地产开发业(应税所得率7%)");
        put("1011201992500003", "土地使用税等级13");
        put("1010400012500012", "建筑业");
        put("HB04300326401212", "其他油墨、染料、颜料、油漆（不包括水性漆）生产过程中产生的废水处理污泥和蒸发处理残渣（液）");
        put("1010602002500164", "有证网吧、电子游戏");
        put("1010602002500197", "歌厅、舞厅、卡拉OK歌舞厅、游戏机、高尔夫球");
        put("1010602002500602", "货物销售—批发（征收率1.2%）");
        put("1010609992500001", "转让专利权、专利技术、著作权等（单次或月累计1万元以下");
        put("1010602002500708", "（零）高尔夫、游艺");
        put("1010602002500751", "交通运输业—货物公路运输");
        put("1010602002500799", "娱乐+服务（征收率）（1.2%）");
        put("1010603002500011", "歌舞表演");
        put("1010603002500060", "外来建筑安装无外出经营证明或未报验登记的");
        put("1010604002500010", "其它劳务1.5%");
        put("HB09111500000001", "铅及其化合物（气－海工）");
        put("HB04300330900111", "电解铝及其他有色金属电解精炼过程中预焙阳极、碳块及其它碳素制品制造过程烟气处理所产生的含焦油废物");
        put("1010602002500330", "茶馆、旅馆0.9%");
        put("HB04300326115450", "聚乙烯合成过程中产生的废催化剂 ");
        put("HB09111100000001", "一般性粉尘（气－海工）");
        put("1010602002500545", "邮电通讯");
        put("1011302022500002", "房地产项目预征(3.5%)");
        put("1011303012500007", "房地产项目清算查账(永川60%)");
        put("1011904012500008", "其它");
        put("1010704992500023", "鹅卵石100%");
        put("101110115YH22001", "公路货物运输合同");
        put("1010602002500227", "修理");
        put("1010608002500009", "个人住宅出租（代开票）0.05%");
        put("1010602002500890", "基础软件开发");
        put("1011202992500003", "土地使用税等级13");
        put("HB04300390003136", "废石棉建材、废石棉绝缘材料");
        put("1010602002500092", "夜总会");
        put("1010602002500577", "打字、复印、刻字");
        put("1010602002500614", "销售农产品(农民销售自产农产品免征个人所得税)");
        put("1010609022500005", "个人转让住房所得(非住宅）");
        put("1010602002500787", "建筑业、房地产开发业(应税所得率)");
        put("1010602002500820", "美容、美发、洗脚、保健按摩、桑拿(个体定税)");
        put("HB04300326103211", "氯乙烯单体生产过程中蒸馏产生的重馏分 ");
        put("HB04300326111211", "苯直接氯化生产氯苯过程中产生的重馏分 ");
        put("HB04300326117050", "异丁烯和甲醇催化生产甲基叔丁基醚过程中产生的废催化剂 ");
        put("HB04300326117150", "以甲醇为原料采用铁钼法生产甲醛过程中产生的废铁钼催化剂");
        put("HB04300326117950", "甲乙酮与氨催化加氢生产2-氨基丁烷过程中产生的废催化剂");
        put("1010602002500403", "原煤2500%");
        put("1010602002500427", "租赁业0.9%");
        put("1010602002500440", "货物销售（3%）");
        put("1010602002500453", "服务业—其他服务业");
        put("HB04300390030734", "使用酸进行电解抛光处理产生的废酸液 ");
        put("HB04300390003645", "其他生产、销售及使用过程中产生的含有机卤化物废物（不包括HW06类） ");
        put("1010602002500496", "原煤150%");
        put("1010400012500023", "采掘业（不包括煤炭采掘业）");
        put("1011308022500001", "非普通住宅（尾盘）");
        put("1011496012500005", "机动船3001-10000T");
        put("1011496012500009", "机动船151-300T(外)");
        put("1010761569900074", "原矿");
        put("1010762339900094", "原矿");
        put("1011496012500026", "乘人车36座以上");
        put("1011901012500005", "办公用途");
        put("1030199012500002", "原煤40");
        put("1021305000000001", "同城物品配送服务");
        put("3042401011500005", "二类区域（区县级）");
        put("3071501021500005", "火电二氧化硫");
        put("101110200YH22007", "专利权转让书据");
        put("1010608002500019", "住宅出租(0.05%)");
        put("1010602002500895", "文艺创作与表演");
        put("1010602002500040", "饮食业");
        put("1010602002500553", "音乐茶座(征收率1.20%）");
        put("1010602002500638", "律师行业年经营收入额：100万元（含）以下的部分");
        put("1010602002500662", "服务业（其他项目）");
        put("1010609992500015", "股权转让所得");
        put("1010602002500828", "音乐茶座（征收率2%）");
        put("1010604002500015", "装卸搬运");
        put("HB04300326116450", "甲醇和氨气催化合成、蒸馏制备甲胺过程中产生的废催化剂 ");
        put("HB01230100000003", "pH值(2-3, 11-12)（水）");
        put("1010602002500441", "人力三轮");
        put("HB04300326113721", "铬铁矿生产铬盐过程中产生的其他废物 ");
        put("HB04300339800221", "使用铬酸进行钻孔除胶处理产生的废渣和废水处理污泥 ");
        put("HB04300390005231", "废铅蓄电池及废铅蓄电池拆解过程中产生的废铅板、废铅膏和酸液");
        put("HB04300390030434", "使用酸进行电解除油、金属表面敏化产生的废酸液 ");
        put("HB04300325101535", "石油炼制过程产生的废碱液和碱渣");
        put("1010602002500276", "设计、咨询、代理、设备租赁");
        put("1010602002500481", "旅店、饮食");
        put("1010761319900054", "原矿");
        put("1011496012500023", "非机动船151-300T(外)");
        put("1039901012500003", "工会经费（中央金融）");
        put("1010307072509999", "其他房屋出租");
        put("1010602002500156", "建筑业－－装饰");
        put("1010602002500170", "广告、设计、制图(征收率1.20%）");
        put("1010602002500572", "建筑业（代开发票）");
        put("1010602002500637", "租赁代理业、仓储业、专业理发");
        put("1010609022500010", "销售不动产—非住宅");
        put("1010602002500861", "保健服务业");
        put("1010602002500809", "个人房屋转让（非住宅）3%");
        put("HB04300326400612", "氧化铬绿颜料生产过程中产生的废水处理污泥 ");
        put("HB04300387300116", "电影厂产生的废显（定）影剂、胶片及废像纸 ");
        put("HB04300333600407", "热处理渗碳炉产生的热处理渗碳氰渣 ");
        put("HB04300326115750", "乙苯脱氢生产苯乙烯过程中产生的废催化剂 ");
        put("HB09111200000001", "石棉尘（气－海工）");
        put("1010602002500529", "公路内河运输(代开发票）");
        put("1011302022500004", "房地产项目预征其他非普通住宅");
        put("1011496012500015", "10T以下");
        put("1010761089900048", "原矿");
        put("1010761109900051", "选矿");
        put("1011496012500035", "机动船2001-3000T(外)");
        put("1010400012500031", "对外来建安0.2%");
        put("1011310002500002", "整体转让在建工程（土地）");
        put("1011305032500002", "标准厂房和楼宇产业园2%");
        put("1010703042500003", "征收率2.04%");
        put("1010796012500011", "水泥稳定粹石基层");
        put("3043313001500007", "按量/吨计征");
        put("3010201001500003", "地方农网还贷资金收入-汇算清缴");
        put("1010602002500216", "广告");
        put("HB04300309100248", "硫砷化合物（雌黄、雄黄及硫砷铁矿）或者其他含砷化合物的金属矿石采选过程中集（除）尘装置收集的粉尘");
        put("HB04300325101108", "石油炼制过程中进油管路过滤或者分离装置产生的残渣");
        put("1010602002500098", "服务业");
        put("1010602002500187", "服务业-其他（2011年9月1日）");
        put("1010602002500777", "销售不动产（代开发票）");
        put("1010603002500007", "修缮(个人承包承租)");
        put("HB04300326102311", "二硝基甲苯加氢法生产甲苯二胺过程中产品精制产生的废液 ");
        put("HB04300327500402", "其他兽药生产过程中产生的蒸馏及反应残余物 ");
        put("HB04300390021008", "含油废水处理中隔油、气浮、沉淀等处理过程中产生的浮油、浮渣和污泥（不包括废水生化处理污泥）");
        put("HB04300339800108", "锂电池隔膜生产过程中产生的废白油 ");
        put("HB04300390022008", "变压器维护、更换和拆解过程中产生的废变压器油 ");
        put("HB04300332103448", "铝灰热回收铝过程烟气处理集（除）尘装置收集的粉尘，铝冶炼和再生过程烟气（包括：再生铝熔炼烟气、铝液熔体净化、除杂、合金化、铸造烟气）处理集（除）尘装置收集的粉尘");
        put("HB01230100000001", "pH值(0-1, 13-14)（水）");
        put("1010602002500422", "煤炭行业销售平均单价100元-200元（含）");
        put("HB04300390030634", "使用硝酸进行钝化产生的废酸液 ");
        put("HB09113500000001", "氯乙烯（气－海工）");
        put("1010602002500282", "美容、美法、洗脚、按摩、桑拿");
        put("1010602002500488", "仓储业");
        put("1010602002500546", "修理修配");
        put("1010761469900071", "选矿");
        put("1010762019900076", "原矿");
        put("3016801002500001", "非居民用电");
        put("1021301000000099", "其他（出行）");
        put("1010602002500903", "为确保此次双定户信息");
        put("1010306992509999", "其他娱乐业（税率10%）");
        put("101070800SZ01001", "疏干排水（直接外排）");
        put("101070800SZ01008", "水源热泵（闭式）");
        put("HB04300326401112", "染料、颜料及中间体生产过程中产生的废母液、残渣、废吸附剂和中间体");
        put("HB04300377200649", "采用物理、化学、物理化学或者生物方法处理或者处置毒性或者感染性危险废物过程中产生的废水处理污泥和废水处理残渣（液）");
        put("1010602002500198", "其他服务（1%）");
        put("1010602002500587", "打字、复印、停车、洗车、照相服务1.2%");
        put("1010602002500595", "租赁业（非住房出租）(个体定税)");
        put("1010602002500604", "医疗服务（征收率1%）");
        put("1010602002500627", "管道运输");
        put("1010602002500634", "建筑业－－修缮");
        put("1010602002500647", "转让住宅");
        put("1010602002500658", "其他行业（征收率1.5%）");
        put("1010603002500041", "出租车");
        put("1010603002500044", "装卸搬运");
        put("1010604002500012", "设计、咨询、代理2%");
        put("HB04300326300104", "氯丹生产过程中六氯环戊二烯过滤产生的残余物，及氯化反应器真空汽提产生的废物");
        put("HB04300333610017", "使用铬酸进行阳极氧化产生的废槽液、槽渣和废水处理污泥");
        put("HB04300326104321", "铬铁矿生产铬盐过程中产生的芒硝 ");
        put("HB04300326104421", "铬铁矿生产铬盐过程中产生的废水处理污泥 ");
        put("HB09111800000001", "镍及其化合物（气－海工）");
        put("1010602002500268", "娱乐业（歌厅、舞厅、卡拉OK、歌舞厅(包括夜总会、练歌房、恋歌房)、音乐茶座（包括酒吧）、高尔夫球、游艺（如射击、狩猎、跑马、游戏机、蹦极、卡丁车、热气球、动力伞、射箭、飞镖等）");
        put("1010602002500475", "交通运输业-客运（2011年9月1日起）");
        put("1010602002500514", "其他食品");
        put("1010761369900063", "选矿");
        put("1010762139900087", "选矿");
        put("HB09111100000004", "一般性粉尘（市政拆迁施工）");
        put("1010704392500001", "石灰石200%");
        put("1011005012500001", "住宅出租4%");
        put("1012130991500002", "磷石膏");
        put("3043313001500004", "按人数计征");
        put("1010602002500209", "保龄球(征收率1.2%)");
        put("1010608012500003", "住宅出租");
        put("1010602002500011", "其他服务(个体定税)");
        put("1010602002500897", "日用家电零售");
        put("HB04300331400321", "铁铬合金生产过程中金属铬铝热法冶炼产生的冶炼渣");
        put("1010602002500108", "仓储业(个体定税)");
        put("1010602002500149", "歌厅、舞厅、卡拉OK歌舞厅（包括夜总会等提供音像设备、乐队、文艺表演并设有包房供成人消费的娱乐场所）、游戏机、高尔夫球");
        put("1010602002500159", "文化、体育业-歌舞、其他文化业");
        put("1010602002500162", "服务业—旅游、仓储、代理、旅馆、租赁、美容、美发、洗脚、保健按摩、桑拿");
        put("1010602002500838", "石油销售 0.5%");
        put("1010602002500716", "其他服务业");
        put("1010602002500749", "广告、设计、制图（3%）");
        put("HB04300333605217", "使用锌和电镀化学品进行镀锌产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300326600305", "木材防腐化学品生产、配制过程中产生的过期原料和废弃产品");
        put("HB04300325100308", "石油炼制过程中含油废水隔油、气浮、沉淀等处理过程中产生的浮油、浮渣和污泥（不包括废水生化处理污泥）");
        put("HB04300390021408", "车辆、轮船及其它机械维修过程中产生的废发动机油、制动器油、自动变速器油、齿轮油等废润滑油");
        put("HB04300326116550", "催化重整生产高辛烷值汽油和轻芳烃过程中产生的废催化剂 ");
        put("1010602002500437", "网吧、卡丁车、飞镖射箭、音乐茶坐");
        put("HB04300326104827", "氧化锑生产过程中产生的熔渣 ");
        put("HB04300390039935", "生产、销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的强碱性擦洗粉、清洁剂、污迹去除剂以及其他强碱性废碱液、固态碱和碱渣");
        put("1010602002500250", "娱乐业—KTV、夜总会、娱乐城");
        put("1010602002500536", "货运车");
        put("1010602002500541", "居民服务、居民其他服务");
        put("1010761029900043", "选矿");
        put("1010761059900044", "原矿");
        put("1010704992500007", "矽石（五氧化二硅）");
        put("1011005992500001", "向个人出租住房");
        put("1012130991500001", "脱硫石膏");
        put("1010602002500867", "文化艺术业");
        put("1010306992500001", "其他娱乐业（税率5%）");
        put("1010602002500175", "代理、咨询、住宿、广告、设计、仓储(征收率2%)");
        put("1010602002500849", "房屋租赁业");
        put("1010602002500712", "其他工程作业（代开发票1.55%）");
        put("1010602002500804", "设备出租");
        put("1010603002500005", "游艺");
        put("1010603002500015", "建筑业(1%)");
        put("1010603002500045", "销售货物");
        put("1010101041500002", "烟叶（11%）");
        put("HB04300326111911", "乙烯氧氯化法生产三氯乙烯、四氯乙烯过程中产生的重馏分 ");
        put("HB04300326112111", "甲苯苯甲酸法生产苯甲酰氯产品精制过程中产生的重馏分 ");
        put("HB04300323100216", "使用显影剂进行印刷显影、抗蚀图形显影，以及凸版印刷产生的废显（定）影剂、胶片和废像纸");
        put("HB04300326117250", "邻二甲苯氧化法生产邻苯二甲酸酐过程中产生的废催化剂 ");
        put("1010602002500443", "驾校-车");
        put("1010602002500278", "销售货物(80000元及以上)");
        put("1010602002500547", "加工制造");
        put("1010400012500024", "采掘业（无烟煤）");
        put("1010307992500002", "其他服务");
        put("1011806001510004", "四级15元");
        put("1021307000000099", "其他（同城货运）");
        put("1010603002500067", "建筑业(1%)（征收率1%)");
        put("1010602002500888", "其他农业专业及辅助性活动");
        put("1010602002500878", "危险废物治理");
        put("HB04300327500802", "兽药生产过程中产生的废弃的产品及原料药");
        put("1010609002500001", "财产转让所得");
        put("1010609022500027", "转让住宅1%");
        put("1010602002500846", "信息技术服务业");
        put("1010602002500860", "洗浴服务业");
        put("1010602002500754", "贷款利息");
        put("HB04300326112911", "水合法、发酵法生产乙醇过程中产生的重馏分 ");
        put("HB04300326600916", "显（定）影剂、正负胶片、像纸、感光材料生产过程中产生的不合格产品和过期产品 ");
        put("1010602002500298", "旅馆业(个体定税)");
        put("1010602002500364", "住宿");
        put("HB04300326106137", "除农药以外其他有机磷化合物生产、配制过程中产生的反应残余物 ");
        put("HB04300332102448", "电解铝铝液转移、精炼、合金化、铸造过程熔体表面产生的铝灰渣，以及回收铝过程产生的盐渣和二次铝灰");
        put("1011303012500001", "房地产项目预征(2%)");
        put("1011496012500003", "机动船501-1500T");
        put("1011905012500005", "仓储");
        put("1020312012500001", "灵活就业人员基本医疗保险(5%)");
        put("1011009002500002", "营业性用房出租12%");
        put("1010704992500003", "煤矸石");
        put("1010798002500008", "预制板");
        put("1010602002500222", "服务业2%");
        put("1010608002500004", "房屋出租 非住宅（代开发票）");
        put("1010400012500011", "房地产业");
        put("HB04300309200333", "采用氰化物进行黄金选矿过程中产生的含氰废水处理污泥和金精矿氰化尾渣");
        put("HB04300390025312", "使用油墨和有机溶剂进行印刷、涂布过程中产生的废物");
        put("HB04300390035435", "使用碱进行电镀阻挡层或者抗蚀层的脱除产生的废碱液");
        put("1010602002500633", "服务业－－饮食、住宿");
        put("1010602002500642", "工业—工业生产、加工、修理、修配");
        put("1010602002500672", "转让土地使用权、专利权、非专利权、商标权、著作权、商誉权、其他经济权益");
        put("1010602002500701", "粮食白酒");
        put("HB04300326111611", "乙烯氯醇法、氧化法生产环氧乙烷过程中产生的重馏分 ");
        put("HB04300327100402", "化学合成原料药生产过程中产生的废吸附剂 ");
        put("HB04300327200202", "化学药品制剂生产过程中的原料药提纯精制、再加工产生的废母液及反应基废物 ");
        put("HB04300326600205", "木材防腐化学品生产过程中产生的废水处理污泥 ");
        put("HB04300390020008", "珩磨、研磨、打磨过程产生的废矿物油及油泥 ");
        put("HB04300329100108", "橡胶生产过程中产生的废溶剂油 ");
        put("1010602002500336", "装卸搬运（代开发票）");
        put("1010602002500339", "台球、保龄球");
        put("1010602002500361", "娱乐业—音乐茶座、台球、游艺、茶楼、歌厅、舞厅");
        put("HB04300326118050", "苯酚和甲醇合成2,6-二甲基苯酚过程中产生的废催化剂");
        put("1010602002500397", "个体行医");
        put("HB04300390003036", "其他生产过程中产生的石棉废物 ");
        put("1010602002500252", "游艺、网吧");
        put("1010602002500533", "售建筑物及土地附着物");
        put("1010752279900034", "原矿");
        put("1010704992500015", "山沙");
        put("1010602002500031", "美容美发、洗脚、保健、按摩、桑拿、游艺、表演2%");
        put("1010602002500010", "娱乐业(个体定税-南岸)");
        put("1010602002500876", "广播");
        put("1010602002500048", "人力三轮车");
        put("1010602002500091", "经营所得－私房出租");
        put("1010609992500014", "财产转让所得3%");
        put("1010602002500713", "装卸（代开发票1.55%）");
        put("HB04300326102011", "苯胺生产过程中苯胺萃取工序产生的蒸馏残渣 ");
        put("HB04300326110111", "苯泵式硝化生产硝基苯过程中产生的重馏分");
        put("1010602002500396", "转让无形资产");
        put("HB04300333610323", "热镀锌过程中产生的废助镀熔（溶）剂和集（除）尘装置收集的粉尘");
        put("HB04300310900136", "石棉矿选矿过程中产生的废渣 ");
        put("HB09112400000001", "苯并(a)芘（气－海工）");
        put("1010602002500277", "文化体育业（代开发票）");
        put("1010602002500491", "美容、按摸、桑拿（2.75%）");
        put("1011306022500001", "非普通住宅（清算）");
        put("1010740419900008", "原矿");
        put("1010752279900035", "选矿");
        put("1010762059900083", "选矿");
        put("1011902012500004", "综合用途");
        put("1010702002500001", "天然气");
        put("1010798002500015", "其它普通公路0.2%");
        put("1011805021510002", "二级25元");
        put("1011801011510005", "四级15元(1.5倍)");
        put("3043313001500002", "居民代征（县城主城区）");
        put("1010602002500016", "工业、交通运输业、商业(应税所得率5%)");
        put("1011203992500001", "土地使用税等级11");
        put("1010604002500003", "建筑、安装、修缮、装饰、其它工程作业（个人）3%");
        put("1010602002500088", "服务业（非住宅出租）");
        put("1010602002500089", "服务业（旅店、饮食、旅游、仓储、、美容、美发、洗脚、保健按摩、桑拿、茶楼（馆）、制图及其他服务）");
        put("1010602002500626", "建筑劳务（代开发票）");
        put("1010602002500659", "其他租赁（征收率2%）");
        put("1010603002500001", "建筑安装业");
        put("HB04300325200611", "煤焦油分馏、精制过程中产生的焦油渣 ");
        put("HB04300390040706", "900-401-06、900-402-06、900-404-06中所列废有机溶剂分馏再生过程中产生的高沸物和釜底残渣");
        put("HB04300390021308", "废矿物油再生净化过程中产生的沉淀残渣、过滤残渣、废过滤吸附介质 ");
        put("HB04300390021608", "使用防锈油进行铸件表面防锈处理过程中产生的废防锈油 ");
        put("1010602002500313", "歌厅、舞厅、卡拉OK、台球、高尔夫球、保龄球、电子游戏、游艺、音乐茶座");
        put("1010602002500373", "歌舞厅、OK厅、夜总会、音乐茶座、洒吧(征收率2.4%)");
        put("1010602002500401", "美容、美发、洗脚、保健按摩、桑拿、洗浴1.8%");
        put("1010602002500433", "夜总会、娱乐城、音乐茶座");
        put("1010602002500461", "租赁");
        put("HB04300319300121", "使用铬鞣剂进行铬鞣、复鞣工艺产生的废水处理污泥和残渣");
        put("HB04300326104221", "铬铁矿生产铬盐过程中产生的铝泥 ");
        put("1010602002500245", "其他娱乐业");
        put("1010602002500480", "公路、水路客运运输");
        put("1010602002500530", "其它交通运输");
        put("1011302012500001", "房地产项目预征(1%)");
        put("1010740119900001", "原矿");
        put("1010740219900006", "选矿");
        put("1010762059900082", "原矿");
        put("1011905012500004", "厂房");
        put("1010602002500026", "交通运输业1.2");
        put("1010608002500001", "营业性用房出租0.7% ");
        put("1010307992509999", "其他服务业");
        put("HB04300326110411", "对硝基氯苯氨解生产对硝基苯胺过程中产生的重馏分");
        put("1010602002500056", "公路运输");
        put("1010602002500127", "医疗(零发2.35%)（征收率）");
        put("1010602002500152", "演艺厅");
        put("1010602002500695", "建筑安装有资质(零发1.61%)（征收率）");
        put("HB04300333605117", "使用氯化锌、氯化铵进行敏化处理产生的废渣和废水处理污泥 ");
        put("1010602002500414", "提供加工及修理、修配劳务 1%");
        put("1010602002500446", "交通运输业－－出租车");
        put("1010602002500450", "水陆运输、其他运输");
        put("HB04300390002632", "使用氢氟酸进行蚀刻产生的废蚀刻液 ");
        put("HB09210200000001", "总镉（钻井泥浆和钻屑－海工）");
        put("1010602002500465", "娱乐");
        put("1010761559900072", "原矿");
        put("1011801022500002", "农村居民建房");
        put("1010796012500012", "高速公路0.35%");
        put("1010796012500014", "其它普通公路0.2%");
        put("3014802001500002", "不动产业务");
        put("1010301022509999", "其它水路运输");
        put("1010602002500181", "建筑安装业（建筑、安装、修缮、装饰、其它工程作业）");
        put("1010602002500200", "其他工业生产0.9%");
        put("1010602002500624", "网吧1.5%");
        put("1010609022500006", "销售不动产（住宅）（2011年9月1日）");
        put("1010602002500733", "高尔夫球（包括高尔夫球场收入、高尔夫练习场收入、会费及练习场卡收入、住宿餐饮收入等）");
        put("1010602002500766", "房地产业");
        put("1010602002500791", "广告业（含广告代理业）");
        put("1010603002500029", "外来建安0.01");
        put("1010604002500018", "除上述服务业以外的其它劳务（单次或月累计1万元以下）（代开发票）");
        put("HB04300326101111", "表氯醇生产过程中精制塔产生的蒸馏残渣 ");
        put("HB04300384100201", "损伤性废物 ");
        put("HB04300326300404", "2,4,5-三氯苯氧乙酸生产过程中四氯苯蒸馏产生的重馏分及蒸馏残余物");
        put("HB04300390040606", "90040206 和 90040406 中所列废物再生处理过程中产生的废活性炭及其他过滤吸附介质 ");
        put("1010602002500317", "未列举的其他服务业");
        put("HB04300326117550", "苯氧化法生产顺丁烯二酸酐过程中产生的废催化剂 ");
        put("HB09110300000001", "一氧化碳（气－海工）");
        put("1010602002500246", "其它文化业");
        put("1011496012500019", "301T以上");
        put("1011801011510002", "二级25元");
        put("1011905012500001", "住房");
        put("HB09112000000006", "烟尘（独立燃烧锅炉-气）");
        put("1010798002500011", "多渣混合料基层");
        put("1010608002500005", "住宅出租（代开发票）");
        put("1010602002500889", "其他道路货物运输");
        put("1010602002500900", "其他未列明商务服务业");
        put("1011204992500002", "土地使用税等级12");
        put("1010602002500075", "美容、美发、洗脚、保健按摩、桑拿(征收率0.60%）");
        put("1010602002500135", "代理、旅游、仓储、租赁、其他服务业、经有权部门批准的互联网上网服务营业场所（网吧）");
        put("1010602002500567", "吊车、专项作业车、轮式专用机械车");
        put("1010609022500024", "房地产业（住宅二手房转让）");
        put("1010602002500680", "工业（增值税6%）（1%）");
        put("1010602002500706", "其它陆路运输");
        put("1010603002500012", "旅店业");
        put("HB04300326110911", "萘磺化生产萘酚过程中产生的重馏分 ");
        put("1010602002500344", "保险");
        put("HB04300326116350", "乙炔法生产醋酸乙烯酯过程中产生的废催化剂 ");
        put("HB01230100000002", "pH值(1-2, 12-13)（水）");
        put("HB04300332110329", "铜、锌、铅冶炼过程中烟气氯化汞法脱汞工艺产生的废甘汞");
        put("HB04300326105530", "铊及其化合物生产过程中产生的熔渣、集（除）尘装置收集的粉尘和废水处理污泥 ");
        put("HB04300332102548", "电解铝生产过程产生的炭渣");
        put("1010602002500257", "农林牧渔业");
        put("1010602002500525", "歌厅、舞厅、卡拉OK、夜总会、娱乐城");
        put("1011303012500003", "房地产项目预征(潼南35%)");
        put("1010740129900002", "原矿");
        put("1011496012500036", "汽车");
        put("1011801011510006", "三级20元(1.5倍)");
        put("101110110YH22002", "技术许可合同");
        put("1010602002500219", "旅馆业");
        put("1010602002500894", "医学研究和试验发展");
        put("1010301012500002", "公路货物运输");
        put("1010303012500003", "金融商品转让");
        put("HB04300390099949", "被所有者申报废弃的，或者未申报废弃但被非法排放、倾倒、利用、处置的，以及有关部门依法收缴或者接收且需要销毁的列入《危险化学品目录》的危险化学品（不含该目录中仅具有“加压气体”物理危险性的危险化学品）");
        put("HB04300390005429", "已禁止使用的，所有者申报废弃的，以及有关部门依法收缴或者接收且需要销毁的《关于汞的水俣公约》管控的汞和汞化合物");
        put("1010602002500560", "交通运输业-客运");
        put("1010602002500859", "美容美发服务业");
        put("1010602002500681", "旅店、茶馆、代理、租赁、仓储（1%）");
        put("1010604002500019", "建筑劳务（代开发票）");
        put("HB04300390000101", "为防治动物传染病而需要收集和处置的废物 ");
        put("HB04300333600307", "含氰热处理炉维修过程中产生的废内衬 ");
        put("1010602002500297", "公路货物运输业(个体定税)");
        put("1010602002500302", "租赁业(个体定税)");
        put("1010602002500355", "建筑安装、修缮、装饰、其它工程作业");
        put("HB04300326115550", "聚丙烯合成过程中产生的废催化剂 ");
        put("HB04300326116150", "硝基苯催化加氢法制备苯胺过程中产生的废催化剂 ");
        put("HB04300332110122", "铜火法冶炼烟气净化产生的收尘渣、压滤渣 ");
        put("HB04300336700136", "车辆制动器衬片生产过程中产生的石棉废物 ");
        put("HB04300326114038", "废腈纶高温高压水解生产聚丙烯腈-铵盐过程中产生的过滤残渣");
        put("1011496012500020", "非机动船10T以下(外)");
        put("1010740149900004", "原矿");
        put("1010752039900024", "选矿");
        put("1010762159900089", "选矿");
        put("1010762209900090", "原矿");
        put("1011496012500024", "非机动船301T以上(外)");
        put("1011496012500025", "乘人车16-35座");
        put("HB01240500000002", "餐饮业(100~300(含)平方米-水）");
        put("1010796012509999", "其他");
        put("1010798002500007", "砖混结构毛坏楼房");
        put("1010798002500013", "高速公路0.35%");
        put("1011006012500003", "独栋商品住宅1.2%");
        put("101110200YH22008", "专有技术使用权转让书据");
        put("1010602002500033", "旅店、旅游、仓储、租赁、代理、其他服务2%");
        put("1010602002500002", "复印打字(个体定税)");
        put("1010602002500006", "其它行业（个体定税）");
        put("101070800SZ01007", "农村人口集中式饮水工程");
        put("1010602002500110", "工业生产(个体定税)");
        put("1010602002500196", "商业");
        put("1010602002500813", "夜总会、歌厅、舞厅2.4%");
        put("1010602002500826", "水路货物运输业（征收率2.5%）");
        put("1010603002500042", "歌厅、舞厅、卡拉OK、夜总会、娱乐城");
        put("1010602002500343", "装卸搬运(代开发票）");
        put("1010602002500347", "建筑、安装、修缮、其他工程作业");
        put("1010602002500357", "超过1000万元至1500万元（含）的部分");
        put("HB04300326115850", "采用烷基化反应（歧化）生产苯、二甲苯过程中产生的废催化剂 ");
        put("1010602002500452", "歌厅、舞厅、卡拉ok厅、夜总会");
        put("HB04300326500329", "电石乙炔法生产氯乙烯单体过程中产生的废酸");
        put("HB04300326105734", "硫酸和亚硫酸、盐酸、氢氟酸、磷酸和亚磷酸、硝酸和亚硝酸等的生产、配制过程中产生的废酸及酸渣 ");
        put("1010602002500486", "公路货物运输");
        put("1010602002500516", "销售非住宅");
        put("1010752109900025", "原矿");
        put("1010761029900042", "原矿");
        put("1010761399900064", "原矿");
        put("1010761559900073", "选矿");
        put("1010762049900080", "原矿");
        put("HB01240500000010", "洗浴业（洗脚、洗澡（座位）-水）");
        put("1010711032500002", "西南油气田");
        put("1011802001510001", "一级35元");
        put("1010400012500001", "市外建筑安装未提供外管证及不符合条件的二级分支机构及项目部");
        put("1010400012500002", "工业(加工、修配业）2.25%");
        put("HB04300390004549", "废电路板（包括已拆除或者未拆除元器件的废弃电路板），及废电路板拆解过程产生的废弃的CPU、显卡、声卡、内存、含电解液的电容器、含金等贵金属的连接件");
        put("1010602002500083", "装饰、装潢业");
        put("1010602002500096", "网吧、保龄球、台球、及其他娱乐业");
        put("1010602002500185", "交通运输业-货运（2011年9月1日起）");
        put("1010602002500611", "建筑安装装饰1%");
        put("1010609022500003", "房产交易核定征收住房1%");
        put("1010602002500758", "旅店");
        put("1010602002500768", "服务业-设计、咨询、代理、租赁业（2011年9月1日起）");
        put("1010603002500050", "广告、设计、制图");
        put("HB04300326111511", "甲醇氯化生产甲烷氯化物过程中产生的釜底残液 ");
        put("HB04300326112611", "化学合成法生产异戊二烯过程中产生的重馏分 ");
        put("HB04300380600116", "摄影扩印服务行业产生的废显（定）影剂、胶片和废像纸");
        put("HB04300327100302", "化学合成原料药生产过程中产生的废脱色过滤介质 ");
        put("HB04300390020508", "镀锡及焊锡回收工艺产生的废矿物油 ");
        put("1010602002500294", "未列举的其他服务业1.2%");
        put("1010602002500405", "建筑装饰");
        put("HB04300332102348", "电解铝生产过程电解槽阴极内衬维修、更换产生的废渣（大修渣）");
        put("1010602002500264", "建筑安装（征收率）（1%）");
        put("1010602002500279", "代理业");
        put("1010602002500474", "服务业(设计、咨询、代理、租赁）");
        put("1010602002500543", "销售其它不动产");
        put("1010761399900065", "选矿");
        put("1011903012500001", "普通住房优惠税率");
        put("HB01240500000003", "餐饮业(300~500（含）平方米-水）");
        put("1010711012500002", "洗选煤75%");
        put("1011006012500002", "独栋商品住宅1%");
        put("1011803001510004", "四级15元");
        put("1010602002500217", "茶楼");
        put("1010602002500228", "人力运输、装卸搬运");
        put("101070800SZ01006", "冷却取用水");
        put("1010602002500060", "文化体育业－－其他");
        put("1010602002500130", "（零）茶馆");
        put("1010602002500147", "航空运输");
        put("1010602002500148", "修理加工(3%)");
        put("1010602002500163", "建筑、安装、修缮、装饰、其它工程作业");
        put("1010602002500180", "不动产拍卖(征收率3%)");
        put("1010602002500632", "服务业－－代理业、旅游业");
        put("1010602002500722", "销售不动产（非住宅）");
        put("1010603002500031", "建筑安装业1%");
        put("1010602002500368", "建筑(征收率1%)");
        put("1010602002500385", "零份发票－单位房屋出租");
        put("1010602002500423", "非住宅");
        put("1010602002500434", "射击、跑马、动力伞等");
        put("1010602002500462", "旅馆业(征收率0.60%）");
        put("HB04300331200123", "废钢电炉炼钢过程中集（除）尘装置收集的粉尘和废水处理污泥");
        put("HB04300390002833", "使用氰化物剥落金属镀层产生的废物 ");
        put("HB04300339800634", "使用硝酸进行钻孔蚀胶处理产生的废酸液 ");
        put("HB04300326108645", "石墨作阳极隔膜法生产氯气和烧碱过程中产生的废水处理污泥 ");
        put("HB09112100000001", "苯（气－海工）");
        put("1010602002500511", "游艺(征收率1.2%)");
        put("1010602002500293", "工业生产、加工、修理、修配1.2%");
        put("1010740619900012", "原矿");
        put("1011903012500003", "住房法定税率");
        put("1030199012500001", "原煤");
        put("1010796012500008", "水泥砼路面");
        put("1010206031500001", "润滑油基础油(废矿物质)");
        put("3071404001500001", "保证金");
        put("1010303012500002", "融资租赁");
        put("1010307072500002", "个人房屋出租3%");
        put("HB04300390040106", "工业生产中作为清洗剂、萃取剂、溶剂或者反应介质使用后废弃的四氯化碳、二氯甲烷、1,1-二氯乙烷、1,2-二氯乙烷、1,1,1-三氯乙烷、1,1,2-三氯乙烷、三氯乙烯、四氯乙烯，以及在使用前混合的含有一种或者多种上述卤化溶剂的混合/调和溶剂");
        put("1010602002500109", "歌舞表演(个体定税)");
        put("1010602002500138", "网吧");
        put("1010602002500139", "煤炭销售4%");
        put("1010602002500166", "歌厅、舞厅、KTV、夜总会、娱乐城");
        put("1010602002500568", "茶馆（茶楼）");
        put("1010602002500598", "茶楼(馆)(征收率1.2%)");
        put("1010602002500721", "煤炭销售3%");
        put("1010602002500784", "销售不动产（销售其他不动产）");
        put("1010602002500816", "建筑、修缮0.6%");
        put("1010603002500006", "销售不动产");
        put("HB04300326101611", "甲苯二异氰酸酯生产过程中产生的蒸馏残渣和离心分离残渣 ");
        put("HB04300326111411", "甲烷氯化生产甲烷氯化物过程中产生的重馏分 ");
        put("HB04300333600507", "金属热处理工艺盐浴槽（釜）清洗产生的含氰残渣和含氰废液");
        put("HB04300325100608", "石油炼制换热器管束清洗过程中产生的含油污泥 ");
        put("HB04300390021908", "冷冻压缩设备维护、更换和拆解过程中产生的废冷冻机油 ");
        put("1010602002500349", "网吧（取得《经营许可证》的）");
        put("HB04300326104020", "铍及其化合物生产过程中产生的熔渣、集（除）尘装置收集的粉尘和废水处理污泥 ");
        put("HB04300390002329", "生产、销售及使用过程中产生的废含汞荧光灯管及其他废含汞电光源，及废弃含汞电光源处理处置过程中产生的废荧光粉、废活性炭和废水处理污泥");
        put("HB04300390002933", "使用氰化物和双氧水进行化学抛光产生的废物 ");
        put("HB04300390030134", "使用硫酸进行酸性碳化产生的废酸液 ");
        put("1010400012500028", "煤炭采掘业（只包括烟煤和无烟煤的开采、褐煤的开采、其他煤炭开采)");
        put("1010752139900030", "选矿");
        put("1011905012500002", "商业用房");
        put("HB01240500000004", "餐饮业(500~1000（含）平方米-水）");
        put("1010704992500017", "砖厂");
        put("1010731112500002", "其他");
        put("1011802001510002", "二级25元");
        put("1011805011510003", "三级20元");
        put("3071501021500007", "一般工业固废");
        put("101110115YH22005", "多式联运合同");
        put("101110200YH22002", "土地使用权转让书据");
        put("1010602002500027", "交通货物运办理（营改增）2.5%");
        put("1010303012500004", "金融经纪业");
        put("1010602002500070", "非住宅出租");
        put("1010602002500664", "服务业-个人出租房屋-非住宅出租（2011年9月1日起）");
        put("1010609992500006", "转让无形资产（2011年9月1日起）");
        put("1010602002500707", "装饰");
        put("1010602002500815", "精煤1000%");
        put("HB04300320100305", "使用含砷、铬等无机防腐剂进行木材防腐过程中产生的废水处理污泥，以及木材防腐处理过程中产生的沾染该防腐剂的废弃木材残片 ");
        put("HB04300325100408", "石油炼制过程中溶气浮选工艺产生的浮渣 ");
        put("HB01230100000005", "pH值(4-5, 9-10)（水）");
        put("1010602002500408", "歌厅舞厅卡拉OK厅夜总会高尔夫球");
        put("1010602002500534", "其他文化体育业");
        put("1039901012500001", "工会经费（区县及市属）");
        put("1010704992500020", "砂  岩100%");
        put("3014801001500001", "保证金");
        put("3071501011500007", "一般工业固废");
        put("3043313001500009", "委托代征");
        put("1010602002500235", "建筑、安装1.02%");
        put("1010602002500237", "音乐茶座、射箭等1.8%");
        put("1010602002500099", "交通运输业");
        put("1010602002500171", "其他（征收率2%）");
        put("1010602002500593", "夜总会等提供音像设备、乐队、文艺表演并设有包房供成人消费的娱乐场所");
        put("1010602002500839", "服务业-咨询");
        put("1010602002500845", "快递服务业");
        put("1010602002500785", "批发零售业(征收率0.60%)");
        put("1010602002500825", "其他服务业（征收率1.2%）");
        put("HB04300326113611", "β-苯胺乙醇法生产靛蓝过程中产生的重馏分");
        put("HB04300390001311", "其他化工生产过程（不包括以生物质为主要原料的加工过程）中精馏、蒸馏和热解工艺产生的高沸点釜底残余物");
        put("1010602002500372", "歌舞表演(征收率1.5%)");
        put("1010602002500381", "房屋出租 非住宅（代开发票）");
        put("HB04300331400221", "铁铬合金生产过程中集（除）尘装置收集的粉尘 ");
        put("HB04300326105834", "卤素和卤素化学品生产过程中产生的废酸 ");
        put("HB04300332100948", "铅锌冶炼过程中，阴极锌熔铸产生的熔铸浮渣 ");
        put("HB09112000000001", "烟尘（气－海工）");
        put("HB09114200000001", "二甲二硫（气－海工）");
        put("1010602002500238", "加工修理");
        put("1010602002500243", "商标权");
        put("1010602002500505", "采掘业、洗选业（征收率2%）");
        put("1010400012500026", "采掘业（洗选）");
        put("1010761339900058", "原矿");
        put("1011496012500022", "非机动船51-150T(外)");
        put("1010731112500001", "长江河道砂石");
        put("3017601021500001", "一般性生产建设项目（市级）");
        put("3010201001500004", "地方农网还贷资金收入-按期申报");
        put("1010602002500207", "饮食业(个体定税)");
        put("1010602002500865", "教育服务业");
        put("1010602002500035", "个人非住房出租");
        put("HB04300325200211", "煤气净化过程氨水分离设施底部的废焦油和焦油渣");
        put("HB04300345100311", "煤气生产过程中煤气冷凝产生的废煤焦油");
        put("HB04300377200318", "具有毒性、感染性中一种或者两种危险特性的危险废物焚烧、热解等处置过程产生的飞灰、废水处理污泥和底渣（不包括生活垃圾焚烧炉协同处置感染性医疗废物产生的底渣）");
        put("1010602002500087", "制造、加工修理修配");
        put("1010602002500090", "按摩桑拿");
        put("1010602002500143", "建筑业无外经证（代开发票）");
        put("1010602002500640", "其它表演");
        put("1010602002500710", "批发零售1%");
        put("1010602002500001", "茶楼(个体定税)");
        put("HB04300333600207", "使用氰化物进行金属热处理产生的淬火废水处理污泥 ");
        put("1010602002500352", "营利性医疗机构");
        put("HB04300326118150", "糠醛脱羰制备呋喃过程中产生的废催化剂 ");
        put("HB01230100000004", "pH值(3-4, 10-11)（水）");
        put("HB09110600000001", "氟化物（气－海工）");
        put("HB04300338400123", "碱性锌锰电池、锌氧化银电池、锌空气电池生产过程中产生的废锌浆 ");
        put("HB04300338700129", "电光源用固汞及含汞电光源生产过程中产生的废活性炭和废水处理污泥");
        put("HB04300390030234", "使用硫酸进行酸蚀产生的废酸液 ");
        put("HB04300332100248", "铜火法冶炼过程中烟气处理集（除）尘装置收集的粉尘");
        put("1010602002500535", "电子游戏（无证网吧）");
        put("1011496012500018", "151-300T");
        put("1010751029900015", "原矿");
        put("1011901012500003", "住宅用途");
        put("HB01240500000014", "汽车、摩托车维修与保养业（（水枪-支）-水）");
        put("1011801021500001", "水利工程用地");
        put("1011804001510001", "一级35元");
        put("3071501021500004", "工业氮氧化物");
        put("101110200YH22003", "房屋等建筑物和构筑物所有权转让书据（不包括土地承包经营权和土地经营权转移）");
        put("1010400012500004", "采掘业（其他）");
        put("30714A0011500001", "采矿权使用费（部本级）");
        put("1010602002500115", "仓储业（征收率2%）");
        put("1010609992500009", "财产转让4%");
        put("1010602002500676", "中餐");
        put("1010602002500678", "未列明其他出租");
        put("1010602002500699", "人力三轮(征收率0.6%");
        put("1010602002500702", "(零)游船、射击、其他游艺");
        put("1010602002500704", "货运");
        put("1010602002500736", "射击、跑马、游戏机、动力伞等");
        put("1010602002500822", "代理咨询(征收率1.2%)");
        put("1010603002500010", "茶馆");
        put("HB04300326103411", "1,1,1-三氯乙烷生产过程中蒸馏产生的重馏分");
        put("HB04300390025412", "使用遮盖油、有机溶剂进行遮盖油的涂敷过程中产生的废物 ");
        put("HB04300333605717", "使用金和电镀化学品进行镀金产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300333604907", "氰化物热处理和退火作业过程中产生的残渣 ");
        put("1010602002500326", "煤炭行业销售平均单价100元（含）以下");
        put("1010602002500332", "装饰装修业(1.02%)");
        put("1010602002500365", "网吧(未取得资质)");
        put("1010602002500375", "商业销售(征收率0.6%)");
        put("1010602002500391", "其他服务.美容美发.健身.医疗.律师.建筑劳务");
        put("1010602002500426", "财产租赁所得0.9%");
        put("HB04300338400226", "镍镉电池生产过程中产生的废渣和废水处理污泥 ");
        put("1010602002500482", "公路运输、水路运输");
        put("1010752039900023", "原矿");
        put("1010762039900078", "原矿");
        put("1011903012500006", "厂房");
        put("HB01240500000015", "摄影扩印服务业（（台）-水）");
        put("1010704992500011", "页岩砖");
        put("1011006012500006", "高档住房1.2%");
        put("1010602002500907", "外来建筑安装有外出经营证明但账务核算不健全的(内资个体)");
        put("101110108YH22002", "其他金融机构借款合同");
        put("1010602002500233", "住宅");
        put("1010608002500016", "房屋出租0.05%");
        put("1010602002500873", "出租车客运");
        put("1010301012500003", "公路旅客运输");
        put("HB04300339800734", "液晶显示板或者集成电路板的生产过程中使用酸浸蚀剂进行氧化物浸蚀产生的废酸液");
        put("1010602002500062", "交通运输业－－客货运输车");
        put("1010602002500111", "建筑、安装、修缮");
        put("1010602002500618", "其他工程作业");
        put("1010609022500026", "转让非住宅3%");
        put("1010602002500698", "其他服务业(零发2%)（征收率）");
        put("1010602002500745", "销售不动产-个人住宅");
        put("1010603002500017", "代理业");
        put("1010603002500018", "美容、美发、洗脚、保健按摩、桑拿");
        put("1010604002500006", "表演—其他（征收率1%）");
        put("1010604002500016", "公路内河运输");
        put("HB04300339800116", "使用显影剂、氢氧化物、偏亚硫酸氢盐、醋酸进行胶卷显影产生的废显（定）影剂、胶片和废像纸");
        put("HB04300333606917", "使用铬酸镀铬产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300384100101", "感染性废物 ");
        put("HB04300326300304", "甲拌磷生产过程中二乙基二硫代磷酸过滤产生的残余物 ");
        put("HB04300390019908", "内燃机、汽车、轮船等集中拆解过程产生的废矿物油及油泥 ");
        put("1010602002500338", "游艇划船、攀岩、彩蛋对抗射击等");
        put("1010602002500404", "其他行业1.8%");
        put("1010602002500459", "网吧(取得资质)");
        put("HB04300332101148", "铅锌冶炼过程中，鼓风炉炼锌锌蒸气冷凝分离系统产生的鼓风炉浮渣 ");
        put("1010602002500247", "健身");
        put("1010602002500526", "旅店业0.9%");
        put("1010762039900079", "选矿");
        put("1010703022500001", "原煤2.5");
        put("1010796012500004", "建安专用");
        put("101110113YH22002", "工程设计合同");
        put("1010602002500214", "煤炭");
        put("1010602002500225", "个人房屋转让（住宅）");
        put("1010602002500884", "音像制品出租");
        put("HB04300326108545", "其他有机卤化物的生产过程中产生的不合格、淘汰、废弃的产品（不包括本名录HW06、HW39类别的危险废物）");
        put("1010602002500174", "茶楼(征收率2%)");
        put("1010609022500025", "房地产业（非住宅销售、转让）");
        put("1010602002500847", "物业管理");
        put("1010602002500703", "出租车");
        put("1010602002500761", "加工制造业0.9%");
        put("1010603002500016", "对企事业单位承包承租经营所得");
        put("HB04300326101911", "苯胺生产过程中产生的蒸馏残渣 ");
        put("HB04300326102711", "使用羧酸肼生产1,1-二甲基肼过程中产品分离产生的残渣");
        put("HB04300326112411", "醋酸丙烯酯法生产环氧氯丙烷过程中产生的重馏分 ");
        put("HB04300390025012", "使用有机溶剂、光漆进行光漆涂布、喷漆工艺过程中产生的废物 ");
        put("HB04300333606117", "使用高锰酸钾进行钻孔除胶处理产生的废渣和废水处理污泥 ");
        put("HB04300327100202", "化学合成原料药生产过程中产生的废母液及反应基废物 ");
        put("HB04300327600102", "利用生物技术生产生物化学药品、基因工程药物过程中产生的蒸馏及反应残余物 ");
        put("HB04300307100108", "石油开采和联合站贮存产生的油泥和油脚");
        put("1010602002500311", "装卸搬运");
        put("1010602002500358", "音乐茶座、酒吧");
        put("HB04300325101750", "石油炼制中采用钝镍剂进行催化裂化产生的废催化剂");
        put("1010602002500454", "溜冰场、游艺");
        put("HB09220300000001", "化学需氧量(CODcr)（生活污水－海工）");
        put("HB09220500000002", "石油类（钻井泥浆和钻屑-海工）");
        put("1010602002500244", "其它行业");
        put("1010602002500284", "洗车");
        put("1011302012500002", "房地产项目清算查账");
        put("1010701002500003", "征收率5.03%");
        put("1010711022500001", "江汉油田");
        put("3071501021500003", "工业二氧化硫");
        put("101110110YH22001", "技术开发合同");
        put("101110110YH22003", "技术咨询合同");
        put("1010602002500030", "建筑、安装、饮食、茶馆业1%");
        put("1010602002500054", "货物运输业（代开发票）");
        put("1010602002500199", "游艺（3%）");
        put("1010602002500558", "其它服务业(征收率1.5%)");
        put("1010609022500008", "个人房屋转让（非住宅）");
        put("1010609992500005", "个人财产拍卖");
        put("HB04300345100111", "煤气生产行业煤气净化过程中产生的煤焦油渣 ");
        put("HB04300326113011", "环氧乙烷直接水合生产乙二醇过程中产生的重馏分 ");
        put("HB04300327500602", "兽药生产过程中产生的废母液、反应基和培养基废物 ");
        put("1010602002500309", "装饰作业无资质(零发2%)（征收率）");
        put("1010602002500374", "美容美发、洗脚、按摩、桑拿(征收率1.5%)");
        put("HB04300339800534", "使用酸进行电解除油、酸蚀、活化前表面敏化、催化、浸亮产生的废酸液 ");
        put("HB04300390034934", "生产、销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的强酸性擦洗粉、清洁剂、污迹去除剂以及其他强酸性废酸液和酸渣");
        put("HB04300326107039", "酚及酚类化合物生产过程中产生的废母液和反应残余物 ");
        put("1010602002500512", "货物运输");
        put("1010400012500022", "采掘业");
        put("1011903012500008", "车库（车位）");
        put("3071501011500006", "火电氮氧化物");
        put("101110200YH22005", "商标专用权转让书据");
        put("1010608002500006", "个人住宅租赁");
        put("1010602002500864", "修理业");
        put("1011202992500001", "土地使用税等级11");
        put("1010301022500001", "水路货物运输");
        put("1010303022500001", "人身保险");
        put("1010400002500009", "销售不动产");
        put("1010602002500093", "零份发票－其他服务业");
        put("1010602002500607", "建筑");
        put("1010602002500646", "歌厅、舞厅、夜总会、OK厅");
        put("1010609022500014", "个人房屋转让（住宅）（征收率1%）");
        put("1010602002500715", "高尔夫球");
        put("1010602002500827", "文体其他(零发1.61%)（征收率）");
        put("1010603002500030", "建筑安装（1%）");
        put("1010603002500037", "租赁业");
        put("HB04300326101411", "邻二甲苯法生产邻苯二甲酸酐过程中产生的蒸馏残渣和轻馏分 ");
        put("HB04300333606017", "使用铬和电镀化学品进行镀黑铬产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300390000405", "销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的木材防腐化学药品");
        put("HB04300326116650", "采用碳酸二甲酯法生产甲苯二异氰酸酯过程中产生的废催化剂 ");
        put("HB04300390035635", "使用碱溶液进行碱性清洗、图形显影产生的废碱液 ");
        put("HB04300332101048", "铅锌冶炼过程中，氧化锌浸出处理产生的氧化锌浸出渣 ");
        put("HB09113100000001", "苯胺类（气－海工）");
        put("1010602002500289", "工业及修理修配");
        put("1011311002500001", "旧房转让普通住宅（核定）");
        put("1010740429900009", "原矿");
        put("1010752339900036", "原矿");
        put("1021301000000001", "驾乘服务");
        put("30714A0011500002", "采矿权使用费（非部本级）");
        put("1010602002500133", "（零）台球、保龄球");
        put("1010602002500151", "教育、体育");
        put("1010602002500585", "网吧（未取得许可）、电子游戏厅3.6%");
        put("1010602002500629", "转让土地使用权");
        put("1010609022500023", "销售不动产—住宅");
        put("1010602002500788", "饮食服务业(应税所得率)");
        put("1010603002500013", "其他文化业");
        put("HB04300390001413", "废弃的粘合剂和密封剂（不包括水基型和热熔型粘合剂和密封剂）");
        put("1010602002500306", "其他服务业（征收率2%）");
        put("1010602002500463", "销售货物(征收率0.60%）");
        put("HB04300330400122", "使用硫酸铜进行敷金属法镀铜产生的废槽液、槽渣和废水处理污泥");
        put("1010602002500265", "交通运输客、货车、两用车(征收率1.5%)");
        put("1010602002500471", "服务业（美容美发）");
        put("1010400012500029", "中介服务业");
        put("1010206071500001", "润滑油基础油(废矿物质)");
        put("1011801021510003", "三级20元");
        put("1011802001510004", "四级15元");
        put("1010602002500221", "（零）其他歌舞表演");
        put("101070800SZ02090", "其他行业");
        put("101070800SZ01010", "地源热泵（其他）");
        put("1010602002500074", "工业生产、加工、修理、修配(征收率0.60%）");
        put("1010602002500125", "文体歌舞表演(零发2%)（征收率）");
        put("1010602002500619", "茶馆");
        put("1010602002500843", "其他采矿业");
        put("1010602002500854", "旅行社服务业");
        put("1010602002500757", "其它未列举服务项目");
        put("1010602002500823", "广告（征收率1.2%）");
        put("1010603002500025", "建筑业(1%)（征收率1%）");
        put("1010602002500013", "建筑、安装、修缮、装饰、其它工程作业（个人）3%");
        put("HB04300326101711", "1,1,1-三氯乙烷生产过程中产生的蒸馏残渣");
        put("HB04300326400312", "钼酸橙颜料生产过程中产生的废水处理污泥 ");
        put("HB04300390020408", "使用轧制油、冷却剂及酸进行金属轧制产生的废矿物油 ");
        put("HB09111400000001", "碳黑尘（气－海工）");
        put("HB04300325101434", "石油炼制过程产生的废酸及酸泥 ");
        put("HB04300390035535", "使用碱进行氧化膜浸蚀产生的废碱液 ");
        put("HB04300390003746", "废弃的镍催化剂 ");
        put("HB04300332101948", "铅锌冶炼过程中，铅电解产生的阳极泥及阳极泥处理后产生的含铅废渣和废水处理污泥 ");
        put("1010602002500508", "旅游（征收率1.2%）");
        put("1011496012500012", "机动船1001-1500T(外)");
        put("1011905012500003", "办公用房");
        put("1010602002500025", "交通运输（营改增）");
        put("1010704402500004", "地下水（温泉水）2%");
        put("1010798002500004", "专用开炸药");
        put("1011006012500007", "其他普通住房");
        put("3017601031500001", "开采矿产资源（开采期间，石油天然气以外）（市级）");
        put("101110112YH22001", "加工合同");
        put("1010608002500015", "非住宅出租（标准子目）");
        put("1010307992500003", "高速公路通行费收入");
        put("1010400002500001", "公路内河运输");
        put("1010602002500102", "台球、保龄球（1%）");
        put("1010602002500128", "装饰装修（征收率1.61%）");
        put("1010602002500575", "销售货物(20000元(含)-30000元)");
        put("1010602002500608", "其他表演");
        put("1010602002500630", "桑拿、美容美发、洗脚、按摩");
        put("1010602002500666", "装璜所得（100万以下）");
        put("1010609992500002", "转让非住宅");
        put("1010609992500003", "转让无开资产");
        put("1010602002500686", "台球1.2%");
        put("1010602002500673", "歌厅、舞厅、电子游戏");
        put("1010602002500803", "出租影碟");
        put("1010603002500019", "台球");
        put("1010604002500004", "设计、咨询、代理、租赁等个人劳务（个人）1.5%");
        put("HB04300326101811", "三氯乙烯和四氯乙烯联合生产过程中产生的蒸馏残渣 ");
        put("HB04300390045113", "废覆铜板、印刷线路板、电路板破碎分选回收金属后产生的废树脂粉 ");
        put("HB04300333605517", "使用镀镍液进行镀镍产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300377200518", "固体废物焚烧处置过程中废气处理产生的废活性炭");
        put("HB04300384100301", "病理性废物 ");
        put("1010400011500001", "所得税跨地区设立的项目部预缴0.2%");
        put("HB04300390030034", "使用酸进行清洗产生的废酸液 ");
        put("HB09114000000001", "甲硫醇（气－海工）");
        put("1010602002500241", "其它服务");
        put("1010602002500492", "其他娱乐（帆船、游艇等）（2.2%）");
        put("1010400012500025", "采掘业（洗精煤）");
        put("1011306012500001", "普通住宅（清算）");
        put("3020202002500001", "汇算清缴");
        put("1011496012500027", "乘人车11-30座以下(外)");
        put("HB01240500000001", "住宿业（床位-水）");
        put("1010400012500035", "物业管理");
        put("1011898001510001", "一级35元");
        put("101110112YH22002", "定作合同");
        put("1011203992500003", "土地使用税等级13");
        put("HB04300390040406", "工业生产中作为清洗剂、萃取剂、溶剂或者反应介质使用后废弃的其他列入《危险化学品目录》的有机溶剂，以及在使用前混合的含有一种或者多种上述溶剂的混合/调和溶剂");
        put("1010602002500045", "其他服务业（代开发票1.25%）");
        put("1010602002500136", "客运35座以下");
        put("1010602002500137", "客货运输车辆");
        put("1010602002500194", "生产、修理、修配(20000元以上)");
        put("1010602002500588", "设计、咨询、代理、租赁（代开票）2%");
        put("1010602002500654", "卡拉OK歌舞厅");
        put("1010602002500657", "工业加工(征收率0.6%)");
        put("1010609992500004", "财产转让2.5%");
        put("1010602002500857", "托儿所");
        put("1010602002500731", "装卸搬运、人力三轮");
        put("1010602002500752", "交通运输业—出租车、客运、人力三轮车、装卸搬运");
        put("1010602002500832", "装卸搬运(零发1.61%)（征收率）");
        put("HB04300325201511", "焦炭生产过程中熄焦废水沉淀产生的焦粉及筛焦过程中产生的粉尘 ");
        put("HB04300325201611", "煤沥青改质过程中产生的闪蒸油 ");
        put("HB04300326100911", "苄基氯生产过程中苄基氯蒸馏产生的蒸馏残渣 ");
        put("HB04300326113111", "乙醛缩合加氢生产丁二醇过程中产生的重馏分 ");
        put("HB04300325101008", "石油炼制过程中澄清油浆槽底沉积物 ");
        put("1010602002500337", "歌舞厅、迪吧、酒吧、电子游戏");
        put("1010602002500379", "转让无形资(征收率4%)");
        put("HB04300325101650", "石油产品加氢精制过程中产生的废催化剂 ");
        put("1010602002500417", "台球、保龄球、音乐茶座（包括酒吧）帆船、游艇、碰碰船、漂流、游艇拖伞、彩弹对抗射击、攀岩、划船、游艺（如射击、狞猎、跑马、蹦级、卡丁车（赛车）、热气球、动力伞、射箭、飞镖等");
        put("1010602002500431", "货物运输业");
        put("HB04300326105429", "卤素和卤素化学品生产过程中产生的含汞硫酸钡污泥 ");
        put("1010602002500263", "农林牧渔");
        put("1010602002500468", "台球(征收率1.5%)");
        put("1010602002500489", "其他服务");
        put("1010602002500510", "网吧(征收率1.8%)");
        put("1010602002500515", "销售住宅");
        put("1010602002500291", "金融保险业");
        put("1011301002500001", "核定征收");
        put("1011303012500010", "旧房核定");
        put("1010761179900052", "原矿");
        put("1011903012500004", "商业用房");
        put("1010702002500005", "征收率4.43%");
        put("3022201001500002", "森林植被（区县级）");
        put("1010608002500008", "非住宅出租（经营项目类）");
        put("1010608002500014", "住宅出租(据实征收10%)");
        put("1010602002500036", "种植业");
        put("1010602002500902", "休闲娱乐用品设备出租");
        put("1010602002500079", "娱乐业（其他项目）");
        put("1010602002500612", "交通运输业（装卸搬运）1.5%");
        put("1010602002500735", "游艇划船、攀岩、彩弹对抗射击等");
        put("1010602002500775", "服务业（代开发票）");
        put("1010602002500812", "客运0.6%");
        put("1010603002500035", "客运35座以下");
        put("1010603002500040", "建筑，安装");
        put("1010603002500065", "市外未提供外管证、不符合认定条件的二级及以下分支机构应核定征收个人所得税");
        put("HB04300325201011", "炼焦、煤焦油加工和苯精制过程中产生的废水处理污泥（不包括废水生化处理污泥）");
        put("HB04300326103011", "四氯化碳生产过程中的重馏分 ");
        put("HB04300327200402", "化学药品制剂生产过程中产生的废吸附剂 ");
        put("HB04300390020308", "使用淬火油进行表面硬化处理产生的废矿物油 ");
        put("1010602002500320", "文化体育业1.5%");
        put("HB04300327600650", "生物药品生产过程中产生的废催化剂 ");
        put("HB04300390002733", "使用氰化物进行表面硬化、碱性除油、电解除油产生的废物 ");
        put("HB04300326107945", "乙烯溴化法生产二溴乙烯过程中产品精制产生的废吸附剂 ");
        put("HB04300332101848", "铅锌冶炼过程中，粗铅火法精炼产生的精炼渣 ");
        put("1010602002500506", "服务个人出租(零发1.85%)（征收率）");
        put("1010608012500002", "个人房屋出租（非住房）");
        put("1010751019900014", "选矿");
        put("1010752109900026", "选矿");
        put("1010752119900027", "原矿");
        put("1012596012500001", "猪");
        put("1010704402500003", "地下水（温泉水）");
        put("1010704992500014", "碎石厂");
        put("1010798002500010", "沥青砼路面");
        put("3017601021500006", "开采矿产资源（在建期间）（区县级）");
        put("101110112YH22005", "测试合同");
        put("1010602002500885", "互联网其他信息服务");
        put("1010602002500891", "其他房地产业");
        put("1010602002500898", "生物技术推广服务");
        put("1010303012509999", "其他金融业务");
        put("1010609992500013", "财产转让所得2%");
        put("101070800SZ01004", "火力发电冷却取用水（按发电量计征）");
        put("1010602002500157", "歌舞厅卡拉OK夜总会娱乐城");
        put("1010609022500009", "个人房屋转让(非住宅)3%");
        put("1010602002500840", "服务业-机动车和电器修理修配");
        put("1010602002500692", "娱乐业--台球、保龄球、游艺场");
        put("1010602002500739", "除货物运输外的其他交通运输业");
        put("1010602002500782", "销售不动产（非住宅转让）");
        put("HB04300326102411", "二硝基甲苯加氢法生产甲苯二胺过程中产品精制产生的重馏分 ");
        put("HB04300390021808", "液压设备维护、更换和拆解过程中产生的废液压油 ");
        put("1010602002500392", "歌厅、舞厅、卡拉OK厅、夜总会、娱乐城");
        put("HB04300326106738", "有机氰化物生产过程中产生的废母液和反应残余物");
        put("HB04300326108145", "芳烃及其衍生物氯代反应过程中产生的废水处理污泥 ");
        put("1010602002500242", "播映、各类培训等（代开发票）");
        put("1010602002500259", "未列举的其他服务业(征收率1.00%)");
        put("1010602002500260", "装修装饰");
        put("1010602002500272", "货物生产、修理、修配、批发和零售业月收入额在1万元以上至5万元（含）的（2011年9月1日起）");
        put("1010602002500531", "建筑安装、装饰");
        put("1010602002500550", "台球(征收率0.60%）");
        put("1011302022500008", "旧房查实");
        put("1010752389900039", "选矿");
        put("3015001002500001", "汇算清缴");
        put("1010701002500004", "征收率4.43%");
        put("1010704992500013", "碎石");
        put("3071501011500001", "化学需氧量");
        put("1010602002500863", "其他居民服务业");
        put("HB04300390000910", "含有PCBs、PCTs和PBBs的电力设备的清洗液");
        put("1010602002500042", "个人房屋转让（非住宅）");
        put("1010602002500061", "文化体育业一－其他文化体育业");
        put("1010602002500066", "旅游业（2%）");
        put("1010602002500116", "服务单位出租(零发1.35%)（征收率）");
        put("1010602002500579", "货物运输（代开发票）");
        put("1010602002500605", "(零)其它陆路运输");
        put("1010602002500623", "台球、保龄球1.5%");
        put("1010602002500628", "商誉");
        put("1010602002500852", "法律服务业");
        put("1010603002500057", "建筑业（2011年9月1日起）");
        put("HB04300326102811", "乙烯溴化法生产二溴乙烯过程中产品精制产生的蒸馏残渣 ");
        put("HB04300326111311", "乙烯直接氯化生产二氯乙烷过程中产生的重馏分 ");
        put("1010602002500386", "工业及手工业");
        put("HB04300326106438", "丙烯腈生产过程中废水汽提器塔底的残余物 ");
        put("HB04300326108245", "氯乙烷生产过程中的塔底残余物 ");
        put("HB04300332101648", "粗铅精炼过程中产生的浮渣和底渣 ");
        put("1010602002500273", "服务业（医疗服务）");
        put("1010752129900029", "选矿");
        put("1010761179900053", "选矿");
        put("1011902012500005", "办公用途");
        put("1010400012500034", "房地产业开发业");
        put("1010702002500006", "征收率4.49%");
        put("1010703042500005", "征收率2.25%");
        put("1011898001510002", "二级25元");
        put("3043313001500008", "按经营面积/量计征");
        put("1012130991500004", "工业污泥");
        put("3017601021500002", "开采矿产资源（在建期间）（市级）");
        put("3043313001500005", "按人数计征（县城主城区）");
        put("1010602002500049", "广告业0.9%");
        put("1010602002500073", "旅游");
        put("1010602002500178", "转让无形资(征收率2.5%)");
        put("1010602002500189", "广告、设计");
        put("1010602002500564", "零份发票－建筑安装");
        put("1010602002500596", "娱乐业--歌厅、舞厅、卡拉OK厅、音乐茶座、酒吧");
        put("1010609022500018", "销售不动产－－住宅");
        put("HB04300326401312", "油漆、油墨生产、配制和使用过程中产生的含颜料、油墨的废有机溶剂");
        put("HB04300333605417", "使用镍和电镀化学品进行镀镍产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300326300504", "2,4-二氯苯氧乙酸生产过程中苯酚氯化工段产生的含2,6-二氯苯酚精馏残渣");
        put("HB04300326600105", "木材防腐化学品生产过程中产生的反应残余物、废过滤介质及吸附剂");
        put("HB04300325100108", "清洗矿物油储存、输送设施过程中产生的油/水和烃/水混合物 ");
        put("1010602002500387", "典当业");
        put("HB04300338400329", "含汞电池生产过程中产生的含汞废浆层纸、含汞废锌膏、含汞废活性炭和废水处理污泥 ");
        put("HB04300330900149", "多晶硅生产过程中废弃的三氯化硅及四氯化硅");
        put("HB04300390004449", "废弃的镉镍电池、荧光粉和阴极射线管");
        put("HB09112200000001", "甲苯（气－海工）");
        put("1010602002500261", "文化");
        put("1011302012500006", "房地产项目预征(2%)");
        put("1010798002500012", "水泥稳定粹石基层");
        put("101110112YH22006", "检验合同");
        put("1010602002500204", "个人房屋转让(住宅)(个体定税)");
        put("1010602002500059", "音乐茶座(含酒吧）");
        put("1010602002500124", "网吧(零发2%)（征收率）");
        put("1010602002500158", "门市出租(0.26)");
        put("1010602002500188", "工程作业");
        put("1010602002500597", "仓储业（征收率1.2%）");
        put("1010602002500753", "服务业—饮食、医疗、广告业、设计、制图");
        put("1010602002500833", "榨菜");
        put("1010603002500021", "娱乐业");
        put("1010117011500001", "中药原药制造业（11%）");
        put("HB04300325100208", "石油初炼过程中储存设施、油-水-固态物质分离器、积水槽、沟渠及其他输送管道、污水池、雨水收集管道产生的含油污泥");
        put("HB04300327500950", "兽药生产过程中产生的废催化剂 ");
        put("HB04300390004950", "机动车和非道路移动机械尾气净化废催化剂");
        put("1010602002500382", "销售不动产（个人从事房地产开发）");
        put("1010602002500402", "劳务、其他服务（代开票）1.5%");
        put("1010602002500429", "照相、冷饮、复印0.9%");
        put("HB04300326106036", "卤素和卤素化学品生产过程中电解装置拆换产生的含石棉废物 ");
        put("1010602002500262", "采矿");
        put("1010608012500001", "个人房屋出租（住房）");
        put("1010740139900003", "原矿");
        put("1010751029900016", "选矿");
        put("1010772029900099", "选矿");
        put("1010704992500010", "芒  硝");
        put("1010706992500002", "钾  土");
        put("1010711022500002", "西南油气田");
        put("1010798002500006", "框架毛坏楼房");
        put("1011803001510002", "二级25元");
        put("1011804001510003", "三级20元");
        put("1011808001510002", "二级25元");
        put("3042401011500004", "一类区域（区县级）");
        put("1010608002500003", "土地场地出租1.5%");
        put("1010608002500011", "非住宅出租（代开发票）");
        put("1010602002500887", "其他仓储业");
        put("1010602002500893", "其他电信服务");
        put("1010303022500003", "交强险");
        put("3071480001500002", "出让收益");
        put("HB04300332100648", "硫化锌矿常压氧浸或者加压氧浸产生的硫渣（浸出渣）");
        put("1010602002500169", "装修装饰(个体)");
        put("1010602002500191", "零份发票－其他工程作业");
        put("1010602002500570", "公共交通、装卸、搬运、人力三轮、其他交通运输");
        put("1010602002500668", "建筑、安装、装饰、修缮、其它工程作业");
        put("1010609022500013", "个人房屋转让（非住宅）（征收率3%）");
        put("1010602002500705", "其他文化业");
        put("1010602002500719", "医疗");
        put("1010602002500755", "美容、美发、洗脚、桑拿");
        put("1010602002500802", "茶楼(包房)");
        put("HB04300326400512", "铬绿颜料生产过程中产生的废水处理污泥 ");
        put("1010602002500316", "个人出租住房");
        put("HB04300377200750", "烟气脱硝过程中产生的废钒钛系催化剂 ");
        put("1010602002500120", "美容美发洗脚保健按摩桑拿(征收率1.8%)");
        put("1010602002500121", "其他娱乐业（征收率1%）");
        put("HB04300326107845", "乙烯溴化法生产二溴乙烯过程中废气净化产生的废液 ");
        put("HB04300332102648", "再生铝和铝材加工过程中，废铝及铝锭重熔、精炼、合金化、铸造熔体表面产生的铝灰渣，及其回收铝过程产生的盐渣和二次铝灰");
        put("HB09113200000001", "氯苯类（气－海工）");
        put("1010602002500538", "煤矿征收");
        put("1011302022500003", "房地产项目预征别墅(5%)");
        put("1011311002500002", "旧房转让非普通住宅（核定）");
        put("1011496012500013", "机动船1501-2000T(外)");
        put("1011902012500003", "住宅用途");
        put("1030299022500001", "垃圾处置费");
        put("1010704992500021", "河  砂100%");
        put("1011801011510008", "一级35元(1.5倍)");
        put("3043313001500006", "按人数计征（建制镇）");
        put("1010602002500029", "交通旅客运输（营改增）2%");
        put("1010307072500001", "个人房屋出租5%");
        put("HB04300327500102", "使用砷或者有机砷化合物生产兽药过程中产生的废水处理污泥");
        put("HB04300390000609", "使用切削油或者切削液进行机械加工过程中产生的油/水、烃/水混合物或者乳化液");
        put("1010602002500082", "交通运输业-装卸搬运");
        put("1010602002500201", "销售服装、家具、窗帘、灯饰、鞋帽1.2%");
        put("1010602002500183", "娱乐业（网吧、音乐茶座、高尔夫球）");
        put("1010602002500551", "茶楼(征收率1.20%）");
        put("1010602002500571", "公路货物运输（代开发票）");
        put("1010609022500021", "转让旧房住宅(零发1%)（征收率）");
        put("1010603002500028", "销售不动产(非住宅)");
        put("1010607992500001", "贷款利息20%");
        put("HB04300325101311", "石油精炼过程中产生的酸焦油和其他焦油 ");
        put("HB04300332103148", "铜火法冶炼烟气净化产生的酸泥（铅滤饼）");
        put("1010602002500333", "装卸搬运1.02%");
        put("1010602002500348", "租赁业、仓储业、旅店业、旅游业、代理业");
        put("1010602002500389", "商品零售");
        put("1010602002500458", "其他交通运输");
        put("HB04300332101448", "铅锌冶炼过程中，集（除）尘装置收集的粉尘 ");
        put("HB04300332102748", "铜再生过程中集（除）尘装置收集的粉尘和湿法除尘产生的废水处理污泥");
        put("HB09112800000001", "甲醇（气－海工）");
        put("1011496012500002", "机动船151-500T");
        put("HB09112000000002", "烟尘（餐饮业100(含)平方米以下-气）");
        put("HB01240500000012", "汽车、摩托车维修与保养业（（提升机-台）-水）");
        put("1010704992500028", "页  岩");
        put("101110112YH22003", "修理合同");
        put("1010602002500218", "客运");
        put("1010602002500232", "律师事务所年收入100万元（含）以下");
        put("1010602002500901", "工程勘察活动");
        put("1010303012500001", "贷款");
        put("1010400002500003", "外来建筑安装无外出经营证明或未报验登记的");
        put("1010400002500004", "贷款利息");
        put("HB04300390001613", "使用酸、碱或者有机溶剂清洗容器设备剥离下的树脂状、粘稠杂物");
        put("1010602002500050", "个体行医0.9%");
        put("1010602002500140", "煤炭销售5%");
        put("1010602002500179", "装饰工程(征收率1%)");
        put("1010602002500631", "货车");
        put("1010602002500636", "客运车");
        put("1010602002500669", "公路、水路货物运输、装卸搬运");
        put("1010602002500732", "卡拉OK歌舞厅(包括夜总会等提供音像设备、乐队、文艺表演并设有包房供成人消费的娱乐场所)");
        put("1010602002500759", "桌球");
        put("1010603002500043", "煤炭行业销售平均单价100元（含）以下");
        put("1010603002500059", "外来建筑安装有外出经营证明但账务核算不健全的");
        put("HB09111600000001", "镉及其化合物（气－海工）");
        put("HB04300326113511", "氢氰酸法生产原甲酸三甲酯过程中产生的重馏分 ");
        put("1010602002500329", "餐饮业");
        put("HB09110900000001", "铬酸雾（气－海工）");
        put("1010602002500384", "建安所得（500万以上）");
        put("1010602002500393", "旅游业");
        put("HB04300326108746", "镍化合物生产过程中产生的反应残余物及不合格、淘汰、废弃的产品 ");
        put("HB04300332102148", "铅锌冶炼过程中，锌焙烧矿热酸浸出黄钾铁矾法、热酸浸出针铁矿法产生的铅银渣 ");
        put("1010602002500249", "超过1500万元以上的部分");
        put("1010602002500503", "文化体育业--播映、其他文化业、体育业");
        put("1011303012500005", "非住宅(车库除外)");
        put("1011303012500006", "房地产项目清算查账(10%)");
        put("1011496012500014", "机动船3001T以上(外)");
        put("1011904012500006", "车库（车位）");
        put("1010796012500006", "砖混结构毛坏楼房");
        put("1011803001510001", "一级35元");
        put("1011806001510003", "三级20元");
        put("3014802001500001", "其他补缴");
        put("3017601031500003", "开采矿产资源（开采期间，石油天然气以外）（区县级）");
        put("1010602002500871", "其他服务-婚介服务");
        put("1010301022500002", "水路旅客运输");
        put("101070800SZ01003", "水力发电");
        put("HB04300390000709", "其他工艺过程中产生的废弃的油/水、烃/水混合物或者乳化液");
        put("1010602002500077", "制造业0.9%");
        put("1010602002500142", "歌厅、舞厅、卡拉OK1.8%");
        put("1010602002500621", "代理业0.9%");
        put("1010602002500653", "其他（征收率2.5%）");
        put("1010602002500670", "铁路运输业");
        put("1010602002500682", "饮食业（1%）");
        put("1010602002500696", "客运(征收率0.6%)");
        put("1010602002500797", "文化业");
        put("1010603002500026", "客运36座以上");
        put("HB04300326101311", "萘法生产邻苯二甲酸酐过程中产生的蒸馏残渣和轻馏分 ");
        put("HB04300326102111", "二硝基甲苯加氢法生产甲苯二胺过程中干燥塔产生的反应残余物 ");
        put("HB04300327200102", "化学药品制剂生产过程中原料药提纯精制、再加工产生的蒸馏及反应残余物");
        put("HB04300325101208", "石油炼制过程中产生的废过滤介质 ");
        put("1010602002500398", "工业、修理、修配");
        put("HB04300324300131", "使用铅箔进行烤钵试金法工艺产生的废烤钵 ");
        put("HB04300390030334", "使用磷酸进行磷化产生的废酸液 ");
        put("HB04300332100548", "铅锌冶炼过程中，锌焙烧矿热酸浸出黄钾铁矾法产生的铁矾渣 ");
        put("1010602002500283", "洗染");
        put("1011303012500004", "非住宅(车库)");
        put("1010740549900011", "选矿");
        put("1010752029900022", "选矿");
        put("1010761349900060", "原矿");
        put("1010704992500008", "天青石");
        put("1010602002500215", "经批准的网吧");
        put("1010602002500880", "图书出租");
        put("1010301012509999", "其他陆路运输");
        put("1010303012500005", "农村金融机构");
        put("HB04300390000810", "含有多氯联苯（PCBs）、多氯三联苯（PCTs）和多溴联苯（PBBs）的废弃的电容器、变压器");
        put("1010602002500023", "加工、修理修配（代开票）1.5%");
        put("1010602002500565", "零份发票－转让无形资产");
        put("1010602002500639", "超过100万元至500万元（含）的部分");
        put("1010609992500007", "转让无形资产(零发2.35%)（征收率）");
        put("1010602002500837", "个体定税（零税率）");
        put("1010602002500763", "租赁(征收率2%)");
        put("1010603002500052", "煤炭行业销售平均单价200元以上");
        put("1010603002500061", "建筑业-发票代开(1%)");
        put("1010604002500017", "设计制图(征收率1.2%)");
        put("1010602002500315", "单位出租住房");
        put("1010602002500390", "水电行业");
        put("1010602002500416", "农、林、牧、渔业");
        put("1010602002500456", "无证网吧、电子游戏");
        put("HB04300332110222", "铜火法冶炼电除雾除尘产生的废水处理污泥 ");
        put("HB04300338400546", "镍氢电池生产过程中产生的废渣和废水处理污泥 ");
        put("1010602002500501", "服务业--广告业");
        put("1010602002500504", "销售货物、工业生产、加工、修理、修配业");
        put("1010602002500509", "其他货物销售（征收率0.6%）");
        put("1010602002500549", "其他文化业(征收率0.60%）");
        put("1011310002500001", "整体转让在建工程（项目）");
        put("1011496012500008", "机动船51-150T(外)");
        put("1010752159900033", "选矿");
        put("1010761069900047", "选矿");
        put("3015802102500001", "汇算清缴");
        put("1011496012500034", "农用运输车");
        put("HB01240500000009", "洗浴业（洗脚、洗澡（床位）-水）");
        put("1011802001510003", "三级20元");
        put("3017601031500002", "开采矿产资源（开采期间，石油天然气）（市级）");
        put("1010602002500037", "（零）装卸、人力三轮");
        put("1010602002500053", "饮食业、茶馆");
        put("1010602002500610", "工业生产1%");
        put("1010602002500613", "娱乐业1.5%");
        put("1010602002500862", "婚姻服务业");
        put("1010602002500723", "美容、美发0.9%");
        put("1010602002500724", "批发和零售贸易业0.9%");
        put("HB04300325201411", "焦炭生产过程中煤气净化产生的残渣和焦油 ");
        put("HB04300326101511", "苯硝化法生产硝基苯过程中产生的蒸馏残渣 ");
        put("HB04300326400912", "使用含铬、铅的稳定剂配制油墨过程中，设备清洗产生的洗涤废液和废水处理污泥 ");
        put("1010161061500999", "市道路协会代征专用征收率3%");
        put("1010602002500345", "典当");
        put("1010602002500366", "未列举的其他服务业(征收率0.60%）");
        put("HB04300326116850", "甲苯氯化水解生产邻甲酚过程中产生的废催化剂 ");
        put("1010602002500394", "汽车美容装饰");
        put("HB04300326104121", "铬铁矿生产铬盐过程中产生的铬渣 ");
        put("HB04300390030834", "使用酸进行催化（化学镀）产生的废酸液 ");
        put("HB04300322100235", "碱法制浆过程中蒸煮制浆产生的废碱液 ");
        put("1010602002500476", "娱乐业（2011年9月1日起）");
        put("1011302022500001", "房地产项目预征(2%)");
        put("1010761459900069", "选矿");
        put("1010762019900077", "选矿");
        put("1010400012500008", "煤炭及金属矿产品采掘业15%");
        put("1010400012500009", "复印业（个体定税）");
        put("HB09112000000003", "烟尘（餐饮业100~300(含)平方米-气）");
        put("HB01240500000005", "洗染服务业（干洗机（台）-水）");
        put("1010796012500009", "沥青砼路面");
        put("3017601031500004", "开采矿产资源（开采期间，石油天然气）（区县级）");
        put("101110115YH22002", "水路货物运输合同");
        put("1010602002500028", "销售货物1%");
        put("1010602002500193", "美容美发、桑拿、保健按摩");
        put("1010602002500552", "歌舞(征收率1.20%）");
        put("1010602002500817", "广告、设计、制图(个体定税)");
        put("HB04300326700315", "生产、配制和装填铅基起爆药剂过程中产生的废水处理污泥 ");
        put("1010140041500002", "固定资产减半征收2%");
        put("1010602002500318", "修缮");
        put("1010602002500350", "娱乐业－－歌厅、舞厅、卡厅、夜总会、娱乐城");
        put("1010602002500377", "销售不动产(住房征收率1%)");
        put("HB04300326500229", "氯乙烯生产过程中吸附汞产生的废活性炭 ");
        put("1010602002500251", "设计、制图、打字、复印、咨询、代理、租赁等");
        put("1010602002500466", "水电气生产和供应");
        put("1010761569900075", "选矿");
        put("1012596012500099", "其它屠宰");
        put("HB01240500000011", "洗浴业（洗脚、洗澡（衣柜）-水）");
        put("HB01240500000013", "汽车、摩托车维修与保养业（（地沟-条）-水）");
        put("1010704992500012", "煤矸砖");
        put("1010704992500022", "河  砂150%");
        put("1010711012500005", "洗选煤65%");
        put("1010711032500003", "华东分公司");
        put("1011804001510002", "二级25元");
        put("3014801001500002", "出让价款收入");
        put("101110200YH22004", "股权转让书据（不包括应缴纳证券交易印花税的）");
        put("1010602002500173", "保龄球(征收率1.5%)");
        put("1010602002500557", "旅游业(征收率1.5%)");
        put("1010602002500617", "律师事务所年收入额超过500万元到1000万元(含)的部分");
        put("1010602002500700", "饮食（征收率1.2%）");
        put("1010602002500774", "旅游、广告、仓储、其他服务");
        put("1010602002500814", "销售煤炭、家电、粮油、副食、文具0.6%");
        put("1010603002500048", "装修装饰(个人承包承租)");
        put("HB04300326100711", "乙烯法制乙醛生产过程中产生的蒸馏残渣 ");
        put("1010104991500002", "其他纺织业（17%）");
        put("HB04300326111811", "乙烯直接氯化生产三氯乙烯、四氯乙烯过程中产生的重馏分 ");
        put("HB04300326400212", "铬黄和铬橙颜料生产过程中产生的废水处理污泥 ");
        put("HB04300390040906", "900-401-06、900-402-06、900-404-06中所列废有机溶剂再生处理过程中产生的废水处理浮渣和污泥（不包括废水生化处理污泥）");
        put("HB04300326116750", "合成气合成、甲烷氧化和液化石油气氧化生产甲醇过程中产生的废催化剂 ");
        put("HB04300333610021", "使用铬酸进行阳极氧化产生的废槽液、槽渣和废水处理污泥");
        put("HB04300326108045", "芳烃及其衍生物氯代反应过程中氯气和盐酸回收工艺产生的废液和废吸附剂 ");
        put("HB09112700000001", "丙烯醛（气－海工）");
        put("1010602002500258", "饮食业(征收率0.60%）");
        put("1010602002500469", "修理修配(征收率0.6%)");
        put("1010602002500518", "(零)歌厅、舞厅、酒吧");
        put("1010602002500521", "律师事务所年收入额超过100万元到500万元(含)的部分");
        put("1010761019900041", "选矿");
        put("1010761449900066", "原矿");
        put("1010771059900097", "原矿");
        put("1011309032500002", "房地产开发-车库（核定）");
        put("1010796012500002", "公路建设(特大桥梁,特长隧道)");
        put("1010796012500013", "独立标段0.1%");
        put("1010602002500906", "委托代征0.5%");
        put("1011805011510004", "四级15元");
        put("3071501021500001", "化学需氧量");
        put("1010608002500012", "租赁（征收率1.2%）");
        put("1010602002500032", "广告、设计、制图、医疗、饲料 2%");
        put("1010602002500022", "批发、零售（代开票）1.5%");
        put("1010602002500057", "装卸、人力三轮");
        put("1010602002500126", "销售服装家具窗帘等（征收率1.2%）");
        put("1010602002500146", "建筑、安装、修缮、装饰、其它工程作业(代开发票）");
        put("1010602002500168", "个人住房转让");
        put("1010602002500748", "客货运输车、正三轮车");
        put("1010602002500764", "交通运输业（货物运输）");
        put("1010603002500002", "建筑、安装、修缮、装饰、其他工程作业");
        put("1010603002500039", "其他行业（征收率1%）");
        put("1010604002500007", "其他服务（代开发票）");
        put("1010110991500001", "其他非金属矿物制品业（11%）");
        put("HB04300326112811", "合成气加压催化生产甲醇过程中产生的重馏分 ");
        put("HB04300326300704", "溴甲烷生产过程中产生的废吸附剂、反应器产生的蒸馏残液和废水分离器产生的废物");
        put("HB04300320100105", "使用五氯酚进行木材防腐过程中产生的废水处理污泥，以及木材防腐处理过程中产生的沾染该防腐剂的废弃木材残片 ");
        put("HB04300390024908", "其他生产、销售、使用过程中产生的废矿物油及沾染矿物油的废弃包装物");
        put("1010602002500296", "建筑安装其他工程作业0.9%");
        put("HB04300326301350", "化学合成农药生产过程中产生的废催化剂");
        put("HB09110700000001", "氰化氢（气－海工）");
        put("HB09111300000001", "玻璃棉尘（气－海工）");
        put("HB04300326104627", "锑金属及粗氧化锑生产过程中产生的熔渣和集（除）尘装置收集的粉尘 ");
        put("HB04300332102848", "锌再生过程中集（除）尘装置收集的粉尘和湿法除尘产生的废水处理污泥");
        put("HB04300332102948", "铅再生过程中集（除）尘装置收集的粉尘和湿法除尘产生的废水处理污泥");
        put("1011309022500001", "房地产开发非普通住宅（核定）");
        put("1010752159900032", "原矿");
        put("1010762099900085", "选矿");
        put("1010762209900091", "选矿");
        put("1010762219900093", "选矿");
        put("1011801021510001", "一级35元");
        put("1011902012500002", "商业用途");
        put("1010711012500003", "洗选煤70%");
        put("3042401011500001", "一类区域（市级）");
        put("1010602002500230", "洗脚、保健按摩、桑拿");
        put("HB04300327600202", "利用生物技术生产生物化学药品、基因工程药物（不包括利用生物技术合成他汀类降脂药物、降糖类药物）过程中产生的废母液、反应基和培养基废物");
        put("1010602002500583", "公路内河运输业（代开票）1.5%");
        put("1010602002500805", "其它娱乐业(含网吧)");
        put("HB04300326510313", "树脂（不包括水性聚氨酯乳液、水性丙烯酸乳液、水性聚氨酯丙烯酸复合乳液）、合成乳胶、增塑剂、胶水/胶合剂生产过程中精馏、分离、精制等工序产生的釜底残液、废过滤介质和残渣");
        put("HB04300390001815", "报废机动车拆解后收集的未引爆的安全气囊 ");
        put("1010602002500308", "浴足（征收率2%）");
        put("1010602002500369", "游艺(征收率1.20%）");
        put("1010602002500428", "旅游业0.9%");
        put("HB04300390035035", "使用氢氧化钠进行煮炼过程中产生的废碱液 ");
        put("HB04300390035335", "使用碱进行清洗除蜡、碱性除油、电解除油产生的废碱液 ");
        put("HB04300326106538", "丙烯腈生产过程中乙腈蒸馏塔底的残余物 ");
        put("1010602002500274", "零份发票－销售不动产");
        put("1010602002500472", "服务业（网吧（经有权部门批准的））");
        put("1010602002500484", "其他");
        put("1010602002500502", "公路内河货物运输业、人力、装卸业");
        put("1011302012500003", "房地产项目清算核定");
        put("1011302022500006", "房地产项目清算查账(渝北60%)");
        put("1011307002500001", "旧房转让（非核定方式）");
        put("1012596012509901", "屠宰税滞纳金");
        put("1010704992500005", "河  砂50%");
        put("1010711032500001", "江汉油田");
        put("1021301000000002", "代驾服务");
        put("1010608002500010", "房屋出租（代开发票）");
        put("HB04300325201711", "固定床气化技术生产化工合成原料气、燃料油合成原料气过程中粗煤气冷凝产生的废焦油和焦油渣");
        put("1010609992500008", "转让无形资产2.5%");
        put("1010602002500711", "销售货物(增值税6%)");
        put("1010602002500750", "非住宅转让、房屋拍卖");
        put("1010602002500792", "旅店业、代理业、仓储业、旅游业、租赁业");
        put("1010603002500053", "医疗服务");
        put("HB04300325200411", "炼焦过程中焦油储存设施中的焦油渣");
        put("HB04300326102211", "二硝基甲苯加氢法生产甲苯二胺过程中产品精制产生的轻馏分 ");
        put("1010110991500002", "其他非金属矿物制品业（17%）");
        put("HB04300322100112", "废纸回收利用处理过程中产生的脱墨渣 ");
        put("HB04300377200418", "危险废物等离子体、高温熔融等处置过程产生的非玻璃态物质和飞灰 ");
        put("1010602002500353", "销售不动产非普通住宅");
        put("1010602002500378", "饮食业(征收率1.5%)");
        put("HB04300325101850", "石油产品加氢裂化过程中产生的废催化剂 ");
        put("HB01230100000006", "pH值(5-6)（水）");
        put("1010602002500380", "交通运输业（人力运输）");
        put("1010602002500399", "邮电通信业");
        put("1010602002500432", "文化体育、健身房、表演、讲座等");
        put("HB09114400000001", "二硫化碳（气－海工）");
        put("1010602002500281", "出租钢模");
        put("1011302022500009", "旧房核定");
        put("1011496012500007", "机动船50T以下(外)");
        put("1010761469900070", "原矿");
        put("1011901012500006", "其他用途");
        put("1010704402500001", "矿泉水200%");
        put("1010796012500003", "专用开炸药");
        put("1011898001510004", "四级15元");
        put("3071404001500002", "出让价款收入");
        put("1010602002500015", "批发、零售业");
        put("1011305022500001", "非普通住宅（预征）");
        put("1010307072500003", "个人房屋出租1.5%");
        put("HB04300326110311", "以苯胺、乙酸酐或者乙酰苯胺为原料生产对硝基苯胺过程中产生的重馏分");
        put("1010602002500058", "公共交通");
        put("1010602002500117", "歌厅舞厅卡拉OK夜总会娱乐城（征收率2%）");
        put("1010602002500184", "其他行业--其他");
        put("1010602002500569", "电子游戏、音乐茶座、网吧、保龄球、台球、游艺、其他娱乐业");
        put("1010602002500584", "网吧（取得许可）、游艺1.8%");
        put("HB04300326400812", "铁蓝颜料生产过程中产生的废水处理污泥 ");
        put("HB04300333606217", "使用铜和电镀化学品进行镀铜产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300327600402", "利用生物技术生产生物化学药品、基因工程药物过程中产生的废吸附剂 ");
        put("1010602002500322", "公路货物运输业");
        put("HB04300339805122", "铜板蚀刻过程中产生的废蚀刻液和废水处理污泥");
        put("HB04300323100729", "使用显影剂、汞化合物进行影像加厚（物理沉淀）以及使用显影剂、氨氯化汞进行影像加厚（氧化）产生的废液和残渣");
        put("HB04300326105329", "水银电解槽法生产氯气过程中产生的废活性炭 ");
        put("HB04300330200136", "石棉建材生产过程中产生的石棉尘、废石棉 ");
        put("1010602002500467", "经济适用房(应税所得率)");
        put("1010602002500528", "网吧以及其他娱乐业（包括只经营高尔夫练习场的其他单位和个人以及配套经营高尔夫练习场的宾馆、会所等取得的收入）");
        put("1010171901500001", "其他建筑服务（2%）");
        put("1010751049900017", "原矿");
        put("1011801022500001", "公路建设用地");
        put("1039901012500002", "工会经费（民航铁路等）");
        put("1010798002500009", "水泥砼路面");
        put("1010602002500904", "外来建筑安装有外出经营证明但账务核算不健全的");
        put("1011806001510001", "一级35元");
        put("3071501021500002", "氨氮");
        put("101110115YH22004", "铁路货物运输合同");
        put("1010602002500229", "水路运输");
        put("1010602002500231", "律师事务所年收入1000万元至1500万元（含）");
        put("1010602002500874", "电气安装");
        put("1010602002500039", "美容、美发、洗脚、保健按摩、桑拿");
        put("1010602002500064", "美容美发洗脚保健按摩桑拿");
        put("1010602002500069", "陆路运输、水路运输");
        put("1010602002500132", "销售货物");
        put("1010602002500165", "转让专利权、专利技术、著作权等");
        put("1010602002500599", "工业（征收率1.2%）");
        put("1010602002500603", "其他文化体育业（征收率1%）");
        put("1010602002500620", "台球");
        put("1010602002500651", "仓储业(征收率1.20%）");
        put("1010602002500661", "服务业（广 告 业）");
        put("1010609022500017", "销售不动产（非住宅）（2011年9月1日）");
        put("1010602002500727", "非专利技术、商誉、著作权转让");
        put("1010602002500786", "工业、交通运输业、商业(应税所得率)");
        put("1010602002500810", "农副产品加工0.9%");
        put("1010603002500032", "仓储业");
        put("1010604002500013", "播映、各类培训");
        put("HB04300326110211", "铁粉还原硝基苯生产苯胺过程中产生的重馏分 ");
        put("HB04300333605817", "使用镀铜液进行化学镀铜产生的废槽液、槽渣和废水处理污泥 ");
        put("HB04300326117650", "甲苯空气氧化生产苯甲酸过程中产生的废催化剂 ");
        put("1010602002500435", "从事商业的以缴纳增值税为主的个体纳税人(0.66%)");
        put("1010602002500442", "个体培训校");
        put("HB04300326105935", "氢氧化钙、氨水、氢氧化钠、氢氧化钾等的生产、配制中产生的废碱液、固态碱和碱渣");
        put("1011303012500009", "旧房查实");
        put("1010771029900096", "原矿");
        put("1011904012500005", "仓储");
        put("HB01240500000016", "餐饮业(100平方米以下-水）");
        put("3042401011500002", "二类区域（市级）");
        put("HB04300390000203", "销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的化学药品和生物制品，以及《医疗用毒性药品管理办法》中所列的毒性中药");
        put("HB04300332103748", "锡火法冶炼烟气净化产生的污酸处理过程产生的砷渣");
        put("1010602002500203", "代理业(个体定税)");
        put("1010602002500780", "租赁业（不含个人住房出租）");
        put("1010603002500047", "建筑(个人承包承租)");
        put("1010604002500008", "装卸搬运（代开发票）");
        put("1010606002500001", "转让无形资产2.5%");
        put("HB04300326110511", "氨化法、还原法生产邻苯二胺过程中产生的重馏分 ");
        put("1010104991500001", "其他纺织业（11%）");
        put("1010602002500430", "设计、制图、复印");
        put("1010602002500436", "金融");
        put("1010602002500445", "工业、修理修配");
        put("HB04300326105229", "水银电解槽法生产氯气过程中产生的废水处理污泥 ");
        put("HB04300333610433", "使用氰化物进行浸洗过程中产生的废液 ");
        put("HB04300390003236", "含有隔膜、热绝缘体等石棉材料的设施保养拆换及车辆制动器衬片的更换产生的石棉废物 ");
        put("1010602002500253", "公路货物运输(个体)");
        put("1010602002500483", "建筑、安装");
        put("1010602002500494", "广告、设计、制图、咨询1.8%");
        put("1010740319900007", "原矿");
        put("1010703042500001", "征收率1.8%");
        put("1010704992500018", "粘土");
        put("1010704992500027", "方解石");
        put("1011201992500001", "土地使用税等级11");
        put("3017601021500003", "取土挖沙采石烧制砖瓦瓷石灰等（市级）");
        put("1010602002500223", "文化体育、溜冰、健身房");
        put("1010608002500018", "个人房屋出租");
        put("1010602002500020", "其他行业(应税所得率10%)");
        put("1010602002500899", "应用软件开发");
        put("1010602002500065", "综合征收率5%乡村");
        put("1010602002500104", "游戏机（3%）");
        put("1010602002500581", "其他文化体育（1%）");
        put("1010602002500656", "个人住房出租（征收率0.08%)");
        put("1010602002500853", "咨询服务业");
        put("1010602002500858", "洗染服务业");
        put("1010602002500742", "建筑业－－安装");
        put("1010602002500760", "保龄球");
        put("1010602002500773", "零份发票－文化体育业");
        put("1010602002500836", "中介服务");
        put("1010603002500009", "建筑安装");
        put("1010603002500046", "外来建安0.015");
        put("1010604002500009", "劳务报酬所得");
        put("HB04300390020908", "金属、塑料的定型和物理机械表面处理过程中产生的废石蜡和润滑油 ");
        put("1010602002500354", "综合征收率7%城镇");
        put("1010602002500406", "美容、美发、桑拿、按摩、足浴");
        put("1010602002500411", "音乐茶座");
        put("HB04300331300134", "钢的精加工过程中产生的废酸性洗液 ");
        put("HB09111900000001", "锡及其化合物（气－海工）");
        put("1010602002500519", "服务业1.5%");
        put("1010602002500523", "修配");
        put("1010761369900062", "原矿");
        put("1020316012500001", "职工大额医疗互助保险（单位缴纳）（1%）");
        put("1010400012500010", "未在我国境内设立机构、场所，却有来源于我国境内的所得");
        put("HB09111100000003", "一般性粉尘（建筑施工）");
        put("1010798002500005", "建安专用");
        put("1039901012500004", "工会经费（长江航运）");
        put("1011804001510004", "四级15元");
        put("101110114YH22001", "房屋租赁合同");
        put("1010602002500882", "汽车租赁");
        put("1010303022500002", "财产保险");
        put("1010400002500006", "设计、咨询、代理、等各类劳务费(未提供核算证明)");
        put("HB04300390001010", "含有PCBs、PCTs和PBBs的电力设备中废弃的介质油、绝缘油、冷却油及导热油");
        put("1010602002500068", "游艺（含网吧）");
        put("1010602002500177", "游艺(征收率1.5%)");
        put("1010602002500645", "加工、修理修配");
        put("1010609022500019", "个人转让住房所得(住宅）");
        put("1010602002500844", "加工业");
        put("1010602002500694", "服务饮食(零发1.85)（征收率）");
        put("1010602002500740", "娱乐业－－音乐茶座、游艺");
        put("1010602002500834", "饲料");
        put("HB04300325200111", "炼焦过程中蒸氨塔残渣和洗油再生残渣");
        put("HB04300326301204", "农药生产、配制过程中产生的过期原料和废弃产品");
        put("1010602002500310", "装卸搬运(征收率1.5%)");
        put("1010602002500319", "其他服务1.5%");
        put("1010602002500359", "商业—销售货物、零商");
        put("HB04300326116950", "异丙苯催化脱氢生产α-甲基苯乙烯过程中产生的废催化剂");
        put("1010602002500395", "庆典服务");
        put("HB04300339800522", "使用酸进行铜氧化处理产生的废液和废水处理污泥");
        put("HB09113400000001", "丙烯腈（气－海工）");
        put("HB09113900000001", "三甲胺（气－海工）");
        put("1010602002500271", "其他行业（网吧、按摩、洗脚、沐浴、桑拿、电子游戏）");
        put("1010400012500019", "制造业");
        put("1010400012500005", "饮食业");
        put("1010400012500006", "制造业（石柱-税率25%）");
        put("1010704402500002", "矿泉水300%");
        put("1010798002500002", "公路建设");
        put("1011807001510004", "四级15元");
        put("3017601021500004", "排放土石渣（市级）");
        put("3043313001500001", "居民代征");
        put("101110114YH22002", "其他租赁合同");
        put("1010602002500052", "其他娱乐业（含网吧）1.5%");
        put("1010602002500155", "服务业－－广告、制图、设计");
        put("1010602002500586", "销售烟、酒、化妆品1.2%");
        put("1010602002500667", "零份发票－机动车运输");
        put("1010602002500841", "文化体育业-电影");
        put("1010602002500738", "美容美发、洗浴、保健按摩");
        put("1010602002500756", "个人医疗服务");
        put("1010602002500769", "服务业（住宅出租）");
        put("1010602002500772", "零份发票－个人住宅出租");
        put("1010602002500798", "体育业");
        put("1010603002500008", "建筑、安装、修缮、装饰、其他工程作业（代开票，企业）1%");
        put("1010603002500062", "出租车（税率1%）");
        put("1010607002500099", "其他利息、股息、红利所得");
        put("HB04300325200811", "轻油回收过程中蒸馏、澄清、洗涤工序产生的残渣 ");
        put("HB04300326101211", "异丙苯生产过程中精馏塔产生的重馏分");
        put("HB04300326103511", "三氯乙烯和四氯乙烯联合生产过程中产生的重馏分 ");
        put("1010117011500002", "中药原药制造业（17%）");
        put("HB04300326510413", "树脂（不包括水性聚氨酯乳液、水性丙烯酸乳液、水性聚氨酯丙烯酸复合乳液）、合成乳胶、增塑剂、胶水/胶合剂合成过程中产生的废水处理污泥（不包括废水生化处理污泥）");
        put("1010602002500363", "播映、各类培训等");
        put("HB09110400000001", "氯气（气－海工）");
        put("1010602002500438", "著作权");
        put("HB09112600000001", "乙醛（气－海工）");
        put("HB09112900000001", "酚类（气－海工）");
        put("1010602002500478", "建安所得（500万以下）");
        put("1010400012500021", "外来建筑安装（发票代开）0.2%");
        put("1011904012500007", "公用设施");
        put("1010307992500001", "殡葬服务");
        put("HB01240500000007", "美容美发保健业（（床位）-水）");
        put("1010704992500026", "普通粘土100%");
        put("1011005012500002", "营业性用房出租12%");
        put("1010602002500896", "文化用品设备出租");
        put("1011305012500001", "普通住宅（预征）");
        put("3071470001500001", "保证金");
        put("101070800SZ01009", "地源热泵（完全回灌）");
        put("1010602002500067", "超过500万元至1000万元（含）的部分");
        put("1010602002500084", "货物生产、修理、修配、批发和零售业月收入额在5万元以上的（2011年9月1日起）");
        put("1010602002500554", "其他（征收率3%）");
        put("1010602002500566", "邮电通信");
        put("1010602002500573", "文化、体育、表演");
        put("1010602002500601", "公路货物运输业（征收率2.5%）");
        put("1010602002500606", "（零）饮食业");
        put("1010609022500022", "转让无形资产（代开发票）");
        put("1010602002500770", "服务业（设计、咨询、代理、租赁业）");
        put("1010602002500781", "美容、美发、洗脚、按摩、桑拿");
        put("1010603002500036", "饮食业");
        put("HB04300327500702", "兽药生产过程中产生的废吸附剂 ");
        put("HB04300326300604", "乙烯基双二硫代氨基甲酸及其盐类生产过程中产生的过滤、蒸发和离心分离残余物及废水处理污泥，产品研磨和包装工序集（除）尘装置收集的粉尘和地面清扫废物");
        put("1010602002500301", "娱乐业(个体定税)");
        put("1010602002500324", "表演(歌舞)");
        put("HB04300327100650", "化学合成原料药生产过程中产生的废催化剂 ");
        put("HB09111000000001", "汞及其化合物（气－海工）");
        put("1010602002500447", "交通运输业－－装卸搬运");
        put("HB04300326106237", "除农药以外其他有机磷化合物生产、配制过程中产生的废过滤吸附介质 ");
        put("HB04300332101248", "铅锌冶炼过程中，锌精馏炉产生的锌渣 ");
        put("1010602002500500", "转让无形资产(个体定税)");
        put("1010602002500542", "未列举的其它服务业");
        put("1010602002500544", "建筑(个体)");
        put("1010400012500027", "采掘业（烟煤、焦煤）");
        put("1010400012500033", "代扣代缴境外企业所得税");
        put("1010762339900095", "选矿");
        put("3015801102500001", "汇算清缴");
        put("3021701001500002", "地方级娱乐业文化事业建设费");
        put("1011806001510002", "二级25元");
        put("3071501011500002", "氨氮");
        put("3017601021500008", "排放土石渣（区县级）");
        put("3010201001500001", "中央农网还贷资金收入-汇算清缴");
        put("1010603002500068", "客货运输车（万州-税率1.5%）");
        put("1010602002500688", "茶楼(个体定税-南岸0.45%)");
        put("1010602002500872", "信息系统集成服务");
        put("HB04300325100508", "石油炼制过程中产生的溢出废油或者乳剂");
        put("HB04300327600302", "利用生物技术生产生物化学药品、基因工程药物（不包括利用生物技术合成他汀类降脂药物、降糖类药物）过程中产生的废脱色过滤介质");
        put("1010602002500043", "建筑安装");
        put("1010602002500047", "建筑、安装、修缮、装饰、其他工程作业");
        put("1010602002500076", "代理业(征收率1.20%）");
        put("1010602002500081", "交通运输业-货运");
        put("1010602002500161", "建筑业—建筑安装、修缮、装饰、装潢、其他工程作业");
        put("1010602002500562", "服务业-美容美发、足浴、按摩、桑拿（2011年9月1日起）");
        put("1010602002500635", "工业生产加工修理修配");
        put("1010609992500010", "海外回流文物拍卖");
        put("HB04300332103329", "铅锌冶炼烟气净化产生的酸泥");
        put("1010602002500300", "医疗服务(个体定税)");
        put("1010602002500307", "销售货物—零售（征收率1.5%）");
        put("HB04300390004850", "废液体催化剂 ");
        put("HB09110500000001", "氯化氢（气－海工）");
        put("1010602002500410", "台球保龄球网吧");
        put("1010602002500419", "律师事务所年收入额超过1500万元以上的部分");
        put("HB04300339805231", "线路板制造过程中电镀铅锡合金产生的废液 ");
        put("HB04300333610534", "青铜生产过程中浸酸工序产生的废酸液 ");
        put("1010602002500285", "家政服务");
        put("1010602002500477", "歌厅、舞厅");
        put("1010602002500497", "原煤1500%");
        put("1010602002500532", "其它经济权益");
        put("1010602002500537", "建筑安装业");
        put("1010751019900013", "原矿");
        put("1010752119900028", "选矿");
        put("3016801912500001", "汇算清缴");
        put("1011903012500002", "普通住房优惠税率（90平米以下）");
        put("1011903012500010", "其它");
        put("1010702002500004", "征收率5.03%");
        put("1010704992500016", "黄砂");
        put("1010706992500004", "磷锶矿");
        put("1010796012500005", "框架毛坏楼房");
        put("3016801002500002", "居民用电");
        put("1011898001510003", "三级20元");
        put("1010602002500908", "外来建筑安装无外出经营证明或未报验登记的(内资个体)");
        put("101110200YH22006", "著作权转让书据");
        put("1010602002500213", "饮食");
        put("1010602002500008", "建筑业（个体定税）");
        put("1010602002500892", "其他道路运输辅助活动");
        put("HB04300327500302", "使用砷或者有机砷化合物生产兽药过程中产生的废脱色过滤介质及吸附剂");
        put("HB04300390000304", "销售及使用过程中产生的失效、变质、不合格、淘汰、伪劣的农药产品，以及废弃的与农药直接接触或者含有农药残余物的包装物");
        put("1010602002500097", "邮电、通信");
        put("1010602002500105", "播映、各类培训等（代开票）1.5%");
        put("1010602002500131", "个人出租营业用房");
        put("1010602002500615", "建筑安装、装饰、修缮(代开发票1.55%)");
        put("1010602002500726", "房屋出租（代开发票）");
        put("1010603002500033", "工业生产、加工、修理、修配");
        put("1010604002500011", "设计、咨询、代理、租赁等个人劳务项目（单次或月累计1万元以下）（代开发票）");
        put("HB04300325200711", "炼焦及煤焦油加工过程中的废水池残渣");
        put("HB04300326111711", "乙炔气相合成、氧氯化生产氯乙烯过程中产生的重馏分 ");
        put("HB04300390040306", "工业生产中作为清洗剂或萃取剂使用后废弃的易燃易爆有机溶剂，包括正己烷、甲苯、邻二甲苯、间二甲苯、对二甲苯、1,2,4三甲苯、乙苯、乙醇、异丙醇、乙醚、丙醚、乙酸甲酯、乙酸乙酯、乙酸丁酯、丙酸丁酯、苯酚 ");
        put("HB04300333600107", "使用氰化物进行金属热处理产生的淬火池残渣 ");
        put("1010602002500321", "饮食业、旅店业、美容、美发、洗脚、茶馆");
        put("1010602002500360", "文化体育业—歌舞表演、其他表演");
        put("HB04300326117850", "β-羟基丙腈催化加氢生产3-氨基-1-丙醇过程中产生的废催化剂");
        put("HB04300331400121", "铬铁硅合金生产过程中集（除）尘装置收集的粉尘 ");
        put("1010602002500499", "个体办学");
        put("1011309032500001", "房地产开发其他类型房地产（核定）");
        put("1010761019900040", "原矿");
        put("1010761089900049", "选矿");
        put("1010761109900050", "原矿");
        put("1011496012500028", "乘人车31座以上(外)");
        put("1011801021510004", "四级15元");
        put("1011807001510002", "二级25元");
        put("1021303000000001", "食品（物品）配送服务");
        put("3071501011500003", "工业二氧化硫");
        put("3071501021500006", "火电氮氧化物");
        put("101110108YH22001", "银行业金融机构借款合同");
        put("3022201001500001", "森林植被（省级）");
        put("1010608012500004", "非住宅出租（渝北）");
        put("1010602002500879", "其他机械与设备经营租赁");
        put("1010400002500002", "装卸搬运");
        put("1010602002500600", "工业加工修理修配（征收率1.2%）");
        put("1010602002500691", "网吧、音乐茶座、台球等");
        put("1010602002500709", "建筑安装装饰1.5%");
        put("1010602002500778", "装饰、其他工程作业");
        put("1010602002500818", "加工、修理、修配(个体定税)");
        put("1010602002500819", "律师行业");
        put("HB04300325200311", "炼焦副产品回收过程中萘精制产生的残渣");
        put("HB04300326400712", "氧化铬绿颜料生产过程中烘干产生的残渣 ");
        put("HB04300390021708", "使用工业齿轮油进行机械设备润滑过程中产生的废润滑油 ");
        put("1010140041500001", "商业(4%)");
        put("HB04300325101950", "石油产品催化重整过程中产生的废催化剂 ");
        put("1010602002500449", "销售不动产-非住宅");
        put("HB04300390030534", "使用硝酸剥落不合格镀层及挂架金属镀层产生的废酸液 ");
        put("HB04300326107139", "酚及酚类化合物生产过程中产生的废过滤吸附介质、废催化剂、精馏残余物 ");
        put("HB04300332101748", "铅锌冶炼过程中，炼铅鼓风炉产生的黄渣 ");
        put("1010602002500470", "销售不动产（建筑物、构筑物、其它土地附着物）");
        put("1010602002500539", "综合征收率5%城镇");
        put("1010602002500292", "建材生产1.2%");
        put("1011303012500008", "房地产项目清算核定");
        put("1011496012500006", "机动船10001T以上");
        put("1010762159900088", "原矿");
        put("1011901012500002", "商业用途");
        put("1011904012500001", "住房");
        put("1010711022500003", "华东分公司");
        put("1010796012500001", "公路建设");
        put("1011808001510001", "一级35元");
        put("3042401011500006", "三类区域（区县级）");
        put("HB04300333606417", "金属或者塑料表面酸（碱）洗、除油、除锈（不包括喷砂除锈）、洗涤、磷化、出光、化抛工艺产生的废腐蚀液、废洗涤液、废槽液、槽渣和废水处理污泥（不包括：铝、镁材（板）表面酸（碱）洗、粗化、硫酸阳极处理、磷酸化学抛光废水处理污泥，铝电解电容器用铝电极箔化学腐蚀、非硼酸系化成液化成废水处理污泥，铝材挤压加工模具碱洗（煲模）废水处理污泥，碳钢酸洗除锈废水处理污泥）");
        put("HB04300390040206", "工业生产中作为清洗剂、萃取剂、溶剂或者反应介质使用后废弃的有机溶剂，包括苯、苯乙烯、丁醇、丙酮、正己烷、甲苯、邻二甲苯、间二甲苯、对二甲苯、1,2,4-三甲苯、乙苯、乙醇、异丙醇、乙醚、丙醚、乙酸甲酯、乙酸乙酯、乙酸丁酯、丙酸丁酯、苯酚，以及在使用前混合的含有一种或者多种上述溶剂的混合/调和溶剂");
        put("HB04300332103548", "锡火法冶炼过程中烟气处理集（除）尘装置收集的粉尘");
        put("1010602002500078", "服务业（其他服务）");
        put("1010602002500085", "服务业-个体医疗行业月收入额5万元以上的（2011年9月1日起）");
        put("1010602002500582", "建筑业");
        put("1010602002500648", "文化体育");
        put("1010609022500001", "房产交易查实征收20%");
        put("1010602002500794", "歌厅、舞厅、卡拉OK歌舞厅（包括夜总会等提供音响设备、乐队、文艺表演并设有包房供成人消费的娱乐场所）、游戏机、高尔夫球");
        put("1010602002500800", "有形动产租赁服务");
        put("1010603002500024", "出租车（征收率1%）");
        put("HB04300325201111", "焦炭生产过程中硫铵工段煤气除酸净化产生的酸焦油");
        put("HB04300326118350", "除农药以外其他有机磷化合物生产过程中产生的废催化剂 ");
        put("HB09110100000001", "二氧化硫（气－海工）");
        put("1010602002500413", "游艺");
        put("1010602002500421", "律师事务所年收入500万元至1000万元（含）");
        put("HB04300390003337", "生产、销售及使用过程中产生的废弃磷酸酯抗燃油 ");
        put("HB04300326106838", "有机氰化物生产过程中催化、精馏和过滤工序产生的废催化剂、釜底残余物和过滤介质 ");
        put("HB04300332100748", "铅锌冶炼过程中，锌焙烧矿热酸浸出针铁矿法产生的针铁矿渣 ");
        put("HB04300332103029", "汞再生过程中集（除）尘装置收集的粉尘，汞再生工艺产生的废水处理污泥");
        put("HB04300390004749", "生产、研究、开发、教学、环境检测（监测）活动中，化学和生物实验室（不包含感染性医学实验室及医疗机构化验室）产生的含氰、氟、重金属无机废液及无机废液处理产生的残渣、残液，含矿物油、有机溶剂、甲醛有机废液，废酸、废碱，具有危险特性的残留样品，以及沾染上述物质的一次性实验用品（不包括按实验室管理要求进行清洗后的废弃的烧杯、量器、漏斗等实验室用品）、包装物（不包括按实验室管理要求进行清洗后的试剂包装物、容器）、过滤吸附介质等");
        put("HB09112300000001", "二甲苯（气－海工）");
        put("1010602002500256", "代理");
        put("1010602002500490", "歌舞厅（3%）");
        put("1010602002500522", "人力运输（代开发票1.55%）");
        put("1010400012500015", "农、林、牧、渔业");
        put("1011301002500002", "查实征收");
        put("1011801011510003", "三级20元");
        put("1011905012500008", "其它");
        put("1011006012500004", "高档住房0.5%");
        put("1011808001510004", "四级15元");
        put("3071501011500004", "工业氮氧化物");
        put("1010602002500220", "医疗服务");
        put("1010602002500018", "饮食服务业(应税所得率7%)");
        put("1010602002500881", "医疗设备经营租赁");
        put("3071470001500002", "出让收益");
        put("30714A0021500002", "探矿权使用费（非部本级）");
        put("1010602002500123", "台球（征收率1%）");
        put("1010602002500561", "交通运输业-装卸搬运（2011年9月1日起）");
        put("1010602002500578", "个人出租房屋");
        put("1010602002500671", "制造业、采掘业");
        put("1010609992500012", "销售不动产(零发2.35%)（征收率）");
        put("1010602002500746", "综合征收率7%乡村");
        put("1010602002500829", "娱乐业(零发1.46%)（征收率）");
        put("1010603002500038", "安装(个人承包承租)");
        put("HB04300326102611", "氯苯、二氯苯生产过程中的蒸馏及分馏残渣");
        put("HB04300326110811", "对苯二酚氧化生产二甲氧基苯胺过程中产生的重馏分 ");
        put("HB04300326401012", "油墨生产、配制过程中产生的废蚀刻液");
        put("1010602002500295", "建筑、安装、修缮、装饰、其他工程作业（代开票，个人）1.5%");
        put("1010602002500448", "建筑业－－建筑");
        put("HB04300326500429", "电石乙炔法生产氯乙烯单体过程中产生的废水处理污泥 ");
        put("HB04300390045229", "含汞废水处理过程中产生的废树脂、废活性炭和污泥 ");
        put("HB09199900000001", "其他大气污染物（海工）");
        put("1010602002500248", "娱乐业－－台球、保龄球");
        put("1010602002500280", "火锅、汤锅、及其他饮食业");
        put("1010602002500540", "歌舞厅、卡拉OK厅、夜总会、娱乐城、音乐茶座");
        put("1011496012500016", "11-50T");
        put("3016801012500001", "汇算清缴");
        put("1030199012500005", "洗精煤");
        put("1010701002500001", "征收率5.32%");
        put("1012130991500003", "赤泥");
        put("101110200YH22001", "土地使用权出让书据");
        put("101070800SZ01002", "水源热泵（开式）");
        put("HB04300326108445", "其他有机卤化物的生产过程（不包括卤化前的生产工段）中产生的残液、废过滤吸附介质、反应残余物、废水处理污泥（不包括环氧氯丙烷皂化液处理产生的石灰渣）、废催化剂（不包括本名录HW04、HW06、HW11、HW12、HW13、HW39类别的危险废物）");
        put("HB04300327200502", "化学药品制剂生产过程中产生的废弃的产品及原料药");
        put("1010602002500114", "水路货物运输业(个体定税)");
        put("1010602002500118", "广告业(零发2%)（征收率）");
        put("1010602002500690", "个人房屋转让(非住宅)(个体定税)");
        put("1010602002500747", "房产出租");
        put("1010602002500783", "销售不动产（住宅转让）");
        put("1010603002500058", "建筑安装、其他劳务");
        put("1010604002500002", "加工、修理修配");
        put("1010604002500005", "表演—歌舞（征收率2%）");
        put("1010607002500001", "储蓄存款利息所得");
        put("HB04300390025112", "使用油漆（不包括水性漆）、有机溶剂进行阻挡层涂敷过程中产生的废物 ");
        put("HB04300333606617", "镀层剥除过程中产生的废槽液、槽渣和废水处理污泥");
        put("HB04300307200108", "以矿物油为连续相配制钻井泥浆用于天然气开采所产生的钻井岩屑和废弃钻井泥浆");
        put("1010602002500327", "煤炭行业销售平均单价200元以上");
        put("1010602002500346", "铁路运输");
        put("1010602002500362", "批发、零售");
        put("HB04300326116050", "乙烯氧化生产环氧乙烷过程中产生的废催化剂 ");
        put("HB04300326117450", "四氯乙烷催化脱氯化氢生产三氯乙烯过程中产生的废催化剂 ");
        put("HB09110200000001", "氮氧化物（气－海工）");
        put("1010602002500383", "销售不动产（住宅（二手房））");
        put("1010602002500424", "客货运输车");
        put("HB04300326106638", "丙烯腈生产过程中乙腈精制塔底的残余物 ");
        put("HB04300390004249", "环境事件及其处理过程中产生的沾染危险化学品、危险废物的废物");
        put("1010602002500267", "加工修理业");
        put("1010602002500270", "服务业（文化体育业）");
        put("1010602002500485", "饮食业、旅店业");
        put("1010602002500290", "广告业（1.25%）");
        put("1010752389900038", "原矿");
        put("1011006012500001", "独栋商品住宅0.5%");
        put("3021701001500001", "中央级娱乐业文化事业建设费");
        put("3071501011500005", "火电二氧化硫");
        put("1010602002500021", "煤炭及金属矿产品采掘业15%");
        put("1010602002500003", "洗 车(个体定税)");
        put("1010602002500886", "其他文体设备和用品出租");
        put("HB04300390001110", "含有或者沾染PCBs、PCTs和PBBs的废弃的包装物及容器");
        put("HB04300390003949", "烟气、VOCs治理过程（不包括餐饮行业油烟治理过程）产生的废活性炭，化学原料和化学制品脱色（不包括有机合成食品添加剂脱色）、除杂、净化过程产生的废活性炭（不包括900-405-06、772-005-18、261-053-29、265-002-29、384-003-29、387-001-29类危险废物）");
        put("1010602002500038", "单位出租营业用房");
        put("1010602002500044", "客运36座以上");
        put("1010602002500134", "邮政电信业 1.5%");
        put("1010602002500717", "广告业");
        put("1010602002500720", "律师事务所年收入1500万元以上");
        put("1010602002500734", "酒吧、歌舞厅、夜总会、迪吧、卡拉OK、娱乐城");
        put("1010602002500743", "建筑业－－其他工程作业");
        put("1010602002500779", "广告业.");
        put("1010602002500790", "其他行业(应税所得率)");
        put("HB04300333605617", "使用硝酸银、碱、甲醛进行敷金属法镀银产生的废槽液、槽渣和废水处理污泥 ");
        put("1010602002500415", "提供加工及修理、修配劳务 1.5%");
        put("1010602002500457", "游乐园");
        put("HB09113000000001", "沥青烟（气－海工）");
        put("HB09113600000001", "光气（气－海工）");
        put("1010602002500286", "理发");
        put("1010602002500287", "停车场");
        put("1010602002500464", "歌厅、舞厅、KTV、夜总会、娱乐城(征收率1.20%）");
        put("1011302022500007", "房地产项目清算核定");
        put("1011903012500005", "办公用房");
        put("1010400012500007", "外来建筑安装（发票自开）0.2%");
        put("1010704992509999", "其他");
        put("101110113YH22003", "工程施工合同");
        put("101110110YH22004", "技术服务合同");
        put("1010602002500210", "货运(征收率1.5%)");
        put("1010602002500234", "仓储、租赁0.9%");
        put("1010602002500004", "饮食业(个体定税-南岸)");
        put("1010400012500032", "企业所得税0.2%");
        put("HB04300390025212", "使用油漆（不包括水性漆）、有机溶剂进行喷漆、上漆过程中过喷漆雾湿法捕集产生的漆渣、以及喷涂工位和管道清理过程产生的落地漆渣");
        put("HB04300332103648", "锡火法冶炼烟气净化产生的酸泥");
        put("1010602002500106", "装卸搬运1.2%");
        put("1010609022500011", "房地产业（住宅开发销售）");
        put("1010602002500848", "房地产中介服务业");
        put("1010602002500811", "文化体育业0.6%");
        put("1010603002500003", "其他表演");
        put("1010607992500002", "贷款利息");
        put("HB04300326102511", "甲苯二胺光气化法生产甲苯二异氰酸酯过程中溶剂回收塔产生的有机冷凝物 ");
        put("HB04300326103111", "二氯乙烯单体生产过程中蒸馏产生的重馏分 ");
        put("HB04300326103311", "1,1,1-三氯乙烷生产过程中蒸汽汽提塔产生的残余物");
        put("1010101041500001", "烟叶（17%）");
        put("HB04300326112511", "异戊烷（异戊烯）脱氢法生产异戊二烯过程中产生的重馏分 ");
        put("HB04300326116250", "以乙烯和丙烯为原料，采用茂金属催化体系生产乙丙橡胶过程中产生的废催化剂");
        put("HB04300326118250", "过氧化法生产环氧丙烷过程中产生的废催化剂 ");
        put("1010602002500451", "其他工程行业");
        put("HB04300340100129", "含汞温度计生产过程中产生的废渣 ");
        put("HB04300326401334", "硫酸法生产钛白粉（二氧化钛）过程中产生的废酸 ");
        put("HB04300333610647", "热处理工艺中产生的含钡盐浴渣 ");
        put("HB04300332100848", "铅锌冶炼过程中，锌浸出液净化产生的净化渣，包括锌粉-黄药法、砷盐法、反向锑盐法、铅锑合金锌粉法等工艺除铜、锑、镉、钴、镍等杂质过程中产生的废渣");
        put("HB09210100000001", "总汞（钻井泥浆和钻屑－海工）");
        put("1010602002500520", "工业性生产、加工、修理、修配");
        put("1010400012500018", "娱乐业");
        put("1011302012500005", "旧房核定");
        put("1011496012500001", "机动船150T以下");
        put("1010752019900019", "原矿");
        put("1010752339900037", "选矿");
        put("1010761329900057", "选矿");
        put("1010762049900081", "选矿");
        put("1030199012500003", "原煤30");
        put("3017601021500007", "取土挖沙采石烧制砖瓦瓷石灰等（区县级）");
        put("HB04300332103848", "锡再生过程中集（除）尘装置收集的粉尘和湿法除尘产生的废水处理污泥");
        put("1010602002500665", "文化体育业（2011年9月1日起）");
        put("1010602002500685", "装卸（代开票）1.5%");
        put("1010603002500056", "外来建安0.017");
        put("HB04300333605017", "使用氯化亚锡进行敏化处理产生的废渣和废水处理污泥 ");
        put("HB04300390021508", "废矿物油裂解再生过程中产生的裂解残渣 ");
        put("1010602002500323", "加工");
        put("1010602002500122", "其他娱乐业（征收率2%）");
        put("HB04300338400431", "铅蓄电池生产过程中产生的废渣、集（除）尘装置收集的粉尘和废水处理污泥 ");
        put("1010602002500266", "其它文化业(征收率1.5%)");
        put("1011496012500004", "机动船1501-3000T");
        put("1011901012500001", "工业用途");
        put("1011902012500001", "工业用途");
        put("1011903012500007", "仓储");
        put("1010602002500024", "营改征个税");
        put("1012596012509902", "屠宰税罚款");
        put("1010602002500211", "建筑安装无资质(零发2%)（征收率）");
        put("HB04300327100502", "化学合成原料药及中间体生产过程中的废弃的产品及中间体");
        put("HB04300390000509", "水压机维护、更换和拆解过程中产生的油/水、烃/水混合物或者乳化液");
        put("HB04300390002429", "生产、销售及使用过程中产生的废含汞温度计、废含汞血压计、废含汞真空表、废含汞压力计、废氧化汞电池和废汞开关，以及《关于汞的水俣公约》管控的其他废含汞非电子测量仪器");
        put("1010602002500071", "转让非住宅");
        put("1010602002500107", "装饰装修1.2%");
        put("1010602002500590", "原煤2000%");
        put("1010602002500594", "游戏机、高尔夫球");
        put("1010602002500655", "非住宅出租（征收率0.7%）");
        put("1010602002500714", "生产");
        put("1010602002500807", "一般纳税人（增值税查账征收）");
        put("1010603002500022", "销售不动产(住宅)");
        put("HB04300327100102", "化学合成原料药生产过程中产生的蒸馏及反应残余物 ");
        put("HB04300320100205", "使用杂酚油进行木材防腐过程中产生的废水处理污泥，以及木材防腐处理过程中产生的沾染该防腐剂的废弃木材残片 ");
        put("HB04300390035235", "使用碱进行清洗产生的废碱液 ");
        put("HB09113300000001", "硝基苯（气－海工）");
        put("1010602002500254", "修缮(个体)");
        put("1010602002500255", "其他工程作业(个体)");
        put("1011306032500001", "其他类型房地产（清算）");
        put("1011496012500011", "机动船501-1000T(外)");
        put("1010740549900010", "原矿");
        put("1010761069900046", "原矿");
        put("1010761319900055", "选矿");
        put("1011902012500006", "其他用途");
        put("1010704992500004", "砂  岩50%");
        put("1010711012500001", "原煤");
        put("1011807001510001", "一级35元");
        put("3010201001500002", "中央农网还贷资金收入-按期申报");
        put("1010602002500007", "旅店业（个体定税）");
        put("3071480001500001", "保证金");

    }};

    public static final Map<String, String> skcllx_dm = new HashMap<String, String>(){{
        put("1","开票");
        put("5","扣款锁定");
        put("0","正常");
        put("3","核销");
        put("6","一元以下税（费）款处理");
        put("2","抵缴");
        put("4","退税");
    }};

}
