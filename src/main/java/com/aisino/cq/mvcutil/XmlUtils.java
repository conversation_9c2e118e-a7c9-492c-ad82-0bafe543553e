package com.aisino.cq.mvcutil;

public class XmlUtils {
/*
	public static Map xmlToMap(String xml) {
		try {
			InputStream is = new ByteArrayInputStream(xml.getBytes("utf-8"));
			SAXBuilder sb = new SAXBuilder();
			Document doc = sb.build(is);
			Element root = doc.getRootElement();
			Map map = iterateElement(root);
			return map;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	private static Map iterateElement(Element root) {
		List childrenList = root.getChildren();
		Element element = null;
		Map map = new HashMap();
		List list = null;
		for (int i = 0; i < childrenList.size(); i++) {
			list = new ArrayList();
			element = (Element) childrenList.get(i);
			if (element.getChildren().size() > 0) {
				if (root.getChildren(element.getName()).size() > 1) {
					if (map.containsKey(element.getName())) {
						list = (List) map.get(element.getName());
					}
					list.add(iterateElement(element));
					map.put(element.getName(), list);
				} else {
					map.put(element.getName(), iterateElement(element));
				}
			} else {
				if (root.getChildren(element.getName()).size() > 1) {
					if (map.containsKey(element.getName())) {
						list = (List) map.get(element.getName());
					}
					list.add(element.getTextTrim());
					map.put(element.getName(), list);
				} else {
					map.put(element.getName(), element.getTextTrim());
				}
			}
		}

		return map;
	}
	public static void main(String[] arg){
		String strxml="<?xml version=\"1.0\" encoding=\"utf-8\"?>"
				+"<interface xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.chinatax.gov.cn/tirip/dataspec/interfaces.xsd\" version=\"CQHXS1.0\">"
				+"<globalInfo>"
				+"<appId>应用标识</appId>"
				+"<interfaceCode>接口编码</interfaceCode>"
				+"<businessGovOrg>税务机关代码</businessGovOrg>"
				+"<businessType>业务类型</businessType>"
				+"<dataExchangeId>业务流水号</dataExchangeId>"
				+"<passWord>通讯密码</passWord>"
				+"<requestTime>业务发起时间</requestTime>"
				+"</globalInfo>"
				+"<returnStateInfo>"
				+"<returnCode>返回代码</returnCode>"
				+"<returnMessage>base64 返回描述</returnMessage>"
				+"</returnStateInfo>"
				+"<Data>"
				+"<content>base64 请求数据内容或返回数据内容</content>"
				+"</Data>"
				+"</interface>";
		System.out.println(strxml.replaceAll("<content>.*</content>", "<content>1234</content>"));
//		System.out.println(strxml.replace("", newChar));
		System.out.println(  XmlUtils.xmlToMap(strxml));
	}*/
}
