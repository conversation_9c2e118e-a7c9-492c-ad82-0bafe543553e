package com.aisino.cq.mvcutil;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.http.HttpServlet;

public class ServletListener extends HttpServlet implements
		ServletContextListener {
	private static ServletContextEvent sce = null;
	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		// TODO Auto-generated method stub

	}

	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		sce = arg0;
	}
	
	public static String getContextParam(String paramName){
		return sce.getServletContext().getInitParameter(paramName);
	}
}
