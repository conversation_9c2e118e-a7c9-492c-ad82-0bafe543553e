package com.aisino.cq.mvcutil;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class JsonUtil {
	/**
	 * <AUTHOR>
	 * @date 2017 01 19 
	 * @function 根据字符串获取JQGRID列展示需要的信息
	 * @param str
	 * @return
	 */
	public static String getJqgridColumn(String str){
		List<Map<String,Object>> list = JSON.parseObject(str,List.class);
		String colNames="";
		String colModel ="";
		if(list !=null && list.size()>0){
			for(Map map:list){
				colNames = colNames +"'"+ map.get("colName")+"',";
				map.remove("colName");
			}
			return "{colNames : ["+colNames.substring(0, colNames.length()-1)+"],colModel :"+JSON.toJSON(list)+" }";
		}
		return "";
	}
	
	/**
	 * <AUTHOR>
	 * @date 2017 01 19 
	 * @function 根据字符串获取到处Excel需要的列头信息
	 * @param str
	 * @return
	 */
	public static List<Map<String,Object>> getExcelColumn(String str){
		
		List<Map<String,Object>> list = JSON.parseObject(str,List.class);
		String colNames="";
		String colModel ="";
		if(list !=null && list.size()>0){
			List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
			for(Map map:list){
				Map<String,Object> temp = new HashMap<String,Object>();
			
				temp.put("colName", map.get("colName"));
				temp.put("index", map.get("index"));
				temp.put("width", map.get("width"));
				
				result.add(temp);
			}
			return result;
		}
		return null;
	}
	public static void main(String[] args){
		String sql="[{colName:'设备名称',name:'MCHNAME',index:'MCHNAME',width:50,align:'center',sortable:false},"+
				"{colName:'设备编号',name:'TAMID',index:'TAMID',width:100,align:'center',sortable:false},"+
				"{colName:'税务机关代码',name:'SSSWJG_DM',index:'SSSWJG_DM',width:100,align:'center',sortable:false},"+
				"{colName:'税务机关名称',name:'SSSWJG_MC',index:'SSSWJG_MC',width:200,align:'center',sortable:false},"+
				"{colName:'运维帐号',name:'HYYWZH',index:'HYYWZH',width:80,align:'center',sortable:false},"+
				"{colName:'设备IP',name:'IP',index:'IP',width:100,align:'center',sortable:false},"+
				"{colName:'有效标志',name:'YXBZ',index:'YXBZ',width:100,align:'center',sortable:false},"+
				"{colName:'浪潮IP',name:'LCIP',index:'LCIP',width:120,align:'center',sortable:false},"+
				"{colName:'浪潮mac',name:'MAC_1',index:'MAC_1',width:120,align:'center',sortable:false},"+
				"{colName:'浪潮ID',name:'TSMID',index:'TSMID',width:100,align:'center',sorttype:'integer'}]";
		System.out.println(JsonUtil.getJqgridColumn(sql));
	}
}
