package com.aisino.cq.mvcutil;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContextsUtil implements ApplicationContextAware {
	private static ApplicationContext ctx;

	@Override
	public void setApplicationContext(ApplicationContext arg0)
			throws BeansException {
		ctx = arg0;
		System.out.println("Spring上下文初始化成功！");
	}
	public static Object getBean(String id){
		if(ctx == null){
			throw new NullPointerException("ApplicationContext is null");
		}
		return ctx.getBean(id);
	}
}
