package com.aisino.cq.mvcutil;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;

public class SpringUtil
{
	public static ApplicationContext ctx;

	public static void setApplicationContext(ApplicationContext applicationContext) throws BeansException
	{
		ctx=applicationContext;
	}

	public static ApplicationContext getApplicationContext(){
		return ctx;
	}

	public static Object getBean(String name){
		return getApplicationContext().getBean(name);
	}

	public static <T> T getBean(Class<T> clazz){
		return getApplicationContext().getBean(clazz);
	}

}
