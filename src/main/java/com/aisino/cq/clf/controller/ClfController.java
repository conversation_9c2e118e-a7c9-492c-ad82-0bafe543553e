package com.aisino.cq.clf.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.pdf.ImageToPdfService;
import com.aisino.cq.utlis.EncodedTools;
import com.aisino.cq.utlis.ItextUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.aisino.cq.clf.util.RequestUtil.callFile;

@RestController
public class ClfController {
    private static final Logger log = LoggerFactory.getLogger(ClfController.class);
    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Resource
    private ClfQyController clfQyController;
    @Autowired
    private ImageToPdfService imageToPdfService;

    //档案查询sql序号
    private static final String daQuerySqlxh = "30000057";
    //档案新增sql序号
    private static final String daInsertSqlxh = "30000055";
    //档案更新sql序号
    private static final String daUpdateSqlxh = "30000059";
    //明细新增sql序号
    private static final String mxInsertSqlxh = "30000056";
    //明细更新sql序号
    private static final String mxUpdateSqlxh = "30000060";
    //明细查询sql序号
    private static final String mxQuerySqlxh = "30000058";
    //明细查询数量sql序号
    private static final String mxQueryCountSqlxh = "30000098";
    private static final String ywlx = "clfgr";
    @Value("${filePath}")
    private String filePath;

    @RequestMapping("isAlive")
    @ResponseBody
    public Object isAlive(HttpServletRequest request){
        CommonResult rtn = new CommonResult();
        rtn.setSuccess("");
        return rtn;
    }

    @RequestMapping("getLoginUser")
    @ResponseBody
    public Object getLoginUser(HttpServletRequest request){
        return request.getSession().getAttribute("usercode");
    }

    @RequestMapping("getLoginUserName")
    @ResponseBody
    public Object getLoginUserName(HttpServletRequest request){
        return request.getSession().getAttribute("username");
    }

    /**
     * function 获取列表信息
     */
    @RequestMapping("getList")
    @ResponseBody
    public Object getList(HttpServletRequest request){
        log.info("获取列表信息");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String buyerPhone = param.get("buyerPhone");
        String sellerPhone = param.get("sellerPhone");
        String buyerName = param.get("buyerName");
        String sellerName = param.get("sellerName");
        String realEstateUnitNum = param.get("realEstateUnitNum");
        String acceptanceNum = param.get("acceptanceNum");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"buyerPhone",buyerPhone,"string");
        esbService.addParam(paramList,"phone",sellerPhone,"string");
        esbService.addParam(paramList,"name",buyerName,"string");
        esbService.addParam(paramList,"applicant",sellerName,"string");
        esbService.addParam(paramList,"realEstateUnitNum",realEstateUnitNum,"string");
        esbService.addParam(paramList,"acceptanceNum",acceptanceNum,"string");
        return esbService.queryEsb("30000008",pageNo, pageSize,"0","true",paramList);
    }

    @RequestMapping("fileList")
    @ResponseBody
    public Object fileList(String reciid){
        /*List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("reciid",reciid,"string"));
        return esbService.queryEsb("30000017","1", "10","0","false",paramList);*/
        JSONObject ywsj = new JSONObject();
        ywsj.put("ywbh",reciid);
        ywsj.put("ywlx",ywlx);
        return callFile("flzllb",ywsj);
    }

    /**
     * function 文件下载
     */
    @RequestMapping("download")
    @ResponseBody
    public void download(String id,String ywbh,HttpServletResponse response){
        log.info("文件下载"+id);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",id);
            ywsj.put("ywlx",ywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段（字符串）
                JSONArray ja = (JSONArray) result.getData();
                if(ja.size()>0) {
                    JSONObject jsonObject = ja.getJSONObject(0);
                    String fileName = jsonObject.getString("wjm");
                    String wjurl = jsonObject.getString("wjurl");
                    String downloadName = new String(fileName.getBytes("gb2312"),"ISO8859-1");
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-Disposition","attachment;filename=\""+downloadName);
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                         byte[] b = IoUtil.readBytes(inputStream);
                         out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 文件预览 已经没用了
     */
    @RequestMapping("previewqk")
    @ResponseBody
    public Object previewqk(HttpServletRequest request,HttpServletResponse response){
        CommonResult result = new CommonResult("00","");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            String id = param.get("id");
            String path = param.get("path");
            if(StringUtils.isEmpty(path)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("id",id,"string"));
                result = esbService.queryEsb("30000017","1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        path = jo.getString("path");
                    }
                }
            }
            if(result.isSuccess()){
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject jo = ja.getJSONObject(0);
                    String file = FileUtils.readFileToString(new File(filePath+File.separator+path+File.separator+id));
                    jo.put("content",file);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }

    /**
     * function 文件预览
     */
    @RequestMapping("preview")
    @ResponseBody
    public void preview(String id,String ywbh,HttpServletResponse response){
        log.info("文件预览id: {},ywbh: {}",id,ywbh);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",id);
            ywsj.put("ywlx",ywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String wjlx = obj.getString("wjlx");
                    String wjurl = obj.getString("wjurl");
                    if("pdf".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                    }else if("png".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_PNG_VALUE);
                    }else if("jpg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("jpeg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("gif".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_GIF_VALUE);
                    }
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                         byte[] b = IoUtil.readBytes(inputStream);
                         out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 删除附件（删除本地文件及已归档文件）
     */
    @RequestMapping("real/estate-file/delete")
    @ResponseBody
    public Object delete(String id,String ywbh,HttpServletRequest request){
        log.info("删除附件");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",id);
            ywsj.put("ywlx",ywlx);
            result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String gdzt = obj.getString("gdzt");
                    String zldjxh = obj.getString("zldjxh");
                    String gldh = obj.getString("gldh");
                    if("已归档".equals(gdzt)){
                        log.info("当前资料已归档---删除归档信息----");
                        result = dzzlkService.zlyc(gldh,zldjxh);
                        paramList.add(esbService.addParam("gdzt","已删除","string"));
                    }
                    if(result.isSuccess()){
                        //调用附件删除接口，也将归档附件删掉了，现在是同一个附件
                        result = callFile("flzlsc",ywsj);
                        //先查目前明细表数据，然后更新本地档案系统主表附件数量
                        if(result.isSuccess()){
                            ywsj.clear();
                            ywsj.put("ywbh",ywbh);
                            ywsj.put("ywlx",ywlx);
                            result = callFile("flzlsl", ywsj);
                            if(result.isSuccess()){
                                // 2. 获取data字段（字符串）
                                String sl = (String) result.getData();
                                //先查询主表是否已有该归档数据
                                paramList.clear();
                                paramList.add(esbService.addParam("ywid",ywbh,"string"));
                                result = esbService.queryEsb(daQuerySqlxh,"1", "10","0","false",paramList);
                                if(result.isSuccess()){
                                    JSONArray data = (JSONArray) result.getData();
                                    if(data.size() == 1){
                                        JSONObject jsonObject = data.getJSONObject(0);
                                        String dazbid = jsonObject.getString("uuid");
                                        //更新档案主表附件数量
                                        paramList.clear();
                                        paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                                        paramList.add(esbService.addParam("fjsl",sl,"string"));
                                        paramList.add(esbService.addParam("uuid",dazbid,"string"));
                                        result = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail(e.getMessage());
        }
        return result;
    }

    /**
     * function 房管材料 已经没用了
     */
    @RequestMapping("/real/estate-rcmt/get")
    @ResponseBody
    public Object fgcl(String id){
        log.info("房管材料"+id);
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("reciid",id,"string"));
        //主表详情
        CommonResult rt = esbService.queryEsb("30000018","1", "10","0","false",paramList);
        if(rt.isSuccess()){
            JSONArray ja = (JSONArray)rt.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                //附件列表
                paramList.clear();
                paramList.add(esbService.addParam("ownership","'3'","int"));
                paramList.add(esbService.addParam("reciid",id,"string"));
                CommonResult rtFile = esbService.queryEsb("30000017","1", "10","0","false",paramList);
                if(rtFile.isSuccess()){
                    jo.put("fileList",rtFile.getData());
                }
                result.setSuccess(jo);
            }else{
                result.setFail("无数据");
            }
        }
        return result;
    }

    /**
     * function 详情
     */
    @RequestMapping("/real/estate-collection-info/sellerGet")
    @ResponseBody
    public Object sellerGet(String id){
        log.info("详情"+id);
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("reciid",id,"string"));
        paramList.add(esbService.addParam("id",id,"string"));
        //主表详情
        CommonResult rt = esbService.queryEsb("30000009","1", "10","0","false",paramList);
        if(rt.isSuccess()){
            JSONArray ja = (JSONArray)rt.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                //卖方信息列表
                CommonResult rt1 = esbService.queryEsb("30000010","1", "10","0","false",paramList);
                if(rt1.isSuccess()){
                    jo.put("sellerInfo",rt1.getData());
                }
                //买方信息列表
                CommonResult rt2 = esbService.queryEsb("30000011","1", "10","0","false",paramList);
                if(rt2.isSuccess()){
                    jo.put("buyerInfo",rt2.getData());
                    //查询家庭成员列表
                    JSONArray buyerInfo = (JSONArray)rt2.getData();
                    if(buyerInfo.size()>0){
                        for (int i=0;i<buyerInfo.size();i++){
                            JSONObject buyer = buyerInfo.getJSONObject(i);
                            paramList.add(esbService.addParam("buyerId",buyer.getString("id"),"string"));
                            CommonResult rt3 = esbService.queryEsb("61000455","1", "10","0","false",paramList);
                            if(rt3.isSuccess()){
                                buyer.put("jtcyList",rt3.getData());
                            }
                        }
                    }
                }
                //个人住宅详情
                CommonResult rt3 = esbService.queryEsb("30000012","1", "10","0","false",paramList);
                if(rt3.isSuccess()){
                    JSONArray ja3 = (JSONArray)rt3.getData();
                    if(ja3.size()>0){
                        jo.put("houseInfo",ja3.getJSONObject(0));
                    }
                }
                //商品用房详情
                CommonResult rt4 = esbService.queryEsb("30000013","1", "10","0","false",paramList);
                if(rt4.isSuccess()){
                    JSONArray ja4 = (JSONArray)rt4.getData();
                    if(ja4.size()>0){
                        jo.put("businessInfo",ja4.getJSONObject(0));
                    }
                }
                //办公用房详情
                CommonResult rt5 = esbService.queryEsb("30000014","1", "10","0","false",paramList);
                if(rt5.isSuccess()){
                    JSONArray ja5 = (JSONArray)rt5.getData();
                    if(ja5.size()>0){
                        jo.put("officeInfo",ja5.getJSONObject(0));
                    }
                }
                //公寓详情
                CommonResult rt6 = esbService.queryEsb("30000015","1", "10","0","false",paramList);
                if(rt6.isSuccess()){
                    JSONArray ja6 = (JSONArray)rt6.getData();
                    if(ja6.size()>0){
                        jo.put("parkingInfo",ja6.getJSONObject(0));
                    }

                }
                //停车位详情
                CommonResult rt7 = esbService.queryEsb("30000016","1", "10","0","false",paramList);
                if(rt7.isSuccess()){
                    JSONArray ja7 = (JSONArray)rt7.getData();
                    if(ja7.size()>0){
                        jo.put("apartmentInfo",ja7.getJSONObject(0));
                    }
                }
                //附件列表
                paramList.clear();
                paramList.add(esbService.addParam("reciid",id,"string"));
                //paramList.add(esbService.addParam("ownership","'1','2'","int"));
//                CommonResult rt8 = esbService.queryEsb("30000017","1", "100","0","false",paramList);
//                if(rt8.isSuccess()){
//                    jo.put("fileList",rt8.getData());
//                }

                //房管信息
                CommonResult rt9 = esbService.queryEsb("30000018","1", "100","0","false",paramList);
                if(rt9.isSuccess()){
                    JSONArray ja9 = (JSONArray)rt9.getData();
                    if(ja9.size()>0){
                        jo.put("rcmt",ja9.getJSONObject(0));
                    }
                }
                result.setSuccess(jo);
            }
        }
        return result;
    }

    /**
     *function 受理
     */
    @RequestMapping("/real/estate-file/shouli")
    @ResponseBody
    public Object shouli(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        CommonResult rtn;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("受理："+param);
        String id = param.get("id");
        //先获取当前状态
        String applyStatus = "";
        String oldCsrybm = "";
        String oldFsrybm = "";
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"id",id,"string");
        rtn = esbService.queryEsb("30000009","1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                applyStatus = jo.getString("applyStatus");
                oldCsrybm = jo.getString("csrybm");
                oldFsrybm = jo.getString("fsrybm");
            }
        }
        //清空参数
        paramList.clear();
        if("税务初审通过".equals(applyStatus) || "复审反签派".equals(applyStatus)){
            if(StringUtils.isNotBlank(oldFsrybm)){
                rtn.setFail("该交易已在复审受理中！");
                return rtn;
            }
            //复审受理
            esbService.addParam(paramList,"applyStatus","税务复审","string");
            esbService.addParam(paramList,"fsrybm",userCode,"string");
            esbService.addParam(paramList,"fsrymc",userName,"string");
        }else if("房管中心已受理".equals(applyStatus) || "初审反签派".equals(applyStatus)){
            if(StringUtils.isNotBlank(oldCsrybm)){
                rtn.setFail("该交易已在初审受理中！");
                return rtn;
            }
            //初审受理
            esbService.addParam(paramList,"applyStatus","税务初审","string");
            esbService.addParam(paramList,"csrybm",userCode,"string");
            esbService.addParam(paramList,"csrymc",userName,"string");
        }else {
            rtn.setFail("当前状态为"+applyStatus+",无法受理！");
            return rtn;
        }
        esbService.addParam(paramList,"id",id,"string");
        esbService.addParam(paramList,"updater",userCode,"string");
        esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb("30000019","1", "10","0","false",paramList);
        return rtn;
    }

    /**
     *function 反签派
     */
    @RequestMapping("/real/estate-file/fqp")
    @ResponseBody
    public Object fqp(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("反签派："+param);
        String id = param.get("id");
        //先获取当前状态、当前初审人员、当前复审人员
        String applyStatus = "";
        String oldCsrybm = "";
        String oldFsrybm = "";
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"id",id,"string");
        rtn = esbService.queryEsb("30000009","1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                applyStatus = jo.getString("applyStatus");
                oldCsrybm = jo.getString("csrybm");
                oldFsrybm = jo.getString("fsrybm");
            }
        }
        //清空参数
        paramList.clear();
        if("税务初审".equals(applyStatus)){
            if(!StringUtils.equals(oldCsrybm,userCode)){
                rtn.setFail("当前用户不是该交易的初审人员，无法反签派！");
                return rtn;
            }
            //初审反签派
            esbService.addParam(paramList,"applyStatus","初审反签派","string");
            esbService.addParam(paramList,"csrybm","","string");
            esbService.addParam(paramList,"csrymc","","string");
        }else if("税务复审".equals(applyStatus)){
            if(!StringUtils.equals(oldFsrybm,userCode)){
                rtn.setFail("当前用户不是该交易的复审人员，无法反签派！");
                return rtn;
            }
            //复审反签派
            esbService.addParam(paramList,"applyStatus","复审反签派","string");
            esbService.addParam(paramList,"fsrybm","","string");
            esbService.addParam(paramList,"fsrymc","","string");
        }else {
            rtn.setFail("当前状态为"+applyStatus+",无法反签派！");
            return rtn;
        }
        esbService.addParam(paramList,"id",id,"string");
        esbService.addParam(paramList,"updater",userCode,"string");
        esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb("30000019","1", "10","0","false",paramList);
        return rtn;
    }

    /**
     *function 审批驳回、终止
     */
    @RequestMapping("/real/estate-file/reject")
    @ResponseBody
    public Object reject(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("审批退回/中止："+param);
        String id = param.get("id");
        String rejectReason = param.get("rejectReason");
        //退回、中止
        String type = param.get("type");
        //退回目标（如果有该值，代表是初审退回；如果没有该值，代表是复审退回），包含卖方、买方、房管（逗号隔开）
        String rejectTarget = param.get("rejectTarget");
        //需修改模块
        String xxgmk = param.get("xxgmk");
        //需重签文件
        String xcqwj = param.get("xcqwj");

        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"id",id,"string");
        esbService.addParam(paramList,"applyRemark",rejectReason,"string");
        esbService.addParam(paramList,"updater",userCode,"string");
        esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        if(StringUtils.isBlank(rejectTarget)){
            //代表是复审退回
            esbService.addParam(paramList,"applyStatus","退回税务初审","string");
        }else {
            //代表是初审退回/中止
            if(StringUtils.equals(type,"中止")){
                esbService.addParam(paramList,"applyStatus","已中止","string");
            }else {
                //初审退回
                esbService.addParam(paramList,"applyStatus","已退回","string");
                esbService.addParam(paramList,"rejectTarget",rejectTarget,"string");
                esbService.addParam(paramList,"xxgmk",xxgmk,"string");
                esbService.addParam(paramList,"xcqwj",xcqwj,"string");
            }
        }
        return esbService.queryEsb("30000019","1", "10","0","false",paramList);
    }

    /**
     *function 审批通过
     */
    @RequestMapping("/real/estate-file/over")
    @ResponseBody
    public Object over(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("审批通过："+param);
        String id = param.get("id");
        //先获取当前状态
        String applyStatus = "";
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"id",id,"string");
        rtn = esbService.queryEsb("30000009","1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                applyStatus = jo.getString("applyStatus");
            }
        }
        //清空参数
        paramList.clear();
        if("税务初审".equals(applyStatus) || "退回税务初审".equals(applyStatus)){
            esbService.addParam(paramList,"applyStatus","税务初审通过","string");
        }else if("税务复审".equals(applyStatus)){
            esbService.addParam(paramList,"applyStatus","税务复审通过","string");
        }else {
            rtn.setFail("当前状态为"+applyStatus+",无法审批通过！");
            return rtn;
        }
        esbService.addParam(paramList,"id",id,"string");
        esbService.addParam(paramList,"updater",userCode,"string");
        esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb("30000019","1", "10","0","false",paramList);
        return rtn;
    }

    /**
     *function 税务确认（签章方式）已经没用了
     */
    @RequestMapping("/real/estate-file/swqrqz")
    @ResponseBody
    public Object swqrqz(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn = new CommonResult("00","");
        try{
            String path = null;
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            log.info("税务确认"+param);
            String id = param.get("id");
            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("id",id,"string"));
            paramList.add(esbService.addParam("ownership","'1','2'","int"));
            rtn = esbService.queryEsb("30000017","1", "100","0","false",paramList);
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0) {
                JSONObject jo = ja.getJSONObject(0);
                path = jo.getString("path");
                String file = FileUtils.readFileToString(new File(filePath+File.separator+path+File.separator+id));
                String type = param.get("type");
                String qzid = param.get("qzid");
                if("gjzqz".equals(type)){
                    String keyword = param.get("keyword");
                    rtn = ItextUtil.qz(file,keyword,qzid);
                }else if("jdwzqz".equals(type)){
                    String page = param.get("page");
                    String pdfx = param.get("pdfx");
                    String pdfy = param.get("pdfy");
                    rtn = ItextUtil.qz(file,Integer.parseInt(pdfx),Integer.parseInt(pdfy),Integer.parseInt(page),qzid);
                }else{
                    rtn.setFail("签章类型"+type+"错误！");
                }
            }
            if(rtn.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+path+File.separator+id),(String)rtn.getData());
                esbService.addParam(paramList,"id",id,"string");
                esbService.addParam(paramList,"swsfyqr","是","string");
                esbService.addParam(paramList,"updater",userCode,"string");
                esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
                rtn.addResult(esbService.queryEsb("30000030","1", "100","0","false",paramList));
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail("税务确认发生异常"+e.getMessage());
        }
        return rtn;
    }
    /**
     *function 税务确认（上传附件方式）已经没用了
     */
    @RequestMapping("/real/estate-file/swqr")
    @ResponseBody
    public Object swqr(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        CommonResult result = new CommonResult("00","");
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("税务确认（上传附件方式）");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String name = file.getOriginalFilename();
            String size = String.valueOf(file.getSize());
            String type = name.substring(name.lastIndexOf(".")+1);
            String fileStr = Base64Utils.encodeToString(byes);
            String id = param.get("id");
            String path = param.get("path");
            if(StringUtils.isEmpty(path)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("id",id,"string"));
                result = esbService.queryEsb("30000017","1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        path = jo.getString("path");
                    }
                }
            }
            if(result.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+path+File.separator+id),fileStr);
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("id",id,"string"));
                paramList.add(esbService.addParam("swsfyqr","是","string"));
                paramList.add(esbService.addParam("type",type,"string"));
                paramList.add(esbService.addParam("size",size,"string"));
                paramList.add(esbService.addParam("name",name,"string"));
                paramList.add(esbService.addParam("updater",userCode,"string"));
                paramList.add(esbService.addParam("updateTime",DateUtils.strNow(),"string"));
                result = esbService.queryEsb("30000030","1", "10","0","false",paramList);
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }
    /**
     *function 推送 已经没用了
     */
    @RequestMapping("/real/estate-file/ts")
    @ResponseBody
    public Object ts(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        CommonResult result = new CommonResult("00","");
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("更新附件");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String fileStr = Base64Utils.encodeToString(byes);
            String id = param.get("id");
            String path = param.get("path");
            if(StringUtils.isEmpty(path)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("id",id,"string"));
                result = esbService.queryEsb("30000017","1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        path = jo.getString("path");
                    }
                }
            }
            if(result.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+path+File.separator+id),fileStr);
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("id",id,"string"));
                paramList.add(esbService.addParam("nsrsfqz","是","string"));
                paramList.add(esbService.addParam("updater",userCode,"string"));
                paramList.add(esbService.addParam("updateTime",DateUtils.strNow(),"string"));
                result = esbService.queryEsb("30000030","1", "10","0","false",paramList);
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }
    /**
     *function 归档
     */
    @RequestMapping("/real/estate-file/gd")
    @ResponseBody
    public Object gd(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        CommonResult rtn;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("归档: "+param);
        String id = param.get("id");
        String fjids = param.get("fjids");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"id",id,"string");
        rtn = esbService.queryEsb("30000009","1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                String applyStatus = jo.getString("applyStatus");
                if(!"复审通过".equals(applyStatus)){
                    rtn.setFail("只有复审通过状态才能进行归档操作！");
                    return rtn;
                }
            }
        }
        if(fjids.length()>0){
            rtn = wjzlgd(id,fjids,userCode,userName);
        }else{
            rtn.setFail("请选择要归档的附件！");
        }
        if(rtn.isSuccess()){
            paramList.clear();
            esbService.addParam(paramList,"id",id,"string");
            esbService.addParam(paramList,"applyStatus","已归档","string");
            esbService.addParam(paramList,"updater",userCode,"string");
            esbService.addParam(paramList,"updateTime", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
            rtn = esbService.queryEsb("30000019","1", "10","0","false",paramList);
        }
        return rtn;
    }

    /**
     *function 文件资料归档，先获取需要归档数据，然后分别进行电子档案系统归档和本地归档
     * @param zbid 主表id
     * @param fjids 附件id集合
     * @param userName 办理人员
     */
    public CommonResult wjzlgd(String zbid,String fjids,String userCode,String userName){
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        //查询主表信息
        esbService.addParam(paramList,"id",zbid,"string");
        esbService.addParam(paramList,"reciid",zbid,"string");
        rtn = esbService.queryEsb("30000009","1", "100","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                //主表本次要归档的附件集合
                List<JSONObject> fjList = new ArrayList<>();
                String djxh= jo.getString("applyNum");
                String nsrsbh = "";
                String nsrmc = "";
                //买方信息列表
                CommonResult rt2 = esbService.queryEsb("30000011","1", "10","0","false",paramList);
                if(rt2.isSuccess()){
                    JSONArray buyerInfo = (JSONArray)rt2.getData();
                    if(buyerInfo.size()>0){
                        for (int i=0;i<buyerInfo.size();i++){
                            JSONObject buyer = buyerInfo.getJSONObject(i);
                            if(buyer.getInteger("sort") == 1 || "1".equals(buyer.getString("sort"))){
                                nsrsbh = buyer.getString("idNum");
                                nsrmc = buyer.getString("name");
                                break;
                            }
                        }
                    }
                }

                //查询主表附件
                JSONObject ywsj = new JSONObject();
                ywsj.put("ywbh",zbid);
                ywsj.put("ywlx",ywlx);
                rtn = callFile("flzllb", ywsj);
                if(rtn.isSuccess()){
                    //附件数据
                    ja = (JSONArray)rtn.getData();
                    if(ja.size()>0) {
                        for(int i=0;i<ja.size();i++){
                            JSONObject obj = ja.getJSONObject(i);
                            String fjid = obj.getString("fjbh");
                            String gdzt= obj.getString("gdzt");
                            if(!EncodedTools.isFullContains(fjids,fjid)){
                                continue;
                            }
                            if("已归档".equals(gdzt)){
                                continue;
                            }
                            fjList.add(obj);
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(fjList)){
                    //电子档案系统初始化并归档主表附件
                    rtn = dzdaxtGd(fjList,zbid,djxh,nsrsbh,nsrmc);
                }
                if(rtn.isSuccess() && !CollectionUtils.isEmpty(fjList)){
                    //本地档案系统归档
                    bddaxtGd(fjList,jo,userCode,nsrmc,nsrsbh,userName);
                }
            }
        }
        return rtn;
    }

    /**
     * 本地档案系统归档（存量房个人归档时ywid使用主表的id，uuid自动生成，无意义）
     * @param dataList 要归档的数据集合
     * @param zbxx 主表信息
     * @param userName 办理人员
     * @return
     */
    private CommonResult bddaxtGd(List<JSONObject> dataList,JSONObject zbxx,String userCode,String nsrmc,String nsrsbh,String userName){
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        String ywbh = zbxx.getString("applyNum");
        String ywid = zbxx.getString("id");
        String zbuuid = "";
        int fjsl = dataList.size();
        boolean existFlag = false;
        //先查询主表是否已有该归档数据
        paramList.add(esbService.addParam("ywid",ywid,"string"));
        rtn = esbService.queryEsb(daQuerySqlxh,"1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray data = (JSONArray) rtn.getData();
            if(data.size() == 0){
                //写入档案表信息
                paramList.clear();
                zbuuid = UUID.randomUUID().toString().replaceAll("-", "");
                paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                paramList.add(esbService.addParam("ywbh",ywbh,"string"));
                paramList.add(esbService.addParam("ywlx","存量房个人","string"));
                paramList.add(esbService.addParam("gdnd", String.valueOf(LocalDate.now().getYear()),"string"));
                paramList.add(esbService.addParam("gdrq", LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"),"string"));
                paramList.add(esbService.addParam("blry",StringUtils.isBlank(userName) ? "管理员" : userName,"string"));
                paramList.add(esbService.addParam("fjsl",String.valueOf(fjsl),"string"));
                paramList.add(esbService.addParam("gdfs","自动归档","string"));
                paramList.add(esbService.addParam("ywid",ywid,"string"));
                paramList.add(esbService.addParam("gdzt","已归档","string"));
                paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
                paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
                paramList.add(esbService.addParam("lrrdm",userCode,"string"));
                paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("yxbz","Y","string"));
                rtn = esbService.queryEsb(daInsertSqlxh,"1", "10","0","false",paramList);
            }else {
                JSONObject jsonObject = data.getJSONObject(0);
                zbuuid = jsonObject.getString("uuid");
                //查询并更新附件数量
                JSONObject ywsj = new JSONObject();
                ywsj.put("ywbh",ywid);
                ywsj.put("ywlx",ywlx);
                rtn = callFile("flzlsl", ywsj);
                if(rtn.isSuccess()){
                    // 2. 获取data字段（字符串）
                    int sl = (Integer)rtn.getData();
                    fjsl+=sl;
                }
                existFlag = true;
            }
        }

        if(existFlag){
            if(rtn.isSuccess()){
                //更新档案表附件数量
                paramList.clear();
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("fjsl",String.valueOf(fjsl),"string"));
                paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                rtn = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
            }
        }
        return rtn;
    }
    /**
     *function 电子档案系统归档
     */
    private CommonResult dzdaxtGd(List<JSONObject> dataList,String id,String djxh,String nsrsbh,String nsrmc){
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        String gldh = esbService.MD5(id);
        //初始化
        rtn = dzzlkService.init(gldh,djxh,nsrsbh,nsrmc);
        if(rtn.isSuccess()){
            for(int i=0;i<dataList.size();i++){
                JSONObject fileData = dataList.get(i);
                String fjid = fileData.getString("fjbh");
                String zlext=fileData.getString("wjlx");
                String wjurl=fileData.getString("wjurl");
                String documentType=fileData.getString("wjzllx");
                String zllxdm = "";
                documentType=documentType == null?"":documentType;
                if(documentType.contains("申报表")){//税费申报表
                    zllxdm = "125131";
                }else if(documentType.contains("凭证")){//完税凭证
                    zllxdm = "125132";
                }else if(documentType.contains("采集表")){//房屋采集表
                    zllxdm = "125133";
                }else if(documentType.contains("税")){//个人所得税扣除
                    zllxdm = "125134";
                }else if(documentType.contains("土地")){//土地增值税扣除
                    zllxdm = "125135";
                }else if(documentType.contains("受理")){//不动产登记受理通知单
                    zllxdm = "125136";
                }else if(documentType.contains("采集表")){//存量房交易采集表
                    zllxdm = "125137";
                }else if(documentType.contains("房产证")){//房产证
                    zllxdm = "123493";
                }else if(documentType.contains("身份证")){//身份证
                    zllxdm = "001832";//对应资料库000750
                }else if(documentType.contains("合同")){//简易合同
                    zllxdm = "000109";//对应资料库000315
                }else{
                    zllxdm = "000000";//其他资料
                }
                if(StringUtils.isNotEmpty(zllxdm)){
                    try{
                        //读取文件内容
                        String zldata = "";
                        //通过url获取流数据
                        try (InputStream inputStream = new URL(wjurl).openStream()) {
                            byte[] bytes = IoUtil.readBytes(inputStream);
//                            zldata = new String(bytes, Charset.defaultCharset());
                            zldata = Base64Utils.encodeToString(bytes);
                        }
                        if(StringUtils.isNotEmpty(zldata)){
                            rtn.addResult(dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata,zlext));
                            if(rtn.isSuccess()){
                                Map<String, Object> zlscObj = (Map<String, Object>)rtn.getData();
                                String zldjxh = (String)zlscObj.get("zldjxh");
                                String zlurlnw = (String)zlscObj.get("zlurlnw");
                                //更新业务附件表状态
                                JSONObject ywsj = new JSONObject();
                                ywsj.put("fjbh",fjid);
                                ywsj.put("ywlx",ywlx);
                                ywsj.put("gdzt","已归档");
                                ywsj.put("zlurlnw",zlurlnw);
                                ywsj.put("zldjxh",zldjxh);
                                ywsj.put("gldh",gldh);
                                rtn = callFile("flzlgx", ywsj);
                                if(!rtn.isSuccess()){
                                    rtn.setFail(rtn.getMsg());
                                }
                            }
                        }else{
                            rtn.setFail(fjid+"文件不存在");
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        log.info(id+"归档时发生异常"+e.getMessage());
                        rtn.setFail(e.getMessage());
                    }
                }
            }
            if(rtn.isSuccess()){
                rtn = dzzlkService.sxzttz(gldh, djxh, nsrsbh, nsrmc);
            }
        }
        return rtn;
    }
    /**
     * function 通过id获取附件信息 已经没用了
     */
    @RequestMapping("/real/estate-file/attachmentList")
    @ResponseBody
    public Object attachmentList(HttpServletRequest request){
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("通过id获取附件信息"+param);
        String id = param.get("id");
        String documentType = param.get("documentType");
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("reciid",id,"string"));
        paramList.add(esbService.addParam("documentType",documentType,"string"));
        CommonResult rtFile = esbService.queryEsb("30000017","1", "10","0","false",paramList);
        return rtFile;
    }

    /**
     * function 文件上传
     */
    @RequestMapping("/real/estate-file/upload")
    @ResponseBody
    public Object upload(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("文件上传");
        CommonResult rtFile = new CommonResult("00","success");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            String name = file.getOriginalFilename();
            String type = name.substring(name.lastIndexOf(".")+1);
            byte[] bytes = file.getBytes();
            if (imageToPdfService.isValidImageFile(file)) {
                // 转换图片
                bytes = imageToPdfService.convertSingleImageToPdf(file);
                type = "pdf";
            }
            String fileStr = Base64Utils.encodeToString(bytes);
            String documentType = param.get("documentType");
            String ownership = param.get("ownership");
            String reciid = param.get("reciid");
            String configId = param.get("configId");
            String path =DateUtils.strNow("yyyyMM");
            String size = String.valueOf(file.getSize());
            boolean qzflag = false;
            if(StringUtils.contains(documentType,"采集表") || StringUtils.contains(documentType,"申报表") || StringUtils.contains(documentType,"承诺书")){
                qzflag = true;
            }
            JSONObject data = new JSONObject();
            data.put("ywbh",reciid);
            data.put("pzbh",configId);
            data.put("wjzllx",documentType);
            data.put("wjgs",ownership);
            data.put("wjm",name);
            data.put("wjlx",type);
            data.put("wjdx",size);
            data.put("wjnr",fileStr);
            data.put("lrrdm",userCode);
            data.put("lrrq",DateUtils.strNow());
            data.put("xgrdm",userCode);
            data.put("xgrq",DateUtils.strNow());
            data.put("yxbz","Y");
            data.put("nsrsfqz",qzflag ? "否" : "-");
            data.put("sfxyswqr",qzflag ? "否" : "-");
            data.put("swsfyqr",qzflag ? "否" : "-");
            data.put("swsfqz",qzflag ? "否" : "-");
            data.put("gdzt","未归档");
            data.put("gldh","");
            data.put("zldjxh","");
            data.put("zlurlnw","");
            data.put("sfts",qzflag ? "否" : "-");
            data.put("ywlx",ywlx);
            rtFile = callFile("flzlxz", data);
            if(rtFile.isSuccess()){
                List<Map<String,Object>> paramList = new ArrayList<>();
                String id = (String) rtFile.getData();
                paramList.add(esbService.addParam("id",id,"string"));
                paramList.add(esbService.addParam("reciid",reciid,"string"));
                paramList.add(esbService.addParam("configId",configId,"string"));
                paramList.add(esbService.addParam("documentType",documentType,"string"));
                paramList.add(esbService.addParam("ownership",ownership,"string"));
                paramList.add(esbService.addParam("name",name,"string"));
                paramList.add(esbService.addParam("path",path,"string"));
                paramList.add(esbService.addParam("url","","string"));
                paramList.add(esbService.addParam("type",type,"string"));
                paramList.add(esbService.addParam("size",size,"string"));
                paramList.add(esbService.addParam("content","" ,"string"));
                paramList.add(esbService.addParam("creator",userCode,"string"));
                paramList.add(esbService.addParam("createTime", DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("updater",userCode,"string"));
                paramList.add(esbService.addParam("updateTime",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("deleted","0","string"));
                paramList.add(esbService.addParam("nsrsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("sfxyswqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfyqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("gdzt","未归档","string"));
                paramList.add(esbService.addParam("gldh","","string"));
                paramList.add(esbService.addParam("zldjxh","","string"));
                paramList.add(esbService.addParam("zlurlnw","","string"));
                paramList.add(esbService.addParam("sfts",qzflag ? "否" : "-","string"));
                rtFile.setSuccess(esbService.paramListToMap(paramList));
            }else {
                rtFile.setFail("上传失败："+rtFile.getMsg());
            }
        }catch (Exception e){
            rtFile.setFail("上传失败："+e.getMessage());
        }
        return rtFile;
    }
}