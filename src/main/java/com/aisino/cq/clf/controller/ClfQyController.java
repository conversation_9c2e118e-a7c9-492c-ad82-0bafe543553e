package com.aisino.cq.clf.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.utlis.EncodedTools;
import com.aisino.cq.utlis.ItextUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.aisino.cq.clf.util.RequestUtil.callFile;

@RestController
@RequestMapping("clfqy")
public class ClfQyController {
    private static final Logger log = LoggerFactory.getLogger(ClfQyController.class);
    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    //主表新增sql序号
    private static final String qyInsertSqlxh = "30000041";
    //主表查询sql序号
    private static final String qyQuerySqlxh = "30000047";
    //主表更新sql序号
    private static final String qyUpdateSqlxh = "30000051";
    //主表审批sql序号
    private static final String qyShenpiSqlxh = "30000050";
    //经办人新增sql序号
    private static final String jbrInsertSqlxh = "30000042";
    //经办人查询sql序号
    private static final String jbrQuerySqlxh = "30000048";
    //经办人修改sql序号
    private static final String jbrUpdateSqlxh = "30000052";
    //房屋新增sql序号
    private static final String fwInsertSqlxh = "30000043";
    //房屋修改sql序号
    private static final String fwUpdateSqlxh = "30000053";
    //房屋分页查询
    private static final String fwQueryPageSqlxh = "30000045";
    //房屋单条查询
    private static final String fwQuerySqlxh = "30000046";
    //附件新增sql序号
    private static final String fjInsertSqlxh = "30000044";
    //附件修改sql序号
    private static final String fjUpdateSqlxh = "30000054";
    //附件查询sql序号
    private static final String fjQuerySqlxh = "30000049";
    //档案新增sql序号
    private static final String daInsertSqlxh = "30000055";
    //档案更新sql序号
    private static final String daUpdateSqlxh = "30000059";
    //档案查询sql序号
    private static final String daQuerySqlxh = "30000057";
    //明细新增sql序号
    private static final String mxInsertSqlxh = "30000056";
    //明细更新sql序号
    private static final String mxUpdateSqlxh = "30000060";
    //明细查询sql序号
    private static final String mxQuerySqlxh = "30000058";
    //明细查询数量sql序号
    private static final String mxQueryCountSqlxh = "30000098";
    private static final String ywlx = "clfqy";
    @Resource(name = "sfzzThreadPool")
    private Executor executor;

    @RequestMapping("isAlive")
    @ResponseBody
    public Object isAlive(HttpServletRequest request){
        CommonResult rtn = new CommonResult();
        rtn.setSuccess("");
        return rtn;
    }

    @RequestMapping("getLoginUser")
    @ResponseBody
    public Object getLoginUser(HttpServletRequest request){
        return request.getSession().getAttribute("usercode");
    }

    /**
     * function 获取房屋列表信息
     */
    @RequestMapping("getList")
    @ResponseBody
    public Object getList(HttpServletRequest request){
        log.info("获取列表信息");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String fgslbh = param.get("fgslbh");
        String cqzh = param.get("cqzh");
        String zldz = param.get("zldz");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"fgslbh",fgslbh,"string");
        esbService.addParam(paramList,"cqzh",cqzh,"string");
        esbService.addParam(paramList,"zldz",zldz,"string");
        CommonResult fwPage = esbService.queryEsb(fwQueryPageSqlxh, pageNo, pageSize, "0", "true", paramList);
        /*List<JSONObject> datas = (List<JSONObject>)fwPage.getData();
        if(datas != null && datas.size() >0){
            for(JSONObject data : datas){
                String zbuuid = data.getString("zbuuid");
                paramList.clear();
                esbService.addParam(paramList,"uuid",zbuuid,"string");
                CommonResult qyData = esbService.queryEsb(qyQuerySqlxh, "1", "10", "0", "false", paramList);
                List<JSONObject> qydatas = (List<JSONObject>)qyData.getData();
                String sqbh = qydatas.get(0).getString("sqbh");
                data.put("sqbh",sqbh);
            }
        }*/
        return fwPage;
    }

    /**
     * 根据主表id 获取所有附件信息
     * @param zbuuid
     * @return
     */
    @RequestMapping("fileList")
    @ResponseBody
    public Object fileList(String zbuuid,String fwuuid){
        JSONObject ywsj = new JSONObject();
        JSONArray ywbhJsonArray = new JSONArray();
        ywbhJsonArray.add(zbuuid);
        ywbhJsonArray.add(fwuuid);
        ywsj.put("ywbhList",ywbhJsonArray);
        ywsj.put("ywlx",ywlx);
        return callFile("flzllb", ywsj);
    }

    /**
     * function 文件下载
     */
    @RequestMapping("download")
    @ResponseBody
    public void download(String uuid,HttpServletResponse response){
        log.info("文件下载"+uuid);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",ywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String fileName = obj.getString("wjm");
                    String wjurl = obj.getString("wjurl");
                    String downloadName = new String(fileName.getBytes("gb2312"),"ISO8859-1");
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-Disposition","attachment;filename=\""+downloadName);
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                        byte[] b = IoUtil.readBytes(inputStream);
                        out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 文件推送平板签章 已经没用了
     */
    @RequestMapping("previewqk")
    @ResponseBody
    public Object previewqk(HttpServletRequest request,HttpServletResponse response){
        CommonResult result = new CommonResult("00","");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            String uuid = param.get("uuid");
            String wjlj = param.get("wjlj");
            if(StringUtils.isEmpty(wjlj)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                result = esbService.queryEsb(fjQuerySqlxh,"1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        wjlj = jo.getString("wjlj");
                    }
                }
            }
            if(result.isSuccess()){
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject jo = ja.getJSONObject(0);
                    String file = FileUtils.readFileToString(new File(filePath+File.separator+wjlj+File.separator+uuid));
                    jo.put("wjnr",file);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }

    /**
     * function 文件预览
     */
    @RequestMapping("preview")
    @ResponseBody
    public void preview(String uuid,HttpServletResponse response){
        log.info("文件预览"+uuid);
        try{
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",ywlx);
            CommonResult result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段（字符串）
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String wjlx = obj.getString("wjlx");
                    String wjurl = obj.getString("wjurl");
                    if("pdf".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                    }else if("png".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_PNG_VALUE);
                    }else if("jpg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("jpeg".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                    }else if("gif".equalsIgnoreCase(wjlx)){
                        response.setContentType(MediaType.IMAGE_GIF_VALUE);
                    }
                    // 更安全的写法（Java 7+ try-with-resources）
                    try (ServletOutputStream out = response.getOutputStream();
                         InputStream inputStream = new URL(wjurl).openStream()) {
                        byte[] b = IoUtil.readBytes(inputStream);
                        out.write(b);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * function 删除附件
     * @param uuid 附件编号
     * @param request
     * @return
     */
    @RequestMapping("real/fj/delete")
    @ResponseBody
    public Object delete(String uuid,HttpServletRequest request){
        log.info("删除附件");
        String userCode = (String)request.getSession().getAttribute("usercode");
        userCode = userCode == null ? "1" :userCode;
        CommonResult result = new CommonResult();
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            JSONObject ywsj = new JSONObject();
            ywsj.put("fjbh",uuid);
            ywsj.put("ywlx",ywlx);
            result = callFile("flzllb",ywsj);
            if(result.isSuccess()){
                // 2. 获取data字段
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject obj = ja.getJSONObject(0);
                    String gdzt = obj.getString("gdzt");
                    String zldjxh = obj.getString("zldjxh");
                    String gldh = obj.getString("gldh");
                    //可能是房屋uuid或者是主表uuid
                    String ywbh = obj.getString("ywbh");
                    if("已归档".equals(gdzt)){
                        log.info("当前资料已归档---删除归档信息----");
                        result = dzzlkService.zlyc(gldh,zldjxh);
                    }
                    if(result.isSuccess()){
                        //调用附件删除接口，也将归档附件删掉了，现在是同一个附件
                        result = callFile("flzlsc",ywsj);
                        if(result.isSuccess()){
                            //查询房屋获取主表id
                            paramList.clear();
                            paramList.add(esbService.addParam("uuid",ywbh,"string"));
                            result = esbService.queryEsb(fwQuerySqlxh,"1", "100","0","false",paramList);
                            if(result.isSuccess()){
                                JSONArray fwxxes = (JSONArray)result.getData();
                                ywsj.clear();
                                String zbuuid = "";
                                if(fwxxes.size()>0){
                                    //大于0代表是房屋uuid
                                    JSONObject fwxx = fwxxes.getJSONObject(0);
                                    zbuuid = fwxx.getString("zbuuid");
                                    JSONArray ywbhJsonArray = new JSONArray();
                                    //主表id
                                    ywbhJsonArray.add(zbuuid);
                                    //房屋id
                                    ywbhJsonArray.add(ywbh);
                                    ywsj.put("ywbhList",ywbhJsonArray);
                                    ywsj.put("ywlx",ywlx);
                                }else {
                                    //否则是主表uuid
                                    ywsj.put("ywbh",ywbh);
                                    ywsj.put("ywlx",ywlx);
                                }
                                //查询附件数量
                                result = callFile("flzlsl", ywsj);
                                if(result.isSuccess()){
                                    // 2. 获取data字段（字符串）
                                    String sl = (String) result.getData();
                                    //先查询主表是否已有该归档数据
                                    paramList.add(esbService.addParam("ywid",ywbh+"-"+zbuuid,"string"));
                                    result = esbService.queryEsb(daQuerySqlxh,"1", "10","0","false",paramList);
                                    if(result.isSuccess()){
                                        JSONArray data = (JSONArray) result.getData();
                                        if(data.size() == 1){
                                            JSONObject jsonObject = data.getJSONObject(0);
                                            String dazbid = jsonObject.getString("uuid");
                                            //更新档案主表附件数量
                                            paramList.clear();
                                            paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                                            paramList.add(esbService.addParam("fjsl",sl,"string"));
                                            paramList.add(esbService.addParam("uuid",dazbid,"string"));
                                            result = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail(e.getMessage());
        }
        return result;
    }

    /**
     * function 归档资料移除（不删除本地文件）已经没用了
     */
    @RequestMapping("real/fj/zlyc")
    @ResponseBody
    public Object zlyc(String uuid,HttpServletRequest request){
        log.info("归档资料移除"+uuid);
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid",uuid,"string"));
            result = esbService.queryEsb(fjQuerySqlxh,"1", "100","0","false",paramList);
            if(result.isSuccess()){
                JSONArray ja = (JSONArray)result.getData();
                if(ja.size()>0) {
                    JSONObject jo = ja.getJSONObject(0);
                    String gdzt = jo.getString("gdzt");
                    String zldjxh = jo.getString("zldjxh");
                    String gldh = jo.getString("gldh");
                    if("已归档".equals(gdzt)){
                        log.info("当前资料已归档---删除归档信息----");
                        result = dzzlkService.zlyc(gldh,zldjxh);
                        if(result.isSuccess()){
                            paramList.add(esbService.addParam("gdzt","已删除","string"));
                            paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                            paramList.add(esbService.addParam("xgrq",DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string"));
                            result = esbService.queryEsb(fjUpdateSqlxh,"1", "10","0","false",paramList);
                        }
                    }else{
                        result.setFail("当前资料未归档");
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail(e.getMessage());
        }
        return result;
    }

    /**
     * function 房屋信息详情页面
     */
    @RequestMapping("/real/fw/get")
    @ResponseBody
    public Object fgcl(String zbuuid){
        log.info("房屋信息主表uuid："+zbuuid);
        CommonResult result = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
        //主表详情
        CommonResult rt = esbService.queryEsb(qyQuerySqlxh,"1", "10","0","false",paramList);
        if(rt.isSuccess()){
            JSONArray ja = (JSONArray)rt.getData();
            if(ja.size()>0){
                JSONObject jo = ja.getJSONObject(0);
                //经办人列表
                paramList.clear();
                paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
                CommonResult rtFile = esbService.queryEsb(jbrQuerySqlxh,"1", "10","0","false",paramList);
                if(rtFile.isSuccess()){
                    jo.put("jbrxxList",rtFile.getData());
                }
                result.setSuccess(jo);
            }else{
                result.setFail("无数据");
            }
        }
        return result;
    }

    /**
     *function 审批
     */
    @RequestMapping("/shenpi")
    @ResponseBody
    public Object shenpi(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        CommonResult rtn = new CommonResult("00","");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("审批"+param);
        String uuid = param.get("uuid");
        String sqbh = param.get("sqbh");
        String bhyy = param.get("bhyy");
        String type = param.get("type");// 已保存、待提交、已提交、受理中、审批驳回（买方）、审批驳回（卖方）、审批通过、已归档
        if("已归档".equals(type)){
            String fjuuids = param.get("fjuuids");
            if(fjuuids.length()>0){
                rtn = wjzlgd(uuid,"",fjuuids,userCode,userName);
            }else{
                rtn.setFail("请选择要归档的附件！");
            }
        }

        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"sqbh",sqbh,"string");
        esbService.addParam(paramList,"bhyy",bhyy,"string");
        esbService.addParam(paramList,"sqzt",type,"string");
        esbService.addParam(paramList,"xgrdm",userCode,"string");
        esbService.addParam(paramList,"xgrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb(qyShenpiSqlxh,paramList);
        return rtn;
    }

    /**
     *function 审批驳回、终止
     */
    @RequestMapping("/real/qy/reject")
    @ResponseBody
    public Object reject(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        userCode = userCode == null ? "1": userCode;
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("审批"+param);
        String sqbh = param.get("sqbh");
        String bhyy = param.get("bhyy");
        String sqzt = param.get("sqzt");// 已保存、待提交、已提交、受理中、审批驳回（买方）、审批驳回（卖方）、审批通过、已归档
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"sqbh",sqbh,"string");
        esbService.addParam(paramList,"bhyy",bhyy,"string");
        esbService.addParam(paramList,"sqzt",sqzt,"string");
        esbService.addParam(paramList,"xgrdm",userCode,"string");
        esbService.addParam(paramList,"xgrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        return esbService.queryEsb(qyShenpiSqlxh,"1", "10","0","false",paramList);
    }

    /**
     *function 审批通过
     */
    @RequestMapping("/real/qy/over")
    @ResponseBody
    public Object over(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn = new CommonResult("00","");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("审核"+param);
        String sqbh = param.get("sqbh");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"sqbh",sqbh,"string");
        esbService.addParam(paramList,"sqzt","审核通过","string");
        esbService.addParam(paramList,"xgrdm",userCode,"string");
        esbService.addParam(paramList,"xgrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb(qyShenpiSqlxh,"1", "10","0","false",paramList);
        return rtn;
    }

    /**
     *function 受理
     */
    @RequestMapping("/real/qy/shouli")
    @ResponseBody
    public Object shouli(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn = new CommonResult("00","");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("受理"+param);
        String sqbh = param.get("sqbh");
        List<Map<String,Object>> paramList = new ArrayList<>();
        esbService.addParam(paramList,"sqbh",sqbh,"string");
        esbService.addParam(paramList,"sqzt","受理中","string");
        esbService.addParam(paramList,"xgrdm",userCode,"string");
        esbService.addParam(paramList,"xgrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
        rtn = esbService.queryEsb(qyShenpiSqlxh,"1", "10","0","false",paramList);
        return rtn;
    }

    /**
     *function 税务确认 已经没用了
     */
    @RequestMapping("/real/fj/swqrqz")
    @ResponseBody
    public Object swqrqz(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult rtn = new CommonResult("00","");
        try{
            String wjlj = null;
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            log.info("税务确认"+param);
            String uuid = param.get("uuid");
            List<Map<String,Object>> paramList = new ArrayList<>();
            paramList.add(esbService.addParam("uuid",uuid,"string"));
            paramList.add(esbService.addParam("wjgs","'1','2'","int"));
            rtn = esbService.queryEsb(fjQuerySqlxh,"1", "100","0","false",paramList);
            JSONArray ja = (JSONArray)rtn.getData();
            if(ja.size()>0) {
                JSONObject jo = ja.getJSONObject(0);
                wjlj = jo.getString("wjlj");
                String file = FileUtils.readFileToString(new File(filePath+File.separator+wjlj+File.separator+uuid));
                String type = param.get("wjlx");
                String qzid = param.get("qzid");
                if("gjzqz".equals(type)){
                    String keyword = param.get("keyword");
                    rtn = ItextUtil.qz(file,keyword,qzid);
                }else if("jdwzqz".equals(type)){
                    String page = param.get("page");
                    String pdfx = param.get("pdfx");
                    String pdfy = param.get("pdfy");
                    rtn = ItextUtil.qz(file,Integer.parseInt(pdfx),Integer.parseInt(pdfy),Integer.parseInt(page),qzid);
                }else{
                    rtn.setFail("签章类型"+type+"错误！");
                }
            }
            if(rtn.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+wjlj+File.separator+uuid),(String)rtn.getData());
                esbService.addParam(paramList,"uuid",uuid,"string");
                esbService.addParam(paramList,"swsfyqr","是","string");
                esbService.addParam(paramList,"xgrdm",userCode,"string");
                esbService.addParam(paramList,"xgrq", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"),"string");
                rtn.addResult(esbService.queryEsb(fjUpdateSqlxh,"1", "100","0","false",paramList));
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail("税务确认发生异常"+e.getMessage());
        }
        return rtn;
    }
    /**
     *function 税务确认 已经没用了
     */
    @RequestMapping("/real/fj/swqr")
    @ResponseBody
    public Object swqr(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        CommonResult result = new CommonResult("00","");
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("税务确认（上传附件方式）");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String name = file.getOriginalFilename();
            String size = String.valueOf(file.getSize());
            String type = name.substring(name.lastIndexOf(".")+1);
            String fileStr = Base64Utils.encodeToString(byes);
            String uuid = param.get("uuid");
            String wjlj = param.get("wjlj");
            if(StringUtils.isEmpty(wjlj)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                result = esbService.queryEsb(fjQuerySqlxh,"1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        wjlj = jo.getString("wjlj");
                    }
                }
            }
            if(result.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+wjlj+File.separator+uuid),fileStr);
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                paramList.add(esbService.addParam("swsfyqr","是","string"));
                paramList.add(esbService.addParam("wjlx",type,"string"));
                paramList.add(esbService.addParam("wjdx",size,"string"));
                paramList.add(esbService.addParam("wjm",name,"string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                result = esbService.queryEsb(fjUpdateSqlxh,"1", "10","0","false",paramList);
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }
    /**
     *function 签章完之后回推 已经没用了
     */
    @RequestMapping("/real/fj/ts")
    @ResponseBody
    public Object ts(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        CommonResult result = new CommonResult("00","");
        String userCode = (String)request.getSession().getAttribute("usercode");
        log.info("签章附件回推");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String fileStr = Base64Utils.encodeToString(byes);
            String uuid = param.get("uuid");
            String wjlj = param.get("wjlj");
            if(StringUtils.isEmpty(wjlj)){
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                result = esbService.queryEsb(fjQuerySqlxh,"1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray ja = (JSONArray)result.getData();
                    if(ja.size()>0) {
                        JSONObject jo = ja.getJSONObject(0);
                        wjlj = jo.getString("wjlj");
                    }
                }
            }
            if(result.isSuccess()){
                FileUtils.writeStringToFile(new File(filePath+File.separator+wjlj+File.separator+uuid),fileStr);
                List<Map<String,Object>> paramList = new ArrayList<>();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                paramList.add(esbService.addParam("nsrsfqz","是","string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                result = esbService.queryEsb(fjUpdateSqlxh,"1", "10","0","false",paramList);
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("读取文件异常："+e.getMessage());
        }
        return result;
    }
    /**
     *function 归档
     */
    @RequestMapping("/real/fj/gd")
    @ResponseBody
    public Object gd(HttpServletRequest request){
        String userCode = (String)request.getSession().getAttribute("usercode");
        String userName = (String)request.getSession().getAttribute("username");
        userCode = userCode == null ? "1" : userCode;
        CommonResult rtn = new CommonResult("00","");
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("归档"+param);
        //主表uuid
        String zbuuid = param.get("zbuuid");
        //房屋uuid
        String fwuuid = param.get("fwuuid");
        //附件uuid集合
        String fjuuids = param.get("fjuuids");
        if(fjuuids.length()>0){
            rtn = wjzlgd(zbuuid,fwuuid,fjuuids,userCode,userName);
        }else{
            rtn.setFail("请选择要归档的附件！");
        }
        if(rtn.isSuccess()){
            //将房屋信息状态更改为已归档
            List<Map<String,Object>> paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid",fwuuid,"string");
            esbService.addParam(paramList,"fgtszt","已归档","string");
            esbService.addParam(paramList,"xgrdm",userCode,"string");
            rtn = esbService.queryEsb(fwUpdateSqlxh,"1", "10","0","false",paramList);
        }
        return rtn;
    }
    /**
     *function 文件资料归档，先获取需要归档数据，然后分别进行电子档案系统归档和本地归档
     * @param zbuuid 主表uuid
     * @param fwuuid 房屋uuid
     * @param fjuuids 附件uuid集合
     * @param userCode 办理人员编码
     */
    public CommonResult wjzlgd(String zbuuid,String fwuuid,String fjuuids,String userCode,String userName){
        CommonResult rtn = new CommonResult();
        try{
            List<Map<String,Object>> paramList = new ArrayList<>();
            esbService.addParam(paramList,"zbuuid",zbuuid,"string");
            //查询主表信息 获取纳税人名称和纳税人识别号
            rtn = esbService.queryEsb(qyQuerySqlxh,"1", "100","0","false",paramList);
            if(rtn.isSuccess()){
                JSONArray ja = (JSONArray)rtn.getData();
                if(ja.size()>0){
                    //主表本次要归档的附件集合
                    List<JSONObject> zbfjList = new ArrayList<>();
                    //房屋本次要归档的附件集合
                    List<JSONObject> fwfjList = new ArrayList<>();
                    //主表一般就是一条数据
                    JSONObject jo = ja.getJSONObject(0);
                    //登记序号
                    String djxh= jo.getString("xsftyshxydm");
                    //纳税人识别号
                    String nsrsbh=jo.getString("xsftyshxydm");
                    //纳税人名称
                    String nsrmc=jo.getString("xsfdwzcmc");
                    //申请编号
                    String sqbh=jo.getString("sqbh");
                    //查询主表附件信息
                    JSONObject ywsj = new JSONObject();
                    ywsj.put("ywbh",zbuuid);
                    ywsj.put("ywlx",ywlx);
                    rtn = callFile("flzllb", ywsj);
                    if(rtn.isSuccess()) {
                        //主表附件数据
                        ja = (JSONArray)rtn.getData();
                        if(ja.size()>0) {
                            for(int i=0;i<ja.size();i++){
                                JSONObject obj = ja.getJSONObject(i);
                                String fjuuid = obj.getString("uuid");
                                String gdzt=obj.getString("gdzt");
                                if(!EncodedTools.isFullContains(fjuuids,fjuuid)){
                                    continue;
                                }
                                if("已归档".equals(gdzt)){
                                    continue;
                                }
                                zbfjList.add(obj);
                            }
                        }
                    }
                    //查询房屋附件信息
                    ywsj.put("ywbh",fwuuid);
                    rtn = callFile("flzllb", ywsj);
                    if(rtn.isSuccess()) {
                        //房屋附件数据
                        ja = (JSONArray)rtn.getData();
                        if(ja.size()>0) {
                            for(int i=0;i<ja.size();i++){
                                JSONObject obj = ja.getJSONObject(i);
                                String fjuuid = obj.getString("uuid");
                                String gdzt = obj.getString("gdzt");
                                if(!EncodedTools.isFullContains(fjuuids,fjuuid)){
                                    continue;
                                }
                                if("已归档".equals(gdzt)){
                                    continue;
                                }
                                zbfjList.add(obj);
                            }
                        }
                    }
                    if(!CollectionUtils.isEmpty(zbfjList)){
                        //电子档案系统初始化并归档主表附件
                        rtn = dzdaxtGd(zbfjList,zbuuid,djxh,nsrsbh,nsrmc);
                    }
                    if(!CollectionUtils.isEmpty(fwfjList)){
                        //电子档案系统初始化并归档房屋附件
                     rtn = dzdaxtGd(fwfjList,zbuuid,djxh,nsrsbh,nsrmc);
                    }
                    //查询房屋信息
                    paramList.clear();
                    esbService.addParam(paramList,"uuid",fwuuid,"string");
                    rtn = esbService.queryEsb(fwQuerySqlxh,"1", "100","0","false",paramList);
                    if(rtn.isSuccess()){
                        JSONArray fwxxes = (JSONArray)rtn.getData();
                        if(fwxxes.size()>0){
                            //房屋信息根据uuid查就是一条数据
                            JSONObject fwxx = fwxxes.getJSONObject(0);
                            zbfjList.addAll(fwfjList);
                            //本地档案系统归档
                            bddaxtGd(zbfjList,sqbh,fwxx,userCode,nsrmc,nsrsbh,userName);
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail(e.getMessage());
        }
        return rtn;
    }

    /**
     * 本地档案系统归档（存量房企业归档时ywid使用企业uuid-房屋uuid，uuid自动生成，无意义）
     * @param dataList 要归档的数据集合
     * @param sqbh 申请编号
     * @param fwxx 房屋信息
     * @param userCode 办理人员代码
     * @return
     */
    private CommonResult bddaxtGd(List<JSONObject> dataList,String sqbh,JSONObject fwxx,String userCode,String nsrmc,String nsrsbh,String userName){
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        String cqzh = fwxx.getString("cqzh");
        String fwuuid = fwxx.getString("uuid");
        String qyuuid = fwxx.getString("zbuuid");
        String zbuuid = "";
        int fjsl = dataList.size();
        boolean existFlag = false;
        //先查询主表是否已有该归档数据
        paramList.add(esbService.addParam("ywid",qyuuid+"-"+fwuuid,"string"));
        rtn = esbService.queryEsb(daQuerySqlxh,"1", "10","0","false",paramList);
        if(rtn.isSuccess()){
            JSONArray data = (JSONArray) rtn.getData();
            if(data.size() == 0){
                //写入档案表信息
                paramList.clear();
                zbuuid = UUID.randomUUID().toString().replaceAll("-", "");
                paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                paramList.add(esbService.addParam("ywbh",sqbh+"-"+cqzh,"string"));
                paramList.add(esbService.addParam("ywlx","存量房企业","string"));
                paramList.add(esbService.addParam("gdnd", String.valueOf(LocalDate.now().getYear()),"string"));
                paramList.add(esbService.addParam("gdrq", LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"),"string"));
                paramList.add(esbService.addParam("blry",StringUtils.isBlank(userName) ? "管理员" : userName,"string"));
                paramList.add(esbService.addParam("fjsl",String.valueOf(fjsl),"string"));
                paramList.add(esbService.addParam("gdfs","自动归档","string"));
                paramList.add(esbService.addParam("ywid",qyuuid+"-"+fwuuid,"string"));
                paramList.add(esbService.addParam("gdzt","已归档","string"));
                paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
                paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
                paramList.add(esbService.addParam("lrrdm",userCode,"string"));
                paramList.add(esbService.addParam("lrrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("yxbz","Y","string"));
                rtn = esbService.queryEsb(daInsertSqlxh,"1", "10","0","false",paramList);
            }else {
                JSONObject jsonObject = data.getJSONObject(0);
                zbuuid = jsonObject.getString("uuid");
                //查询房屋附件数量
                JSONObject ywsj = new JSONObject();
                JSONArray ywbhJsonArray = new JSONArray();
                ywbhJsonArray.add(qyuuid);
                ywbhJsonArray.add(fwuuid);
                ywsj.put("ywbhList",ywbhJsonArray);
                ywsj.put("ywlx",ywlx);
                rtn = callFile("flzlsl", ywsj);
                if(rtn.isSuccess()){
                    // 2. 获取data字段（字符串）
                    fjsl += (Integer) rtn.getData();
                }
                existFlag = true;
            }
        }

        if(existFlag){
            if(rtn.isSuccess()){
                //更新档案表附件数量
                paramList.clear();
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("fjsl",String.valueOf(fjsl),"string"));
                paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                rtn = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
            }
        }
        return rtn;
    }
    /**
     * 电子档案系统对要归档的数据进行初始化并归档
     * @param dataList 要归档的数据集合
     * @param uuid   是主表附件还是房屋附件，分别取对应的uuid
     * @return
     */
    private CommonResult dzdaxtGd(List<JSONObject> dataList,String uuid,String djxh,String nsrsbh,String nsrmc){
        String gldh = esbService.MD5(uuid);
        CommonResult result = new CommonResult();
        try {
            //初始化
            CommonResult init = dzzlkService.init(gldh, djxh, nsrsbh, nsrmc);
            if(init.isSuccess()) {
                List<CompletableFuture<String>> futures = new ArrayList<>();
                for(JSONObject fileObj : dataList){
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                        List<Map<String,Object>> paramList = new ArrayList<>();
                        CommonResult rtn = new CommonResult();
                        String fjuuid = fileObj.getString("uuid");
                        String wjlx=fileObj.getString("wjlx");
                        String wjurl=fileObj.getString("wjurl");
                        String wjzllx=fileObj.getString("wjzllx");
                        String zllxdm = "";
                        wjzllx=wjzllx == null?"":wjzllx;
                        if(wjzllx.contains("申报表")){//税费申报表
                            zllxdm = "125131";
                        }else if(wjzllx.contains("凭证")){//完税凭证
                            zllxdm = "125132";
                        }else if(wjzllx.contains("采集表")){//房屋采集表
                            zllxdm = "125133";
                        }else if(wjzllx.contains("税")){//个人所得税扣除
                            zllxdm = "125134";
                        }else if(wjzllx.contains("土地")){//土地增值税扣除
                            zllxdm = "125135";
                        }else if(wjzllx.contains("受理")){//不动产登记受理通知单
                            zllxdm = "125136";
                        }else if(wjzllx.contains("采集表")){//存量房交易采集表
                            zllxdm = "125137";
                        }else if(wjzllx.contains("房产证")){//房产证
                            zllxdm = "123493";
                        }else if(wjzllx.contains("身份证")){//身份证
                            zllxdm = "001832";//对应资料库000750
                        }else if(wjzllx.contains("合同")){//简易合同
                            zllxdm = "000109";//对应资料库000315
                        }else if(wjzllx.contains("营业执照")){
                            zllxdm = "000000"; //TODO
                        }else if(wjzllx.contains("委托书")){
                            zllxdm = "000000"; //TODO
                        }else{
                            zllxdm = "000000";//其他资料
                        }
                        if(StringUtils.isNotEmpty(zllxdm)){
                            try{
                                //通过url获取流数据
                                String zldata = "";
                                try (InputStream inputStream = new URL(wjurl).openStream()) {
                                    byte[] bytes = IoUtil.readBytes(inputStream);
//                                    zldata = new String(bytes, Charset.defaultCharset());
                                    zldata = Base64Utils.encodeToString(bytes);
                                }
                                if(StringUtils.isNotEmpty(zldata)){
                                    rtn.addResult(dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata,wjlx));
                                    if(rtn.isSuccess()){
                                        Map<String, Object> zlscObj = (Map<String, Object>)rtn.getData();
                                        String zldjxh = (String)zlscObj.get("zldjxh");
                                        String zlurlnw = (String)zlscObj.get("zlurlnw");
                                        //更新业务附件表状态
                                        JSONObject ywsj = new JSONObject();
                                        ywsj.put("fjbh",fjuuid);
                                        ywsj.put("ywlx",ywlx);
                                        ywsj.put("gdzt","已归档");
                                        ywsj.put("zlurlnw",zlurlnw);
                                        ywsj.put("zldjxh",zldjxh);
                                        ywsj.put("gldh",gldh);
                                        rtn = callFile("flzlgx", ywsj);
                                        if(!rtn.isSuccess()){
                                            rtn.setFail(rtn.getMsg());
                                        }
                                    }
                                }else{
                                    rtn.setFail(fjuuid+"文件不存在");
                                }
                            }catch (Exception e){
                                log.info(uuid+"归档时发生异常"+e.getMessage());
                                rtn.setFail(e.getMessage());
                            }
                        }
                        return "success";
                    },executor).exceptionally(ex -> {
                        String fjuuid = fileObj.getString("uuid");
                        log.error("uuid为：【" + fjuuid + "】的数据电子归档失败，原因为：" + ex.getMessage());
                        return "【uuid："+fjuuid+"】";
                    });
                    futures.add(future);
                }
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                //默认推送成功
                boolean flag = true;
                StringBuffer stringBuffer = new StringBuffer("以下数据归档异常：");
                //等待所有异步任务完成
                allFutures.get(300L, TimeUnit.SECONDS);
                for(CompletableFuture<String> future : futures){
                    String taskResult = future.get();
                    if(!StringUtils.equals(taskResult,"success")){
                        //有推送失败的，则整体标记为失败
                        flag = false;
                        stringBuffer.append(taskResult);
                    }
                }
                if(flag){
                    result = dzzlkService.sxzttz(gldh, djxh, nsrsbh, nsrmc);
                }
            }
        } catch (Exception e) {
            log.error("归档发生异常：{}",e.getMessage());
            result.setFail(e.getMessage());
        }
        return result;
    }
    /**
     * function 通过id获取附件信息 已经没用了
     */
    @RequestMapping("/real/fj/attachmentList")
    @ResponseBody
    public Object attachmentList(HttpServletRequest request){
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        log.info("通过id获取附件信息"+param);
        String zbuuid = param.get("zbuuid");
        String wjzllx = param.get("wjzllx");
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("zbuuid",zbuuid,"string"));
        paramList.add(esbService.addParam("wjzllx",wjzllx,"string"));
        CommonResult rtFile = esbService.queryEsb(fjQuerySqlxh,"1", "10","0","false",paramList);
        return rtFile;
    }

    /**
     * function 文件上传
     */
    @RequestMapping("/real/fj/upload")
    @ResponseBody
    public Object upload(HttpServletRequest request, @RequestParam("file")MultipartFile file){
        String userCode = (String)request.getSession().getAttribute("usercode");
        userCode = userCode == null ? "1" : userCode;
        log.info("文件上传");
        CommonResult rtFile = new CommonResult("00","success");
        try{
            Map<String,String> param = RequestUtil.getReqParamMap(request);
            byte[] byes = file.getBytes();
            String fileStr = Base64Utils.encodeToString(byes);
//            String fileId = UUID.randomUUID().toString().replaceAll("-","");
            String wjzllx = param.get("wjzllx");
            boolean qzflag = false;
            if(StringUtils.contains(wjzllx,"采集表") || StringUtils.contains(wjzllx,"申报表") || StringUtils.contains(wjzllx,"承诺书")){
                qzflag = true;
            }
            String wjgs = param.get("wjgs");
            //新上传的附件是绑在这个房屋上的
            String fwuuid = param.get("fwuuid");
            String wjm = file.getOriginalFilename();
            String wjlj =DateUtils.strNow("yyyyMM");
            String wjdx = String.valueOf(file.getSize());
            String wjlx = wjm.substring(wjm.lastIndexOf(".")+1);
            JSONObject data = new JSONObject();
//            data.put("fjbh",fileId);
            data.put("ywbh",fwuuid);
            data.put("pzbh","0");
            data.put("wjzllx",wjzllx);
            data.put("wjgs",wjgs);
            data.put("wjm",wjm);
            data.put("wjlx",wjlx);
            data.put("wjdx",wjdx);
            data.put("wjnr",fileStr);
            data.put("lrrdm",userCode);
            data.put("lrrq",DateUtils.strNow());
            data.put("xgrdm",userCode);
            data.put("xgrq",DateUtils.strNow());
            data.put("yxbz","Y");
            data.put("nsrsfqz",qzflag ? "否" : "-");
            data.put("sfxyswqr",qzflag ? "否" : "-");
            data.put("swsfyqr",qzflag ? "否" : "-");
            data.put("swsfqz",qzflag ? "否" : "-");
            data.put("gdzt","未归档");
            data.put("gldh","");
            data.put("zldjxh","");
            data.put("zlurlnw","");
            data.put("sfts",qzflag ? "否" : "-");
            data.put("ywlx",ywlx);
            rtFile = callFile("flzlxz", data);
            if(rtFile.isSuccess()){
                List<Map<String,Object>> paramList = new ArrayList<>();
                String fileId = (String) rtFile.getData();
                paramList.add(esbService.addParam("uuid",fileId,"string"));
                paramList.add(esbService.addParam("zbuuid",fwuuid,"string"));
                paramList.add(esbService.addParam("pzbh","0","string"));
                paramList.add(esbService.addParam("wjzllx",wjzllx,"string"));
                paramList.add(esbService.addParam("wjgs",wjgs,"string"));
                paramList.add(esbService.addParam("wjm",wjm,"string"));
                paramList.add(esbService.addParam("wjlj",wjlj,"string"));
                paramList.add(esbService.addParam("wjurl","temp","string"));
                paramList.add(esbService.addParam("wjlx",wjlx,"string"));
                paramList.add(esbService.addParam("wjdx",wjdx,"string"));
                paramList.add(esbService.addParam("wjnr","" ,"string"));
                paramList.add(esbService.addParam("lrrdm",userCode,"string"));
                paramList.add(esbService.addParam("lrrq", DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                paramList.add(esbService.addParam("xgrq",DateUtils.strNow(),"string"));
                paramList.add(esbService.addParam("yxbz","Y","string"));
                paramList.add(esbService.addParam("nsrsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("sfxyswqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfyqr",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("swsfqz",qzflag ? "否" : "-","string"));
                paramList.add(esbService.addParam("gdzt","未归档","string"));
                paramList.add(esbService.addParam("gldh","","string"));
                paramList.add(esbService.addParam("zldjxh","","string"));
                paramList.add(esbService.addParam("zlurlnw","","string"));
                paramList.add(esbService.addParam("sfts",qzflag ? "否" : "-","string"));
                rtFile.setSuccess(esbService.paramListToMap(paramList));
            }else {
                rtFile.setFail("上传失败："+rtFile.getMsg());
            }
        }catch (Exception e){
            rtFile.setFail("上传失败："+e.getMessage());
        }
        return rtFile;
    }
}