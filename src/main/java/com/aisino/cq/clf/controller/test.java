package com.aisino.cq.clf.controller;

import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.util.CommonResult;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class test {

    public static void main(String[] args) {
        try {
            daupdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //DsjptService esbService = new DsjptService();
        //List<Map<String,Object>> paramList = new ArrayList<>();
        //paramList.add(esbService.addParam("id","2222","string"));
        //CommonResult rt8 = esbService.queryEsb("30000010","1", "100","0","false",paramList);
        //System.out.println(DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        //add10();
        //System.out.println(new Md5Util().bytesToMD5("sf".getBytes()));
        //add2();
        //add1();
        //String gldh = esbService.MD5("8891");
        //System.out.println(gldh);
        //List<Map<String,Object>> paramList = new ArrayList<>();
        //paramList.add(esbService.addParam("id","1","string"));
        //esbService.queryEsb("30000031","1", "10","0","false",paramList);//删除主表数据
    }

    /**
     * 档案主表update
     */
    public static void daupdate(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid","6f8e792f6a0f4abe8bc840bc55d28c1c","string"));
        paramList.add(esbService.addParam("gdzt","已归档","string"));
        CommonResult rtFile = esbService.queryEsb("30000059","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
    /**
     * 不动产表update
     */
    public static void rcmtupdate(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1194","string"));
        paramList.add(esbService.addParam("delete","1","string"));
        CommonResult rtFile = esbService.queryEsb("30000040","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
    //企业主表
    public static void qyadd1(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid","37284626138821839","string"));
        paramList.add(esbService.addParam("sqbh","************","string"));
        paramList.add(esbService.addParam("sqzt","已提交","string"));
        paramList.add(esbService.addParam("sqr","张三","string"));
        paramList.add(esbService.addParam("xsfdwzcmc","哈哈科技","string"));
        paramList.add(esbService.addParam("xsftyshxydm","500201199601205648","string"));
        paramList.add(esbService.addParam("xsffddbr","哈哈","string"));
        paramList.add(esbService.addParam("xsflxdz","北京市海淀区","string"));
        paramList.add(esbService.addParam("xsflxdh","18937235462","string"));
        paramList.add(esbService.addParam("gmfdwzcmc","呵呵科技","string"));
        paramList.add(esbService.addParam("gmftyshxydm","7388274721838884821","string"));
        paramList.add(esbService.addParam("gmffddbr","呵呵","string"));
        paramList.add(esbService.addParam("gmflxdz","广东省广州市","string"));
        paramList.add(esbService.addParam("gmflxdh","18738493284","string"));
        paramList.add(esbService.addParam("bhyy","","string"));

        paramList.add(esbService.addParam("lrrdm","1","string"));
        paramList.add(esbService.addParam("lrrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("xgrdm","100","string"));
        paramList.add(esbService.addParam("xgrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("yxbz","Y","string"));
        CommonResult rtFile = esbService.queryEsb("30000041","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
    //企业经办人表
    public static void qyadd2(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid","gdgaggdgada2838747213","string"));
        paramList.add(esbService.addParam("zbuuid","222233334444565555","string"));
        paramList.add(esbService.addParam("jyjs","卖方","string"));
        paramList.add(esbService.addParam("jbrxm","张三","string"));
        paramList.add(esbService.addParam("sfzh","1287472838287472","string"));
        paramList.add(esbService.addParam("lxdh","18738493284","string"));
        paramList.add(esbService.addParam("lxdz","北京市海淀区","string"));
        paramList.add(esbService.addParam("wtrq","2025/1/1","string"));

        paramList.add(esbService.addParam("lrrdm","1","string"));
        paramList.add(esbService.addParam("lrrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("xgrdm","100","string"));
        paramList.add(esbService.addParam("xgrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("yxbz","Y","string"));
        CommonResult rtFile = esbService.queryEsb("30000042","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //房屋信息表
    public static void qyadd3(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid",UUID.randomUUID().toString().replaceAll("-",""),"string"));
        paramList.add(esbService.addParam("zbuuid","37284626138821839","string"));
        paramList.add(esbService.addParam("fwlx","个人住房","string"));
        paramList.add(esbService.addParam("zldz","北京市丰台区","string"));
        paramList.add(esbService.addParam("jzmj","300","string"));
        paramList.add(esbService.addParam("cqzh","CQ877372183921","string"));
        paramList.add(esbService.addParam("jzjg","钢混","string"));
        paramList.add(esbService.addParam("sbdj","10000","string"));
        paramList.add(esbService.addParam("sbzj","3000000","string"));
        paramList.add(esbService.addParam("fgslbh","SLBH38271477273721","string"));
        paramList.add(esbService.addParam("fgtszt","未推送","string"));

        paramList.add(esbService.addParam("lrrdm","1","string"));
        paramList.add(esbService.addParam("lrrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("xgrdm","100","string"));
        paramList.add(esbService.addParam("xgrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("yxbz","Y","string"));
        CommonResult rtFile = esbService.queryEsb("30000043","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
    //房屋信息查询
    public static void qyQuery3(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("zldz","北京","string"));
        CommonResult rtFile = esbService.queryEsb("30000045","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //附件添加
    public static void qyadd4() throws Exception{
        String fileId = UUID.randomUUID().toString().replaceAll("-","");
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid",fileId,"string"));
        paramList.add(esbService.addParam("zbuuid","222233334444565555","string"));
        paramList.add(esbService.addParam("pzbh","1","string"));
        paramList.add(esbService.addParam("wjzllx","身份证","string"));
        paramList.add(esbService.addParam("wjgs","3","string"));
        paramList.add(esbService.addParam("wjm","身份证.jpg","string"));
        paramList.add(esbService.addParam("wjlj","15320231061","string"));
        paramList.add(esbService.addParam("wjurl","1","string"));
        paramList.add(esbService.addParam("wjlx","jpg","string"));
        paramList.add(esbService.addParam("wjdx","10000","string"));
        paramList.add(esbService.addParam("wjnr",fileId ,"string"));
        paramList.add(esbService.addParam("lrrdm","1","string"));
        paramList.add(esbService.addParam("lrrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("xgrdm","1","string"));
        paramList.add(esbService.addParam("xgrq","2025-03-18 17:17:35","string"));
        paramList.add(esbService.addParam("yxbz","Y","string"));
        paramList.add(esbService.addParam("nsrsfqz","-","string"));
        paramList.add(esbService.addParam("sfxyswqr","-","string"));
        paramList.add(esbService.addParam("swsfyqr","-","string"));
        paramList.add(esbService.addParam("swsfqz","-","string"));
        paramList.add(esbService.addParam("gdzt","-","string"));
        paramList.add(esbService.addParam("gldh","-","string"));
        paramList.add(esbService.addParam("zldjxh","-","string"));
        paramList.add(esbService.addParam("zlurlnw","-","string"));
        paramList.add(esbService.addParam("sfts","-","string"));
        CommonResult rtFile = esbService.queryEsb("30000044","1", "10","0","false",paramList);
        System.out.println(rtFile);
        FileUtils.writeStringToFile(new File("d:/"+fileId),"sadf");
    }
    //主表
    public static void add1(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","2222","string"));
        paramList.add(esbService.addParam("applyNum","0","string"));
        paramList.add(esbService.addParam("applicant","张三","string"));
        paramList.add(esbService.addParam("applyTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","500201199601205648","string"));
        paramList.add(esbService.addParam("phone","14320231061","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","1","string"));
        paramList.add(esbService.addParam("applyStatus","1","string"));
        paramList.add(esbService.addParam("sellerStatus","1","string"));
        paramList.add(esbService.addParam("buyerStatus","1","string"));
        paramList.add(esbService.addParam("shareSituation","1","string"));
        paramList.add(esbService.addParam("houseType","个人住宅","string"));
        paramList.add(esbService.addParam("remark","","string"));
        paramList.add(esbService.addParam("applyRemark","","string"));
        paramList.add(esbService.addParam("housePurpose","housePurpose","string"));
        paramList.add(esbService.addParam("certificateType","certificateType","string"));
        paramList.add(esbService.addParam("paymentMethod","paymentMethod","string"));

        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000024","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
    //卖方信息列表
    public static void add2(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","2222","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("obligeeType","1","string"));
        paramList.add(esbService.addParam("name","张章","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","501201199601205648","string"));
        paramList.add(esbService.addParam("phone","14320234061","string"));
        paramList.add(esbService.addParam("maritalStatus","1","string"));
        paramList.add(esbService.addParam("houseNumber","1","string"));
        paramList.add(esbService.addParam("holdingRatio","1","string"));
        paramList.add(esbService.addParam("shareSituation","shareSituation","string"));

        paramList.add(esbService.addParam("sort","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000029","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //买方信息列表
    public static void add3(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("obligeeType","1","string"));
        paramList.add(esbService.addParam("name","张四","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","510201199601205648","string"));
        paramList.add(esbService.addParam("phone","15320231061","string"));
        paramList.add(esbService.addParam("maritalStatus","1","string"));
        paramList.add(esbService.addParam("houseNumber","1","string"));
        paramList.add(esbService.addParam("obligeeNature","1","string"));
        paramList.add(esbService.addParam("holdingRatio","1","string"));
        paramList.add(esbService.addParam("shareSituation","1","string"));
        paramList.add(esbService.addParam("sort","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000020","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //附件添加
    public static void add5() throws Exception{
        String fileId = UUID.randomUUID().toString().replaceAll("-","");
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("configId","1","string"));
        paramList.add(esbService.addParam("documentType","版式文件","string"));
        paramList.add(esbService.addParam("ownership","2","string"));
        paramList.add(esbService.addParam("name","身份证","string"));
        paramList.add(esbService.addParam("path","15320231061","string"));
        paramList.add(esbService.addParam("url","1","string"));
        paramList.add(esbService.addParam("type","png","string"));
        paramList.add(esbService.addParam("size","1","string"));
        paramList.add(esbService.addParam("content",fileId ,"string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000027","1", "10","0","false",paramList);
        System.out.println(rtFile);
        FileUtils.writeStringToFile(new File("d:/"+fileId),"sadf");
    }

    //房管材料
    public static void add6(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("hallWindowNum","1","string"));
        paramList.add(esbService.addParam("acceptanceNum","1","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","1","string"));
        paramList.add(esbService.addParam("sellerName","张四","string"));
        paramList.add(esbService.addParam("sellerIdNum","510201199601205648","string"));
        paramList.add(esbService.addParam("buyerName","张四2","string"));
        paramList.add(esbService.addParam("buyerIdNum","520201199601205648","string"));
        paramList.add(esbService.addParam("pushStatus","1","string"));
        paramList.add(esbService.addParam("remark","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000028","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //个人住宅详情
    public static void add4(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("houseLocation","1","string"));
        paramList.add(esbService.addParam("realEstateCertificateNum","张四","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","201","string"));
        paramList.add(esbService.addParam("buildingArea","510201199601205648","string"));
        paramList.add(esbService.addParam("housePurpose","15320231061","string"));
        paramList.add(esbService.addParam("certificateType","1","string"));
        paramList.add(esbService.addParam("paymentMethod","1","string"));
        paramList.add(esbService.addParam("obligeeNature","1","string"));
        paramList.add(esbService.addParam("realEstateName","1","string"));
        paramList.add(esbService.addParam("suiteType","1","string"));
        paramList.add(esbService.addParam("houseRoom","1","string"));
        paramList.add(esbService.addParam("houseHall","1","string"));
        paramList.add(esbService.addParam("houseBathroom","1","string"));
        paramList.add(esbService.addParam("houseKitchen","1","string"));
        paramList.add(esbService.addParam("residentialType","1","string"));
        paramList.add(esbService.addParam("groundFloors","1","string"));
        paramList.add(esbService.addParam("undergroundFloors","1","string"));
        paramList.add(esbService.addParam("toward","1","string"));
        paramList.add(esbService.addParam("pmLevel","1","string"));
        paramList.add(esbService.addParam("supportingFacility","1","string"));
        paramList.add(esbService.addParam("buildingCompletionYear","1","string"));
        paramList.add(esbService.addParam("declareTransactionTotalPrice","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000023","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //存量商业用房评估信息采集表-车位
    public static void add7(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("houseType","车位","string"));
        paramList.add(esbService.addParam("evaluatePartition","1","string"));
        paramList.add(esbService.addParam("houseLocation","渝北区某某小区","string"));
        paramList.add(esbService.addParam("pmName","渝北物业","string"));
        paramList.add(esbService.addParam("realEstateCertificateNum","1231231312234","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","201","string"));
        paramList.add(esbService.addParam("propertyRightOwner","李四","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","500112196612032545","string"));
        paramList.add(esbService.addParam("phone","12344556677","string"));
        paramList.add(esbService.addParam("buildingArea","108","string"));
        paramList.add(esbService.addParam("floor","16","string"));
        paramList.add(esbService.addParam("internalArea","100","string"));
        paramList.add(esbService.addParam("landUseRightType","1","string"));
        paramList.add(esbService.addParam("landUseRightEndDate","1","string"));
        paramList.add(esbService.addParam("remainingLandUseErm","12","string"));
        paramList.add(esbService.addParam("parkingSpaceType","1","string"));
        paramList.add(esbService.addParam("transactionDate","1","string"));
        paramList.add(esbService.addParam("declareTransactionTotalPrice","1","string"));
        paramList.add(esbService.addParam("declareTransactionUnitPrice","1","string"));
        paramList.add(esbService.addParam("houseOtherDescription","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000026","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //存量商业用房评估信息采集表-办公用房
    public static void add8(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("houseType","车位","string"));
        paramList.add(esbService.addParam("evaluatePartition","1","string"));
        paramList.add(esbService.addParam("houseLocation","渝北区某某小区","string"));
        paramList.add(esbService.addParam("pmName","渝北物业","string"));
        paramList.add(esbService.addParam("realEstateCertificateNum","1231231312234","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","201","string"));
        paramList.add(esbService.addParam("propertyRightOwner","李四","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","500112196612032545","string"));
        paramList.add(esbService.addParam("phone","12344556677","string"));
        paramList.add(esbService.addParam("buildingArea","108","string"));
        paramList.add(esbService.addParam("floor","16","string"));
        paramList.add(esbService.addParam("buildingStructure","100","string"));
        paramList.add(esbService.addParam("landUseRightType","1","string"));
        paramList.add(esbService.addParam("landUseRightEndDate","1","string"));
        paramList.add(esbService.addParam("remainingLandUseErm","12","string"));
        paramList.add(esbService.addParam("yearBuilt","1","string"));
        paramList.add(esbService.addParam("transactionDate","1","string"));
        paramList.add(esbService.addParam("declareTransactionTotalPrice","1","string"));
        paramList.add(esbService.addParam("declareTransactionUnitPrice","1","string"));
        paramList.add(esbService.addParam("houseOtherDescription","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000025","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //存量商业用房评估信息采集表-商服用房
    public static void add9(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("houseType","车位","string"));
        paramList.add(esbService.addParam("evaluatePartition","1","string"));
        paramList.add(esbService.addParam("houseLocation","渝北区某某小区","string"));
        paramList.add(esbService.addParam("pmName","渝北物业","string"));
        paramList.add(esbService.addParam("realEstateCertificateNum","1231231312234","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","201","string"));
        paramList.add(esbService.addParam("propertyRightOwner","李四","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","500112196612032545","string"));
        paramList.add(esbService.addParam("phone","12344556677","string"));
        paramList.add(esbService.addParam("buildingArea","108","string"));
        paramList.add(esbService.addParam("floor","16","string"));
        paramList.add(esbService.addParam("buildingStructure","100","string"));
        paramList.add(esbService.addParam("landUseRightType","1","string"));
        paramList.add(esbService.addParam("landUseRightEndDate","1","string"));
        paramList.add(esbService.addParam("remainingLandUseErm","12","string"));
        paramList.add(esbService.addParam("houseShape","1","string"));
        paramList.add(esbService.addParam("transactionDate","1","string"));
        paramList.add(esbService.addParam("declareTransactionTotalPrice","1","string"));
        paramList.add(esbService.addParam("declareTransactionUnitPrice","1","string"));
        paramList.add(esbService.addParam("houseOtherDescription","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000022","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }

    //存量商业用房评估信息采集表-公寓
    public static void add10(){
        DsjptService esbService = new DsjptService();
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("id","1","string"));
        paramList.add(esbService.addParam("reciid","1","string"));
        paramList.add(esbService.addParam("houseType","车位","string"));
        paramList.add(esbService.addParam("evaluatePartition","1","string"));
        paramList.add(esbService.addParam("houseLocation","渝北区某某小区","string"));
        paramList.add(esbService.addParam("pmName","渝北物业","string"));
        paramList.add(esbService.addParam("realEstateCertificateNum","1231231312234","string"));
        paramList.add(esbService.addParam("realEstateUnitNum","201","string"));
        paramList.add(esbService.addParam("propertyRightOwner","李四","string"));
        paramList.add(esbService.addParam("idType","201","string"));
        paramList.add(esbService.addParam("idNum","500112196612032545","string"));
        paramList.add(esbService.addParam("phone","12344556677","string"));
        paramList.add(esbService.addParam("buildingArea","108","string"));
        paramList.add(esbService.addParam("floor","16","string"));
        paramList.add(esbService.addParam("buildingStructure","100","string"));
        paramList.add(esbService.addParam("landUseRightType","1","string"));
        paramList.add(esbService.addParam("landUseRightEndDate","1","string"));
        paramList.add(esbService.addParam("remainingLandUseErm","12","string"));
        paramList.add(esbService.addParam("floorHeight","1","string"));
        paramList.add(esbService.addParam("yearBuilt","1","string"));
        paramList.add(esbService.addParam("buildingImplement","1","string"));
        paramList.add(esbService.addParam("transactionDate","1","string"));
        paramList.add(esbService.addParam("declareTransactionTotalPrice","1","string"));
        paramList.add(esbService.addParam("declareTransactionUnitPrice","1","string"));
        paramList.add(esbService.addParam("creator","admin","string"));
        paramList.add(esbService.addParam("createTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("updater","admin","string"));
        paramList.add(esbService.addParam("updateTime","2025-01-13 17:17:35","string"));
        paramList.add(esbService.addParam("deleted","0","string"));
        CommonResult rtFile = esbService.queryEsb("30000021","1", "10","0","false",paramList);
        System.out.println(rtFile);
    }
}
