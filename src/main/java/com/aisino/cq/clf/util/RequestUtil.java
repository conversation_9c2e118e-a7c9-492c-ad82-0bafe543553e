package com.aisino.cq.clf.util;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class RequestUtil {
    private static final Logger log = LoggerFactory.getLogger(RequestUtil.class);

    public static Map<String,String> getReqParamMap(HttpServletRequest request){
        Map<String,String[]> map = request.getParameterMap();
        Map<String,String> param = new HashMap<>();
        for(String str:map.keySet()){
            param.put(str,map.get(str)[0].trim());
        }
        return param;
    }
    /**
     * 调用税友接口操作文件
     * @param ywlx 业务类型
     * @param ywsj 业务数据
     * @return
     */
    public static CommonResult callFile(String ywlx, JSONObject ywsj){
        String format = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN);
        JSONObject jo = new JSONObject();
        jo.put("ywlx",ywlx);
        jo.put("qqsj",format);
        jo.put("qqlsh", UUID.randomUUID().toString().replaceAll("-",""));
        jo.put("ywsj",ywsj);
        log.info("请求http://newznhhdpt.cqsw.tax.cn/open/hxyw，请求参数为：{}", JSONUtil.toJsonStr(jo));
        String result = HttpUtil.post("http://newznhhdpt.cqsw.tax.cn/open/hxyw", JSONUtil.toJsonStr(jo));
        log.info("请求http://newznhhdpt.cqsw.tax.cn/open/hxyw，响应结果为：{}",result);
        CommonResult commonResult = new CommonResult();
        // 解析外层对象
        JSONObject obj = JSON.parseObject(result);
        if(StringUtils.equals("flzllb",ywlx)){
            //查询附件列表
            if(obj != null){
                //如果ywsj中包含pageNo和pageSize参数，则代表是分页查询，需要取data中的list数据,total取哪个都行，值是一样的
                if(ywsj.containsKey("pageNo") && ywsj.containsKey("pageSize")){
                    Object data = obj.get("data");
                    JSONObject dataObj = null;
                    //如果data是个字符串，需要格式化为标准json对象
                    if(data instanceof String){
                        dataObj = JSON.parseObject((String)data);
                    }else {
                        dataObj = obj.getJSONObject("data");
                    }
                    commonResult.setResult(obj.getString("code"), obj.getString("msg"), dataObj.get("list"),dataObj.getInteger("total"));
                }else {
                    Object data = obj.get("data");
                    //如果data是个字符串，需要格式化为标准json对象
                    if(data instanceof String){
                        data = JSON.parseArray((String)data);
                    }
                    commonResult.setResult(obj.getString("code"), obj.getString("msg"), data,obj.getInteger("total"));
                }
            }
        }else {
            // 其他操作
            Object data = obj.get("data");
            commonResult.setResult(obj.getString("code"), obj.getString("msg"), data,obj.getInteger("total"));
        }

        return commonResult;
    }
}
