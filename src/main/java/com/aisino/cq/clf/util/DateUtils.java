package com.aisino.cq.clf.util;

import org.apache.commons.lang.time.DateFormatUtils;

import java.util.Calendar;
import java.util.Date;

public class DateUtils {
    public static String strNow(){
        return DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss");
    }
    public static String strNow(String formatter){
        return DateFormatUtils.format(new Date(),formatter);
    }


    public static String strNowAddYear(int year){
        String lastDay = null;
        try
        {
            Calendar ca =Calendar.getInstance();
            ca.setTime(new Date());
            ca.add(Calendar.YEAR, year);
            return DateFormatUtils.format(ca.getTime(),"yyyy-MM-dd HH:mm:ss");
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return lastDay;
    }

    public static void main(String[] args) {
        System.out.println(strNowAddYear(1));
    }

}
