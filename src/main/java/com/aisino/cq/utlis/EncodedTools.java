package com.aisino.cq.utlis;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @function 字符串处理方法
 * <AUTHOR> by HAI
 */
public class EncodedTools {
	public static String gbkToIso(Object obj){
		try {
			return new String(objToStr(obj).getBytes("GBK"),"iso-8859-1");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String isoToGbk(Object obj){
		try {
			return new String(objToStr(obj).getBytes("iso-8859-1"),"GBK");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String isoToutf8(Object obj){
		try {
			return new String(objToStr(obj).getBytes("iso-8859-1"),"utf-8");	
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String utf8Toiso(Object obj){
		try {
			return new String(objToStr(obj).getBytes("utf-8"),"iso-8859-1");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String gbkToutf8(Object obj){
		try {
			return new String(objToStr(obj).getBytes("GBK"),"utf-8");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String utf8ToGbk(Object obj){
		try {
			return new String(objToStr(obj).getBytes("utf-8"),"GBK");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String utf8ToAscII(Object obj){
		try {
			return new String(objToStr(obj).getBytes("utf-8"),"US-ASCII");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String AscIIToUtf8(Object obj){
		try {
			return new String(objToStr(obj).getBytes("US-ASCII"),"utf-8");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objToStr(obj);
	}
	public static String gbkToIso(String str){
		try {
			return new String(doNull(str).getBytes("GBK"),"iso-8859-1");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String isoToGbk(String str){
		try {
			return new String(doNull(str).getBytes("iso-8859-1"),"GBK");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String isoToutf8(String str){
		try {
			return new String(doNull(str).getBytes("iso-8859-1"),"utf-8");	
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String utf8Toiso(String str){
		try {
			return new String(doNull(str).getBytes("utf-8"),"iso-8859-1");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String gbkToutf8(String str){
		try {
			return new String(doNull(str).getBytes("GBK"),"utf-8");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String utf8ToGbk(String str){
		try {
			return new String(doNull(str).getBytes("utf-8"),"GBK");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String utf8ToAscII(String str){
		try {
			return new String(doNull(str).getBytes("utf-8"),"US-ASCII");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String AscIIToUtf8(String str){
		try {
			return new String(doNull(str).getBytes("US-ASCII"),"utf-8");
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	public static String doNull(String str){
		return str == null ? "" : str;
	}
	public static String objToStr(Object obj){
		return obj == null ? "" : obj.toString();
	}
	/**
	* @function 重写isEmpty
	 * @edit by HAI
	 */
	public static boolean isEmpty(String str){
		return str == null || str.trim().length() == 0 ? true : false;
	}
	/**
	 * @function 对List进行批转码
	 * @edit by HAI
	 */
	public static List<Map<String, Object>> getListChange(List<Map<String, Object>> listm){
		try {
			if(listm!=null && listm.size()>0){
				for(Map<String, Object> map1 : listm){
					for(String str :map1.keySet()){
						map1.put(str, anyToGbk(String.valueOf(map1.get(str)==null?"":map1.get(str))));
					}
				}
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}		
		return listm;
	}
	/**
	 * @function 常用编码转换iso
	 * <AUTHOR>
	 */
	public static String anyToIso(String str){
		if(isEmpty(str)){
			return "";
		}
		try {
			String encode = getEncoded(str);
			if(encode != null && encode.length()>0){
				return new String(str.getBytes(encode),"ISO-8859-1");
			}else{
				return str;
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	/**
	 * @function 常用编码转换GKB
	 * <AUTHOR>
	 */
	public static String anyToGbk(String str){
		if(isEmpty(str)){
			return "";
		}
		try {
			String encode = getEncoded(str);
			if(encode != null && encode.length()>0){
				return new String(str.getBytes(encode),"GBK");
			}else{
				return str;
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return str;
	}
	/**
	 * @function 常用编码转换iso
	 * <AUTHOR>
	 */
	public static String anyToIso(Object obj){
		String objstr = objToStr(obj);
		try {
			String encode = getEncoded(objstr);
			if(encode != null && encode.length()>0){
				return new String(objstr.getBytes(encode),"ISO-8859-1");
			}else{
				return objstr;
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objstr;
	}
	/**
	 * @function 常用编码转换GKB
	 * <AUTHOR>
	 */
	public static String anyToGbk(Object obj){
		String objstr = objToStr(obj);
		try {
			String encode = getEncoded(objstr);
			if(encode != null && encode.length()>0){
				return new String(objstr.getBytes(encode),"GBK");
			}else{
				return objstr;
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return objstr;
	}
	/**
	 * @function 枚举获取字符串编码
	 * <AUTHOR>
	 */
	public static String getEncoded(String str){
		try {
			if(str.equals(new String(str.getBytes("GBK"),"GBK"))){
				return "GBK";
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		try {
			if(str.equals(new String(str.getBytes("ISO-8859-1"),"ISO-8859-1"))){
				return "ISO-8859-1";
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		try {
			if(str.equals(new String(str.getBytes("UTF-8"),"UTF-8"))){
				return "UTF-8";
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		try {
			if(str.equals(new String(str.getBytes("GB2312"),"GB2312"))){
				return "GB2312";
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		try {
			if(str.equals(new String(str.getBytes("US-ASCII"),"US-ASCII"))){
				return "GB2312";
			}
		} catch (Exception e) {
			System.out.println("报告！出现了异常！");
		}
		return null;
	}

	/**
	 * 判断是否完全包含
	 * @param input
	 * @param target
	 * @return
	 */
	public static boolean isFullContains(String input,String target){
		if(StringUtils.isBlank(input) || StringUtils.isBlank(target)){
			return false;
		}
		List<String> items = Arrays.asList(StringUtils.split(input, ','));
		return items.contains(target);
	}
}
