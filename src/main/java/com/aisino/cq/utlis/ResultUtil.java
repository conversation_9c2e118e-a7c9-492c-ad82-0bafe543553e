package com.aisino.cq.utlis;

import java.util.ArrayList;
import java.util.Map;

public class ResultUtil {
    public static final String MsgFail = "0";
    public static final String MsgSucc = "1";
    private String code = "1";
    private int pageIndex = 1;
    private int pageSize = 10000;
    private long total ;
    private String msg ="成功";
    private Object data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public int getPageIndex()
    {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex)
    {
        this.pageIndex = pageIndex;
    }

    public int getPageSize()
    {
        return pageSize;
    }

    public void setPageSize(int pageSize)
    {
        this.pageSize = pageSize;
    }

    public long getTotal()
    {
        return total;
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public void setMsgInfo(String code, String msg){
            this.code = code;
            this.msg = msg;
    }



    public void setErrorInfo(){
        this.code = "0";
        this.msg = "失败";
        /* 2019.11.27 测试2.0接口新增 */
        this.data = new ArrayList<Map<String, Object>>();
    }

    @Override
    public String toString() {
        return "{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
