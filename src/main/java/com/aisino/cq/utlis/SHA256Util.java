package com.aisino.cq.utlis;

import java.security.MessageDigest;

/**
 * @ClassName : SHA256Util
 * @Description :
 * <AUTHOR> 码哥
 * @Date : 2020-01-19  10:15
 */
public class SHA256Util
{
    /**
     * SHA256加密
     * @param str
     * @return
     */
    public static String getSHA256(String str){
        MessageDigest messageDigest;
        String encodestr = "";

        try{
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodestr = byte2Hex(messageDigest.digest());

        }catch (Exception e){
            e.printStackTrace();
        }
        return encodestr.toUpperCase();
    }


    /**
     * 转换为16进制
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes){
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i=0;i<bytes.length;i++){
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1){
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }


    public static void main(String[] args) {
        String str = "12345678camin";
        System.out.println(getSHA256(str));
    }

}
