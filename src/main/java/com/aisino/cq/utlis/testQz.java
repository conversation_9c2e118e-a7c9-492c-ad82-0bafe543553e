package com.aisino.cq.utlis;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

public class testQz {
    public static void main(String[] args) {
        FileOutputStream outputStream = null;
        try {
            String courseFile = new File("").getCanonicalPath() + "\\src\\main\\resources\\";
            byte[] fileByte = Files.readAllBytes(Paths.get(courseFile+"1111111111.pdf"));
            List<float[]> positions = ItextUtil.findKeywordPostions(fileByte,"那么准确");
            System.out.println(positions);
            System.out.println("发现关键字总条数：" + positions.size());
            System.out.println("最后一次出现关键字的位置信息：页码=" + (int)positions.get(positions.size()-1)[0] +"，X轴=" + positions.get(positions.size()-1)[1] + "，Y轴=" + positions.get(positions.size()-1)[2]);
            ByteArrayOutputStream result = ItextUtil.signature(fileByte,courseFile+"cqgxjscykfq-ssywzyz.png",courseFile+"cqgxjscykfq-ssywzyz.p12",positions.get(positions.size()-1)[1],positions.get(positions.size()-1)[2],100,100,(int)positions.get(positions.size()-1)[0]);

            outputStream = new FileOutputStream(new File(courseFile+ System.currentTimeMillis()+".pdf"));
            outputStream.write(result.toByteArray());
            outputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(outputStream != null){
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
