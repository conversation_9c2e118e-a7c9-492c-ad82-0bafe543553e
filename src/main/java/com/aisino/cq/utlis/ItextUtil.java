package com.aisino.cq.utlis;

import com.aisino.cq.clf.util.CommonResult;
import com.itextpdf.awt.geom.Rectangle2D.Float;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.itextpdf.text.pdf.parser.*;
import com.itextpdf.text.pdf.security.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

public class ItextUtil {
    private static Logger log = LoggerFactory.getLogger(ItextUtil.class);
    private static final char[] PASSWORD = "aisino95113".toCharArray();//keystory密码

    private static class PdfRenderListener implements RenderListener {
        private int pageNum;
        private StringBuilder contentBuilder = new StringBuilder();
        private List<CharPosition> charPositions = new ArrayList<>();
        public PdfRenderListener(int pageNum) {
            this.pageNum = pageNum;
        }
        public void beginTextBlock() {
        }
        public void renderText(TextRenderInfo renderInfo) {
            List<TextRenderInfo> characterRenderInfos = renderInfo.getCharacterRenderInfos();
            for (TextRenderInfo textRenderInfo : characterRenderInfos) {
                String word = textRenderInfo.getText();
                if (word.length() > 1) {
                    word = word.substring(word.length() - 1, word.length());
                }
                Float rectangle = textRenderInfo.getAscentLine().getBoundingRectange();
                float x = (float)rectangle.getX();
                float y = (float)rectangle.getY();
                CharPosition charPosition = new CharPosition(pageNum, (float)x, (float)y);
                charPositions.add(charPosition);
                contentBuilder.append(word);
            }
        }
        public void endTextBlock() {
        }
        public void renderImage(ImageRenderInfo renderInfo) {
        }
        public String getContent() {
            return contentBuilder.toString();
        }
        public List<CharPosition> getcharPositions() {
            return charPositions;
        }
    }

    private static class PdfPageContentPositions {
        private String content;
        private List<float[]> positions;
        public String getContent() {
            return content;
        }
        public void setContent(String content) {
            this.content = content;
        }
        public List<float[]> getPositions() {
            return positions;
        }
        public void setPostions(List<float[]> positions) {
            this.positions = positions;
        }
    }
    /**
     * 查找关键字集合
     */
    public static List<float[]> findKeywordPostions(byte[] pdfData, String keyword) {
        List<float[]> result = new ArrayList<>();
        List<PdfPageContentPositions> pdfPageContentPositions = getPdfContentPostionsList(pdfData);
        for (PdfPageContentPositions pdfPageContentPosition : pdfPageContentPositions) {
            List<float[]> charPositions = findPositions(keyword, pdfPageContentPosition);
            if (charPositions == null || charPositions.size() < 1) {
                continue;
            }
            result.addAll(charPositions);
        }
        return result;
    }
    /**
     * 读取文件内容
     */
    private static List<PdfPageContentPositions> getPdfContentPostionsList(byte[] pdfData) {
        try {
            PdfReader reader = new PdfReader(pdfData);
            List<PdfPageContentPositions> result = new ArrayList<>();
            int pages = reader.getNumberOfPages();
            for (int pageNum = 1; pageNum <= pages; pageNum++) {
                PdfRenderListener pdfRenderListener = new PdfRenderListener(pageNum);
                //解析pdf，定位位置
                PdfContentStreamProcessor processor = new PdfContentStreamProcessor(pdfRenderListener);
                PdfDictionary pageDic = reader.getPageN(pageNum);
                PdfDictionary resourcesDic = pageDic.getAsDict(PdfName.RESOURCES);
                try {
                    processor.processContent(ContentByteUtils.getContentBytesForPage(reader, pageNum), resourcesDic);
                } catch (Exception e) {
                    reader.close();
                    log.error("读取文件内容发生未知异常：" + e);
                }
                String content = pdfRenderListener.getContent();
                List<CharPosition> charPositions = pdfRenderListener.getcharPositions();
                List<float[]> positionsList = new ArrayList<>();
                for (CharPosition charPosition : charPositions) {
                    float[] positions = new float[]{charPosition.getPageNum(), charPosition.getX(), charPosition.getY()};
                    positionsList.add(positions);
                }
                PdfPageContentPositions pdfPageContentPositions = new PdfPageContentPositions();
                pdfPageContentPositions.setContent(content);
                pdfPageContentPositions.setPostions(positionsList);
                result.add(pdfPageContentPositions);
            }
            reader.close();
            return result;
        } catch (Exception e) {
            log.error("查找关键字发生未知异常：" + e);
            return null;
        }
    }

    /**
     * 定位关键字位置
     */
    private static List<float[]> findPositions(String keyword, PdfPageContentPositions pdfPageContentPositions) {
        List<float[]> result = new ArrayList<>();
        String content = pdfPageContentPositions.getContent();
        List<float[]> charPositions = pdfPageContentPositions.getPositions();
        for (int pos = 0; pos < content.length(); ) {
            int positionIndex = content.indexOf(keyword, pos);
            if (positionIndex == -1) {
                break;
            }
            float[] postions = charPositions.get(positionIndex);
            result.add(postions);
            pos = positionIndex + 1;
        }
        return result;
    }

    static class CharPosition{
        private int pageNum = 0;
        private float x = 0;
        private float y = 0;

        public CharPosition(int pageNum, float x, float y) {
            this.pageNum = pageNum;
            this.x = x;
            this.y = y;
        }
        public int getPageNum() {
            return pageNum;
        }
        public float getX() {
            return x;
        }
        public float getY() {
            return y;
        }
        @Override
        public String toString() {
            return "[pageNum=" + this.pageNum + ",x=" + this.x + ",y=" + this.y + "]";
        }
    }

    /**
     * 单多次签章通用
     */
    public static ByteArrayOutputStream signature(byte[] srcFile, String imagePath,String p12Path,float pdfX,float pdfY,float imageWidwh,float imageHeight,int pageIndex){
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        try {
             //将证书文件放入指定路径，并读取keystore ，获得私钥和证书链
             KeyStore ks = KeyStore.getInstance("PKCS12");
             ks.load(new FileInputStream(p12Path), PASSWORD);
             String alias = ks.aliases().nextElement();
             PrivateKey pk = (PrivateKey) ks.getKey(alias, PASSWORD);
             Certificate[] chain = ks.getCertificateChain(alias);

            PdfReader reader = new PdfReader(srcFile);
            //创建签章工具PdfStamper ，最后一个boolean参数是否允许被追加签名
            PdfStamper stamper = PdfStamper.createSignature(reader, result, '\0', null, true);
            // 获取数字签章属性对象
            PdfSignatureAppearance appearance = stamper.getSignatureAppearance();
            appearance.setReason("");//签章原因
            appearance.setLocation("");//位置
            //设置签名的签名域名称，多次追加签名的时候，签名预名称不能一样，图片大小受表单域大小影响（过小导致压缩）
            appearance.setVisibleSignature(new Rectangle(pdfX-imageWidwh/2, pdfY-imageHeight/2, pdfX+imageWidwh/2, pdfY+imageHeight/2), pageIndex, UUID.randomUUID().toString().replaceAll("-",""));
            //读取图章图片
            Image image = Image.getInstance(imagePath);
            appearance.setSignatureGraphic(image);
            appearance.setCertificationLevel(PdfSignatureAppearance.CERTIFIED_NO_CHANGES_ALLOWED);
            //设置图章的显示方式，如下选择的是只显示图章（还有其他的模式，可以图章和签名描述一同显示）
            appearance.setRenderingMode(PdfSignatureAppearance.RenderingMode.GRAPHIC);
            // 摘要算法
            ExternalDigest digest = new BouncyCastleDigest();
            // 签名算法
            ExternalSignature signature = new PrivateKeySignature(pk, DigestAlgorithms.SHA1, null);
            // 调用itext签名方法完成pdf签章
            MakeSignature.signDetached(appearance, digest, signature, chain, null, null, null, 0, MakeSignature.CryptoStandard.CMS);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(null!=result){
                    result.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
		return result;
    }

    public static CommonResult qz(String base64File,int page,int pdfX,int pdfY,String qzid) {
        CommonResult result = new CommonResult();
        try {
            String courseFile = new File("").getCanonicalPath() + "\\src\\main\\resources\\";
            byte[] fileByte = Base64.getDecoder().decode(base64File);
            ByteArrayOutputStream os = ItextUtil.signature(fileByte,courseFile+qzid+".png",courseFile+qzid+".p12",pdfX,pdfY,100,100,page);
            result.setSuccess(Base64.getEncoder().encodeToString(os.toByteArray()));
        } catch (Exception e) {
            e.printStackTrace();
            result.setFail("调用签章出现异常："+e.getMessage());
        }
        return result;
    }

    public static CommonResult qz(String base64File,String keyWord,String qzid) {
        CommonResult result = new CommonResult();
        try {
            byte[] fileByte = Base64.getDecoder().decode(base64File);
            List<float[]> positions = ItextUtil.findKeywordPostions(fileByte,keyWord);
            System.out.println(positions);
            System.out.println("发现关键字总条数：" + positions.size());
            System.out.println("最后一次出现关键字的位置信息：页码=" + (int)positions.get(positions.size()-1)[0] +"，X轴=" + positions.get(positions.size()-1)[1] + "，Y轴=" + positions.get(positions.size()-1)[2]);
            result = qz(base64File, (int)positions.get(positions.size()-1)[0],(int)positions.get(positions.size()-1)[1],(int)positions.get(positions.size()-1)[2],qzid);
        } catch (Exception e) {
            e.printStackTrace();
            result.setFail("调用签章出现异常："+e.getMessage());
        }
        return result;
    }
}
