package com.aisino.cq.utlis;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil
{


    /**
     * 某年的1月1号
     * @param year
     * @return
     */
    public static String getFirstDayDate(String year){
        String lastDay = null;
        try
        {
            int iYear = Integer.parseInt(year);
            Calendar ca =Calendar.getInstance();
            ca.clear();
            ca.set(Calendar.YEAR,iYear);
            Date lastDateDay = ca.getTime();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            lastDay = sf.format(lastDateDay);
            return lastDay;
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return lastDay;
    }

    /**
     * 某年的12月31号
     * @param year
     * @return
     */
    public static String getLastDayDate(String year){
        String lastDay = null;
        try
        {
            int iYear = Integer.parseInt(year);
            Calendar ca =Calendar.getInstance();
            ca.clear();
            ca.set(Calendar.YEAR,iYear);
            ca.roll(Calendar.DAY_OF_YEAR,-1);
            Date lastDateDay = ca.getTime();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            lastDay = sf.format(lastDateDay);
            return lastDay;
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return lastDay;
    }

    public static boolean compareDate(String srcDate,String destDate){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try
        {
            Date d1 = sf.parse(srcDate);
            Date d2 = sf.parse(destDate);
            return d1.before(d2);
        } catch (ParseException e)
        {
            e.printStackTrace();
        }
        return false;
    }

    public static void main(String[] args)
    {
        System.out.println(compareDate("2018-12-01","2018-02-01"));
    }
}
