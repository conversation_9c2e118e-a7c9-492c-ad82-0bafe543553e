package com.aisino.cq.utlis;

import java.security.MessageDigest;

public class Md5Util
{
    public String bytesToHex(final byte[] inputBytes) {
        final StringBuffer md5str = new StringBuffer();
        for (int i = 0; i < inputBytes.length; ++i) {
            int digital = inputBytes[i];
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }
    
    public String bytesToMD5(final byte[] inputByte) {
        String md5str = null;
        try {
            final MessageDigest md = MessageDigest.getInstance("MD5");
            final byte[] buff = md.digest(inputByte);
            md5str = this.bytesToHex(buff);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return md5str;
    }
    
    public static void main(final String[] args) {
        final Md5Util t = new Md5Util();
        System.out.println(t.bytesToMD5("111111".getBytes()));
    }
}
