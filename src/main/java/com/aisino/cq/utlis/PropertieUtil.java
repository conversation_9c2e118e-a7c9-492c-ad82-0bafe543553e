 package com.aisino.cq.utlis;
 
 import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.concurrent.ConcurrentHashMap;

 public class PropertieUtil{
   private static Hashtable params = new Hashtable();
   private static Map<String,Hashtable> cachePropertieParam = new ConcurrentHashMap<String,Hashtable>(); 
   private static PropertieUtil propertieUtil = null;	 
   private PropertieUtil(){};
   /**
    * @function 初始化配置文件
    * @param propertieName
    * <AUTHOR>
    */
   public static PropertieUtil getIns(String propertieName){
	   if(propertieUtil==null){
		   propertieUtil = new PropertieUtil();
	   }
	   params = cachePropertieParam.get(propertieName);
	   if(params==null || params.size() == 0){
		   propertieUtil.initPropertie(propertieName);
	   }
	   return propertieUtil;
   }
   
   /**
    * @function 初始化配置文件
    * @param propertieName
    * <AUTHOR>
    */
   private void initPropertie(String propertieName){
    	params = new Hashtable();
	    ResourceBundle bundle = ResourceBundle.getBundle(propertieName);
        Enumeration keys = bundle.getKeys();
        while (keys.hasMoreElements()) {
          String key = (String)keys.nextElement();
          String value = bundle.getString(key);
          params.put(key, value);
        }
        cachePropertieParam.put(propertieName, params);
   }
   /**
    * @function 获取配置文件属性
    * @param name
    * <AUTHOR>
    */
   public String getStringProp(String name) {
	     if (params!=null && params.containsKey(name)) {
	       return (String)params.get(name);
	     }
	     return null;
   }

     public static void main(String[] args){
         System.out.println(PropertieUtil.getIns("application-dev").getStringProp("spring.datasource.druid.type"));
     }
}
