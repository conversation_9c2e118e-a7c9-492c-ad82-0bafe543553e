package com.aisino.cq.lzlg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

//"管理后台 - 漏征漏管清单信息主表数据")
@RestController
@RequestMapping("/lzlg/lzlg")
public class LzlgqdxxzbsjController {
    private static  final Logger log = LoggerFactory.getLogger(LzlgqdxxzbsjController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("漏征漏管清单-列表");

        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String dwzcmc = param.get("dwzcmc");
        String tyshxydm = param.get("tyshxydm");
        String fddbr = param.get("fddbr");
        String kzztdjlx = param.get("kzztdjlx");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"dwzcmc", dwzcmc, "string");
        esbService.addParam(paramList,"tyshxydm", tyshxydm, "string");
        esbService.addParam(paramList,"fddbr", fddbr, "string");
        esbService.addParam(paramList,"kzztdjlx", kzztdjlx, "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000091",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/tb")
    @ResponseBody
    public Object tb(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("漏征漏管清单-图表");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String nd = param.get("nd");
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"sfjxswdj", "否", "string");
        esbService.addParam(paramList,"tjrq", nd, "string");
        CommonResult resultLzlg = esbService.queryEsb("30000099",paramList);
        JSONArray rowsLzlg = new JSONArray();
        if (null != resultLzlg.getData()){
            rowsLzlg = (JSONArray)resultLzlg.getData();
        }

        esbService.addParam(paramList,"sfjxswdj", "是", "string");
        esbService.addParam(paramList,"tjrq", nd, "string");
        CommonResult resultYdj = esbService.queryEsb("30000099",paramList);
        JSONArray rowsYdj = new JSONArray();
        if (null != resultYdj.getData()){
            rowsYdj = (JSONArray)resultYdj.getData();
        }

        String[][] tb = new String[12][12];
        int sort = 11;
        for (int i = 1; i <13 ; i++) {
            String yf = nd;
            if (i<10) {
                yf +="-0"+i;
            } else {
                yf +="-"+i;
            }
            tb[0][i-1] = "0";
            for (Object obj : rowsLzlg) {
                JSONObject jo = (JSONObject) obj;
                if (yf.equals(jo.getString("yf"))) {
                    tb[0][i-1] = jo.getString("sl");
                }
            }
            tb[1][i-1] = "0";
            for (Object obj : rowsYdj) {
                JSONObject jo = (JSONObject) obj;
                if (yf.equals(jo.getString("yf"))) {
                    tb[1][i-1] = jo.getString("sl");
                }
            }
        }

        result.setSuccess(tb);
        return result;
    }

    @PostMapping("/ycsjgzlb")
    @ResponseBody
    public Object ycsjgzlb(HttpServletRequest request) {
        log.info("漏征漏管清单-异常数据告知");

        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"sfycsj", "是", "string");
        esbService.addParam(paramList,"sftsgs", "否", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000091",pageNo, pageSize,"0","false",paramList);
    }

    @PostMapping("/tsgs")
    @ResponseBody
    public Object tsgs(HttpServletRequest request) {
        log.info("漏征漏管-异常数据告知-推送工商");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"sfycsj", "是", "string");
        esbService.addParam(paramList,"sftsgs", "否", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        result = esbService.queryEsb("30000091",pageNo, pageSize,"0","false",paramList);
        if (null == result.getData()){
            return result;
        }
        JSONArray rows = (JSONArray)result.getData();

        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
//            先推送，再处理推送状态
//            TODO 推送工商

//            漏征漏管清单信息主表数据 - 编辑
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", jo.getString("uuid"), "string");
            esbService.addParam(paramList,"sftsgs", "是", "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            result = esbService.queryEsb("30000090", pageNo, pageSize,"0","true", paramList);
        }
        result.setSuccess(null);
        return result;
    }

    @PostMapping("/djjglb")
    @ResponseBody
    public Object djjglb(HttpServletRequest request) {
        log.info("漏征漏管清单-登记结果");
        CommonResult result = new CommonResult();
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"djrwzt", "已完成", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        return esbService.queryEsb("30000095",pageNo, pageSize,"0","false",paramList);
    }

    @PostMapping("/djztgx")
    @ResponseBody
    public Object djztgx(HttpServletRequest request) {
        log.info("漏征漏管-登记结果-登记状态更新");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"zdjrwzt", "已下发", "string");
        esbService.addParam(paramList,"djrwzt", "已完成", "string");
        esbService.addParam(paramList,"yxbz", "Y", "string");
        result = esbService.queryEsb("30000095",pageNo, pageSize,"0","false",paramList);
        if (null == result.getData()){
            return result;
        }
        JSONArray rows = (JSONArray)result.getData();

        for (Object obj: rows) {
            JSONObject jo = (JSONObject) obj;
//            漏征漏管清单信息主表数据 - 编辑
            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", jo.getString("zbuuid"), "string");
            esbService.addParam(paramList,"sftsgs", jo.getString("sftsgs"), "string");
            esbService.addParam(paramList,"sfjxswdj", "是", "string");
            esbService.addParam(paramList,"djrwzt", "已完成", "string");
            esbService.addParam(paramList,"xgrdm", userCode, "string");
            result = esbService.queryEsb("30000090", pageNo, pageSize,"0","true", paramList);
        }
        result.setSuccess(null);
        return result;
    }

    @PostMapping("/djrwxf")
    @ResponseBody
    public Object zzrwxf(HttpServletRequest request) {
        log.info("漏征漏管-登记任务下发");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
            String zbuuid = null;
//            漏征漏管清单信息主表数据 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000091", paramList);
            JSONObject lzlg = null;
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                lzlg = ((JSONArray)result.getData()).getJSONObject(0);
            } else {
                continue;
            }
            if (!"未下发".equals(lzlg.getString("djrwzt"))){
//                continue;
            }
//            漏征漏管清单信息主表数据 - 编辑
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "djrwzt", "已下发", "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            result = esbService.queryEsb("30000090", paramList);

//            漏征漏管清单下发及反馈信息表数据 - 新增
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", IdUtil.simpleUUID(), "string");
            esbService.addParam(paramList, "zbuuid", uuid, "string");

            esbService.addParam(paramList, "xfr", userCode, "string");
            esbService.addParam(paramList, "xfrq", DateUtils.strNow(), "string");
            esbService.addParam(paramList, "xfbz", "null", "string");
            esbService.addParam(paramList, "fkr", "null", "string");
            esbService.addParam(paramList, "fkrq", "null", "string");
            esbService.addParam(paramList, "fkbz", "null", "string");
            esbService.addParam(paramList, "djrwwcrq", "null", "string");
            esbService.addParam(paramList, "djjg", "null", "string");
            esbService.addParam(paramList, "djry", "null", "string");
            esbService.addParam(paramList, "djrwzt", "处理中", "string");
            esbService.addParam(paramList, "lrrdm", userCode, "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            esbService.addParam(paramList, "yxbz", "Y", "string");
            result = esbService.queryEsb("30000092", paramList);
        }

        result.setSuccess(null);
        return result;
    }

    @PostMapping("/update")
    @ResponseBody
    public Object update(HttpServletRequest request) {
        log.info("漏征漏管-編輯");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String uuid = param.get("uuid");
        String bz = param.get("bz");
        List<Map<String,Object>> paramList = new ArrayList<>();
//        漏征漏管清单信息主表数据 - 编辑
        esbService.addParam(paramList,"uuid", uuid, "string");
        esbService.addParam(paramList,"bz", bz, "string");
        result = esbService.queryEsb("30000090", paramList);
        return result;
    }


    /**
     * function 漏征漏管-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletResponse response){
        log.info("漏征漏管-导出: " + jsonData );

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String dwzcmc = param.getString("dwzcmc");
            String tyshxydm = param.getString("tyshxydm");
            String fddbr = param.getString("fddbr");
            String kzztdjlx = param.getString("kzztdjlx");

            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"dwzcmc", dwzcmc, "string");
            esbService.addParam(paramList,"tyshxydm", tyshxydm, "string");
            esbService.addParam(paramList,"fddbr", fddbr, "string");
            esbService.addParam(paramList,"kzztdjlx", kzztdjlx, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000091", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "统计日期", "单位注册名称", "统一社会信用代码", "纳税人状态", "注册地", "经营范围", "法定代表人", "联系方式", "课征主体登记类型", "员工数量", "营业期限", "仓储物品", "主管税务科所", "街道乡镇", "税收管理员", "登记任务状态", "是否进行税务登记", "登记结果", "备注" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("tjrq"));
                    r.add(jo.getString("dwzcmc"));
                    r.add(jo.getString("tyshxydm"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("zcd"));
                    r.add(jo.getString("jyfw"));
                    r.add(jo.getString("fddbr"));
                    r.add(jo.getString("lxfs"));
                    r.add(jo.getString("kzztdjlx"));
                    r.add(jo.getString("ygsl"));
                    r.add(jo.getString("yyqx"));
                    r.add(jo.getString("ccwp"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("djrwzt"));
                    r.add(jo.getString("sfjxswdj"));
                    r.add(jo.getString("djjg"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("漏征漏管", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("漏征漏管-导出失败", e);
        }
    }









//    @RequestMapping("add")
//    @ResponseBody
    public Object add(HttpServletRequest request) {
        log.info("漏征漏管清单-新增数据");
        String tt = DateUtils.strNow("yyMMddHHmmss");
        String userCode = (String)request.getSession().getAttribute("usercode");
        List<Map<String, Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid", IdUtil.simpleUUID(), "string"));
//        paramList.add(esbService.addParam("tjrq", DateUtils.strNow(), "string"));
        paramList.add(esbService.addParam("tjrq", "2025-03-21", "string"));
        paramList.add(esbService.addParam("dwzcmc", "dwzcmc"+tt, "string"));
        paramList.add(esbService.addParam("tyshxydm", "tyshxydm"+tt, "string"));
        paramList.add(esbService.addParam("nsrzt", "nsrzt"+tt, "string"));
        paramList.add(esbService.addParam("zcd", "zcd", "string"));
        paramList.add(esbService.addParam("jyfw", "jyfw", "string"));
        paramList.add(esbService.addParam("fddbr", "fddbr", "string"));
        paramList.add(esbService.addParam("lxfs", "lxfs", "string"));
//        paramList.add(esbService.addParam("kzztdjlx", "企业", "string"));
        paramList.add(esbService.addParam("kzztdjlx", "个人", "string"));
        paramList.add(esbService.addParam("ygsl", "ygsl", "string"));
        paramList.add(esbService.addParam("yyqx", "yyqx", "string"));
        paramList.add(esbService.addParam("ccwp", "ccwp", "string"));
        paramList.add(esbService.addParam("zgswksdm", "zgswksdm", "string"));
        paramList.add(esbService.addParam("zgswks", "zgswks", "string"));
        paramList.add(esbService.addParam("jdxzdm", "jdxzdm", "string"));
        paramList.add(esbService.addParam("jdxz", "jdxz", "string"));
        paramList.add(esbService.addParam("ssglydm", "ssglydm", "string"));
        paramList.add(esbService.addParam("ssgly", "ssgly", "string"));
//        paramList.add(esbService.addParam("sfjxswdj", "否", "string"));
        paramList.add(esbService.addParam("sfjxswdj", "否", "string"));
//        paramList.add(esbService.addParam("sfycsj", "否", "string"));
        paramList.add(esbService.addParam("sfycsj", "是", "string"));
        paramList.add(esbService.addParam("sftsgs", "否", "string"));
        paramList.add(esbService.addParam("djrwzt", "未下发", "string"));
        paramList.add(esbService.addParam("djjg", "djjg", "string"));
        paramList.add(esbService.addParam("zt", "zt", "string"));
        paramList.add(esbService.addParam("bz", "bz", "string"));

        paramList.add(esbService.addParam("lrrdm", "xgrDm", "string"));
        paramList.add(esbService.addParam("xgrdm", "xgrDm", "string"));
        paramList.add(esbService.addParam("yxbz", "Y", "string"));
        return esbService.queryEsb("30000089", paramList);
    }


//        @PostMapping("/plxgshuj")
//    @ResponseBody
    public Object plxgshuj(HttpServletRequest request) {
        log.info("欠税清单-批量修改数据");
        String userCode = (String)request.getSession().getAttribute("usercode");
        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = "9999";
        String pageNo = "1";
        List<Map<String,Object>> paramList = new ArrayList<>();

        result = esbService.queryEsb("30000091",pageNo, pageSize,"0","true",paramList);
        if (null == result.getData()){
            result.setSuccess(null);
        }
        JSONArray rows = (JSONArray)result.getData();

        int tt = 0;
        for (Object obj: rows) {
            String tjrq = "2025-03-02";
            if (tt<=8){
                tjrq = "2024-04-02";
            } else if(tt>8 && tt<=16){
                tjrq = "2024-06-02";
            } else if(tt>16 && tt<=24){
                tjrq = "2024-07-02";
            } else if(tt>24 && tt<=32){
                tjrq = "2024-09-02";
            } else if(tt>32 && tt<=40){
                tjrq = "2024-12-02";
            } else if(tt>40 && tt<=48){
                tjrq = "2025-01-02";
            }
            JSONObject jo = (JSONObject) obj;
            String uuid = jo.getString("uuid");

            paramList = new ArrayList<>();
            esbService.addParam(paramList,"uuid", uuid, "string");
            paramList.add(esbService.addParam("tjrq", tjrq, "string"));
            result = esbService.queryEsb("30000090",pageNo, pageSize,"0","true",paramList);
            tt++;
        }

        return null;
    }



}