package com.aisino.cq.lzlg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aisino.cq.clf.service.DsjptService;
import com.aisino.cq.clf.service.DzzlkService;
import com.aisino.cq.clf.util.CommonResult;
import com.aisino.cq.clf.util.DateUtils;
import com.aisino.cq.clf.util.RequestUtil;
import com.aisino.cq.qyqsgl.constant.DmMaps;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

//"管理后台 - 漏征漏管清单下发及反馈信息表数据")
@RestController
@RequestMapping("/lzlg/lzlgswdj")

public class LzlgqdxfjfkxxbsjController {
    private static  final Logger log = LoggerFactory.getLogger(LzlgqdxfjfkxxbsjController.class);

    @Autowired
    private DsjptService esbService;
    @Resource
    private DzzlkService dzzlkService;
    @Value("${filePath}")
    private String filePath;

    @PostMapping("/page")
    @ResponseBody
    public Object page(HttpServletRequest request) {
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String pageSize = param.get("pageSize");
        String pageNo = param.get("pageNo");
        String dwzcmc = param.get("dwzcmc");
        String tyshxydm = param.get("tyshxydm");
        String fddbr = param.get("fddbr");
        String kzztdjlx = param.get("kzztdjlx");

        List<Map<String,Object>> paramList = new ArrayList<>();

        esbService.addParam(paramList,"dwzcmc", dwzcmc, "string");
        esbService.addParam(paramList,"tyshxydm", tyshxydm, "string");
        esbService.addParam(paramList,"fddbr", fddbr, "string");
        esbService.addParam(paramList,"kzztdjlx", kzztdjlx, "string");
        esbService.addParam(paramList,"zgswks", zgswks, "string");
        return esbService.queryEsb("30000095",pageNo, pageSize,"0","true",paramList);
    }

    @PostMapping("/djrwfk")
    @ResponseBody
    public Object zzrwfk(HttpServletRequest request) {
        log.info("漏征漏管税务登记-登记任务反馈");
        String userCode = (String)request.getSession().getAttribute("usercode");

        CommonResult result = new CommonResult();
        Map<String,String> param = RequestUtil.getReqParamMap(request);
        String djrwwcrq = param.get("djrwwcrq");
        String djrwzt = param.get("djrwzt");
        String djjg = param.get("djjg");
        String djry = param.get("djry");
        List<Map<String,Object>> paramList = new ArrayList<>();

        String[] ids = param.get("bz").split(",");
        for (String uuid: ids) {
            String zbuuid = null;
//            漏征漏管清单下发及反馈信息表数据 - 查询
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            result = esbService.queryEsb("30000094", paramList);
            JSONObject lzlg = null;
            if (null != result.getData()
                    && ((JSONArray)result.getData()).size()>0){
                lzlg = ((JSONArray)result.getData()).getJSONObject(0);
            } else {
                continue;
            }
            if (!"处理中".equals(lzlg.getString("djrwzt"))){
                continue;
            }
//            漏征漏管清单下发及反馈信息表数据 - 编辑
            paramList = new ArrayList<>();
            esbService.addParam(paramList, "uuid", uuid, "string");
            esbService.addParam(paramList, "fkr", userCode, "string");
            esbService.addParam(paramList, "fkrq", DateUtils.strNow(), "string");

            esbService.addParam(paramList, "djrwwcrq", djrwwcrq, "string");
            esbService.addParam(paramList, "djrwzt", djrwzt, "string");
            esbService.addParam(paramList, "djjg", djjg, "string");
            esbService.addParam(paramList, "djry", djry, "string");
            esbService.addParam(paramList, "xgrdm", userCode, "string");
            result = esbService.queryEsb("30000093", paramList);

        }

        result.setSuccess(null);
        return result;
    }

    /**
     * function 漏征漏管税务登记-导出
     */
    @RequestMapping("dcFn")
    @ResponseBody
    public void dcFn(String jsonData, HttpServletRequest request, HttpServletResponse response){
        log.info("漏征漏管税务登记-导出: " + jsonData );
        String swjgdm = (String)request.getSession().getAttribute("swjgdm");
        String zgswks = DmMaps.swjgdm.get(swjgdm);

        try{
            JSONObject param = JSONObject.parseObject(URLDecoder.decode(jsonData.replaceAll("：:",":"),"UTF-8"));
            String dwzcmc = param.getString("dwzcmc");
            String tyshxydm = param.getString("tyshxydm");
            String fddbr = param.getString("fddbr");
            String kzztdjlx = param.getString("kzztdjlx");

            List<Map<String,Object>> paramList = new ArrayList<>();

            esbService.addParam(paramList,"dwzcmc", dwzcmc, "string");
            esbService.addParam(paramList,"tyshxydm", tyshxydm, "string");
            esbService.addParam(paramList,"fddbr", fddbr, "string");
            esbService.addParam(paramList,"kzztdjlx", kzztdjlx, "string");
            esbService.addParam(paramList,"zgswks", zgswks, "string");
            esbService.addParam(paramList,"yxbz", "Y", "string");
            CommonResult result = esbService.queryEsb("30000095", paramList);

            List<List<String>> sheetRows = new ArrayList<>();
            List<String> headers = CollUtil.newArrayList("序号", "统计日期", "单位注册名称", "统一社会信用代码", "纳税人状态", "注册地", "经营范围", "法定代表人", "联系方式", "课征主体登记类型", "员工数量", "营业期限", "仓储物品", "主管税务科所", "街道乡镇", "税收管理员", "登记任务下发日期", "登记任务完成日期", "登记任务状态", "是否进行税务登记", "登记结果", "备注" );
            sheetRows.add(headers);
            if (null == result.getData() || ((JSONArray)result.getData()).size()==0){

            } else {
                JSONArray rows = (JSONArray)result.getData();
                int xh = 0;
                for (Object obj: rows) {
                    xh++;
                    JSONObject jo = (JSONObject) obj;
                    List<String> r = new ArrayList<>();
                    r.add(xh+"");
                    r.add(jo.getString("tjrq"));
                    r.add(jo.getString("dwzcmc"));
                    r.add(jo.getString("tyshxydm"));
                    r.add(jo.getString("nsrzt"));
                    r.add(jo.getString("zcd"));
                    r.add(jo.getString("jyfw"));
                    r.add(jo.getString("fddbr"));
                    r.add(jo.getString("lxfs"));
                    r.add(jo.getString("kzztdjlx"));
                    r.add(jo.getString("ygsl"));
                    r.add(jo.getString("yyqx"));
                    r.add(jo.getString("ccwp"));
                    r.add(jo.getString("zgswks"));
                    r.add(jo.getString("jdxz"));
                    r.add(jo.getString("ssgly"));
                    r.add(jo.getString("djrwxfrq"));
                    r.add(jo.getString("djrwwcrq"));
                    r.add(jo.getString("djrwzt"));
                    r.add(jo.getString("sfjxswdj"));
                    r.add(jo.getString("djjg"));
                    r.add(jo.getString("bz"));
                    sheetRows.add(r);
                }
            }
            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(sheetRows,true);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            String fileName = URLEncoder.encode("漏征漏管税务登记", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName + ".xlsx");
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            out.close();
            writer.close();

        }catch (Exception e){
            log.error("漏征漏管税务登记-导出失败", e);
        }
    }


}