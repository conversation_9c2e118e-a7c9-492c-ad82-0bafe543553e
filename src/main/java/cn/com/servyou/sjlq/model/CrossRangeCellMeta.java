package cn.com.servyou.sjlq.model;

/**
 * table转excel
 * 跨行元素元数据
 *
 */
public class CrossRangeCellMeta {
    public CrossRangeCellMeta(int firstRowIndex, int firstColIndex, int rowSpan, int colSpan) {
        super();
        this.firstRowIndex = firstRowIndex;
        this.firstColIndex = firstColIndex;
        this.rowSpan = rowSpan;
        this.colSpan = colSpan;
    }

    private int firstRowIndex;
    private int firstColIndex;
    private int rowSpan;// 跨越行数
    private int colSpan;// 跨越列数

    public int getFirstRow() {
        return firstRowIndex;
    }

    public int getLastRow() {
        if (rowSpan > 0){
            return firstRowIndex + rowSpan - 1;
        } else{
            return firstRowIndex + rowSpan;
        }
    }

    public int getFirstCol() {
        return firstColIndex;
    }

    public int getLastCol() {
        if (colSpan > 0){
            return firstColIndex + colSpan - 1;
        } else {
            return firstColIndex + colSpan;
        }
    }

    public int getColSpan(){
        return colSpan;
    }
}
