package cn.com.servyou.sjlq.model;

import java.util.List;

/**
 * 分页结果封装类
 */
public class PageResult<T> {
    // 当前页码
    private int pageNo;
    // 每页记录数
    private int pageSize;
    // 总记录数
    private int total;
    // 总页数
    private int pages;
    // 数据列表
    private List<T> list;

    public PageResult() {
    }

    public PageResult(int pageNo, int pageSize, int total, List<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        this.pages = (total + pageSize - 1) / pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}