package cn.com.servyou.sjlq.model;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

import java.io.Serializable;

public class QueryDataVo implements Serializable {

    private static final long serialVersionUID = -4214236501903574966L;

    private String message;

    private String code;

    private Object rows;

    private int total;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    private QueryDataVo() {}

    public static QueryDataVo getQueryData(String realData) {
        QueryDataVo vo = new QueryDataVo();
        JSONObject ret = JSONObject.parseObject(realData);
        if (null == ret){
            return vo;
        }
        if (ret.containsKey("code")){
            vo.code = ret.getString("code");
        } else {
            vo.code = "";
        }
        if (ret.containsKey("message")){
            vo.message = ret.getString("message");
        } else {
            vo.message = "";
        }
        if (ret.containsKey("errorMsg") && !StringUtils.isEmpty(ret.getString("errorMsg"))){
            vo.message = ret.getString("errorMsg");
        }
        if (ret.containsKey("total")){
            vo.total = ret.getInteger("total");
        } else {
            vo.total = 0;
        }
        if (ret.containsKey("rows")) {
            vo.rows = ret.get("rows");
        } else {
            vo.rows = null;
        }
        return vo;
    }
}
