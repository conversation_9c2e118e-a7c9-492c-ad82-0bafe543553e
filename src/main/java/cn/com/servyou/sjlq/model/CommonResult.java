package cn.com.servyou.sjlq.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

public class CommonResult {
    private String code;//00为成功其他都为失败
    private String msg;
    private Object data;//当code不为00时data为null
    private int total;
    private Long time;//查询耗时ms

    public CommonResult(){}
    public CommonResult(String code, String msg){
        this.code=code;
        this.msg=msg;
    }
    public CommonResult(String code, String msg, Object data){
        this.code=code;
        this.msg=msg;
        this.data=data;
    }
    public boolean isSuccess(){
        return "00".equals(code)?true:false;
    }
    public void setSuccess(Object data){
        this.code = "00";
        this.msg = "success";
        this.data=data;
    }
    public void setFail(String msg){
        this.code = "99";
        this.msg = msg;
    }
    public void setFail(String code,String msg){
        this.code = code;
        this.msg = msg;
    }
    public void setResult(String code, String msg, Object data) {
        this.code=code;
        this.msg=msg;
        this.data=data;
    }

    public void setResult(String code, String msg, Object data,int total) {
        this.code=code;
        this.msg=msg;
        this.data=data;
        this.total=total;
    }

    public void addResult(CommonResult rt) {
        if(rt==null){
            return;
        }
        if(this.code==null || "00".equals(this.code)){
            this.code=rt.getCode();
        }
        if(rt.isSuccess()){
            this.data=rt.getData();
            this.total=this.total+rt.getTotal();
        }else{
            this.msg= StringUtils.isEmpty(this.msg)?rt.getMsg():this.msg+";"+rt.getMsg();
        }
    }
    public JSONObject zrfc(){
        JSONObject jo= new JSONObject();
        jo.put("code",this.code);
        jo.put("msg",this.msg);
        jo.put("data", JSON.toJSONString(this.data));
        jo.put("total",this.total);
        jo.put("time",this.time);
        return jo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "CommonResult{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", data='" + data + '\'' +
                ", total=" + total +
                ", time=" + time +
                '}';
    }
}
