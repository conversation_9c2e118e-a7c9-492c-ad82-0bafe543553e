package cn.com.servyou.sjlq.model.jkfw;

/**
 * 渠道-服务权限
 * */
public class XtJrQdFwQxModel {
    //渠道与服务关系
    private String QDID;
    private String FWID;
    private String CREATETIME;
    private String TC;

    private String FWIDS;

    //渠道
    private String QDDM;
    private String QDMC;

    //服务
    private String FWDM;
    private String FWMC;
    private String LX;
    private String YXBZ;


    public String check(){
        if (QDID == null || "".equals(QDID.trim())){
            return "渠道ID为空";
        }
        if (FWID == null || "".equals(FWID.trim())){
            return "服务ID为空";
        }
        if (TC == null || "".equals(TC.trim())){
            return "最高并发为空";
        }
        return null;
    }

    public String getQDID() {
        return QDID;
    }

    public void setQDID(String QDID) {
        this.QDID = QDID;
    }

    public String getFWID() {
        return FWID;
    }

    public void setFWID(String FWID) {
        this.FWID = FWID;
    }

    public String getCREATETIME() {
        return CREATETIME;
    }

    public void setCREATETIME(String CREATETIME) {
        this.CREATETIME = CREATETIME;
    }

    public String getTC() {
        return TC;
    }

    public void setTC(String TC) {
        this.TC = TC;
    }

    public String getQDDM() {
        return QDDM;
    }

    public void setQDDM(String QDDM) {
        this.QDDM = QDDM;
    }

    public String getQDMC() {
        return QDMC;
    }

    public void setQDMC(String QDMC) {
        this.QDMC = QDMC;
    }

    public String getFWDM() {
        return FWDM;
    }

    public void setFWDM(String FWDM) {
        this.FWDM = FWDM;
    }

    public String getFWMC() {
        return FWMC;
    }

    public void setFWMC(String FWMC) {
        this.FWMC = FWMC;
    }

    public String getLX() {
        return LX;
    }

    public void setLX(String LX) {
        this.LX = LX;
    }

    public String getYXBZ() {
        return YXBZ;
    }

    public void setYXBZ(String YXBZ) {
        this.YXBZ = YXBZ;
    }

    public String getFWIDS() {
        return FWIDS;
    }

    public void setFWIDS(String FWIDS) {
        this.FWIDS = FWIDS;
    }
}
