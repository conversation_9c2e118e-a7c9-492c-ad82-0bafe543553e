package cn.com.servyou.sjlq.model;

public class SqlBean {
    //序号
    private int xh;
    //代码
    private String dm;
    //名称
    private String mc;
    //口径
    private String sql;
    //结果
    private String msg;
    //更新前处理
    private String beforeupdate;
    //更新到目标表语句
    private String resultsql;
    //更新到目标表语句条数
    private String resultcount;
    //更新时间
    private String gxsj;
    //系统
    private String xtdm;
    private String xtmc;
    //运行状态 0 未运行 1运行中
    private String running;
    //运行结果 0 默认 1成功 -1失败
    private String status;
    //数据源头
    private String datasource;

    public int getXh() {
        return xh;
    }

    public void setXh(int xh) {
        this.xh = xh;
    }

    public String getDm() {
        return dm;
    }

    public void setDm(String dm) {
        this.dm = dm;
    }

    public String getMc() {
        return mc;
    }

    public void setMc(String mc) {
        this.mc = mc;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getBeforeupdate() {
        return beforeupdate;
    }

    public void setBeforeupdate(String beforeupdate) {
        this.beforeupdate = beforeupdate;
    }

    public String getResultsql() {
        return resultsql;
    }

    public void setResultsql(String resultsql) {
        this.resultsql = resultsql;
    }

    public String getResultcount() {
        return resultcount;
    }

    public void setResultcount(String resultcount) {
        this.resultcount = resultcount;
    }

    public String getGxsj() {
        return gxsj;
    }

    public void setGxsj(String gxsj) {
        this.gxsj = gxsj;
    }

    public String getXtdm() {
        return xtdm;
    }

    public void setXtdm(String xtdm) {
        this.xtdm = xtdm;
    }

    public String getXtmc() {
        return xtmc;
    }

    public void setXtmc(String xtmc) {
        this.xtmc = xtmc;
    }

    public String getRunning() {
        return running;
    }

    public void setRunning(String running) {
        this.running = running;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
}
