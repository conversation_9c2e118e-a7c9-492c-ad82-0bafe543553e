package cn.com.servyou.sjlq.model;

//执法公示
public class ZfgsDataBean {
    private String cfyj;
    private String createrName;
    private String jdswh;
    private String creater;
    private String id;
    private String cflb;
    private String xzxdrdm;
    private String orgid;
    private String updateVersion;
    private String xzxdrmc;
    private String publish;
    private String sjtbSj;
    private String createDate;
    private String ajmc;
    private String updater;
    private String swordrownum;
    private String status;
    private String cfrq;
    private String fddbrxm;
    private String updaterName;
    private String updateStatus;
    private String dataOrder;
    private String updateDate;
    private String sendDate;
    private String auditSlipId;
    private String cfsy;
    private String cfjgxq;
    private String cfjg;
    private String lrrq; //由createDate转换而来

    public String getCfyj() {
        return cfyj;
    }

    public void setCfyj(String cfyj) {
        this.cfyj = cfyj;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getJdswh() {
        return jdswh;
    }

    public void setJdswh(String jdswh) {
        this.jdswh = jdswh;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCflb() {
        return cflb;
    }

    public void setCflb(String cflb) {
        this.cflb = cflb;
    }

    public String getXzxdrdm() {
        return xzxdrdm;
    }

    public void setXzxdrdm(String xzxdrdm) {
        this.xzxdrdm = xzxdrdm;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getUpdateVersion() {
        return updateVersion;
    }

    public void setUpdateVersion(String updateVersion) {
        this.updateVersion = updateVersion;
    }

    public String getXzxdrmc() {
        return xzxdrmc;
    }

    public void setXzxdrmc(String xzxdrmc) {
        this.xzxdrmc = xzxdrmc;
    }

    public String getPublish() {
        return publish;
    }

    public void setPublish(String publish) {
        this.publish = publish;
    }

    public String getSjtbSj() {
        return sjtbSj;
    }

    public void setSjtbSj(String sjtbSj) {
        this.sjtbSj = sjtbSj;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getAjmc() {
        return ajmc;
    }

    public void setAjmc(String ajmc) {
        this.ajmc = ajmc;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getSwordrownum() {
        return swordrownum;
    }

    public void setSwordrownum(String swordrownum) {
        this.swordrownum = swordrownum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCfrq() {
        return cfrq;
    }

    public void setCfrq(String cfrq) {
        this.cfrq = cfrq;
    }

    public String getFddbrxm() {
        return fddbrxm;
    }

    public void setFddbrxm(String fddbrxm) {
        this.fddbrxm = fddbrxm;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(String updateStatus) {
        this.updateStatus = updateStatus;
    }

    public String getDataOrder() {
        return dataOrder;
    }

    public void setDataOrder(String dataOrder) {
        this.dataOrder = dataOrder;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getSendDate() {
        return sendDate;
    }

    public void setSendDate(String sendDate) {
        this.sendDate = sendDate;
    }

    public String getAuditSlipId() {
        return auditSlipId;
    }

    public void setAuditSlipId(String auditSlipId) {
        this.auditSlipId = auditSlipId;
    }

    public String getCfsy() {
        return cfsy;
    }

    public void setCfsy(String cfsy) {
        this.cfsy = cfsy;
    }

    public String getCfjgxq() {
        return cfjgxq;
    }

    public void setCfjgxq(String cfjgxq) {
        this.cfjgxq = cfjgxq;
    }

    public String getCfjg() {
        return cfjg;
    }

    public void setCfjg(String cfjg) {
        this.cfjg = cfjg;
    }

    public String getLrrq() {
        return lrrq;
    }

    public void setLrrq(String lrrq) {
        this.lrrq = lrrq;
    }
}
