package cn.com.servyou.sjlq.model.jkfw;

/**
 * 接入渠道
 * */
public class XtJrQdModel {
    private String UUID;
    private String QDDM;
    private String QDMC;
    private String APPCODE;
    private String APPNAME;
    private String COMPANYCODE;
    private String COMPANYNAME;
    private String YXBZ;
    private String PASSWORD;
    private String CREATETIME;
    private String UPDATETIME;
    private String TC;

    public String check(){
        if (UUID == null || "".equals(UUID.trim())){
            return "主键为空";
        }
        if (QDDM == null || "".equals(QDDM.trim())){
            return "渠道代码为空";
        }
        if (QDMC == null || "".equals(QDMC.trim())){
            return "渠道名称为空";
        }
        if (APPCODE == null || "".equals(APPCODE.trim())){
            return "应用代码为空";
        }
        if (APPNAME == null || "".equals(APPNAME.trim())){
            return "应用名称为空";
        }
        if (YXBZ == null || "".equals(YXBZ.trim())){
            return "有效标识为空";
        }
        if (COMPANYCODE == null || "".equals(COMPANYCODE.trim())){
            return "公司代码为空";
        }
        if (COMPANYNAME == null || "".equals(COMPANYNAME.trim())){
            return "公司名称为空";
        }
        if (TC == null || "".equals(TC.trim())){
            return "最高并发为空";
        }
        return null;
    }

    public String getUUID() {
        return UUID;
    }

    public void setUUID(String UUID) {
        this.UUID = UUID;
    }

    public String getQDDM() {
        return QDDM;
    }

    public void setQDDM(String QDDM) {
        this.QDDM = QDDM;
    }

    public String getQDMC() {
        return QDMC;
    }

    public void setQDMC(String QDMC) {
        this.QDMC = QDMC;
    }

    public String getAPPCODE() {
        return APPCODE;
    }

    public void setAPPCODE(String APPCODE) {
        this.APPCODE = APPCODE;
    }

    public String getAPPNAME() {
        return APPNAME;
    }

    public void setAPPNAME(String APPNAME) {
        this.APPNAME = APPNAME;
    }

    public String getCOMPANYCODE() {
        return COMPANYCODE;
    }

    public void setCOMPANYCODE(String COMPANYCODE) {
        this.COMPANYCODE = COMPANYCODE;
    }

    public String getCOMPANYNAME() {
        return COMPANYNAME;
    }

    public void setCOMPANYNAME(String COMPANYNAME) {
        this.COMPANYNAME = COMPANYNAME;
    }

    public String getYXBZ() {
        return YXBZ;
    }

    public void setYXBZ(String YXBZ) {
        this.YXBZ = YXBZ;
    }

    public String getPASSWORD() {
        return PASSWORD;
    }

    public void setPASSWORD(String PASSWORD) {
        this.PASSWORD = PASSWORD;
    }

    public String getCREATETIME() {
        return CREATETIME;
    }

    public void setCREATETIME(String CREATETIME) {
        this.CREATETIME = CREATETIME;
    }

    public String getUPDATETIME() {
        return UPDATETIME;
    }

    public void setUPDATETIME(String UPDATETIME) {
        this.UPDATETIME = UPDATETIME;
    }

    public String getTC() {
        return TC;
    }

    public void setTC(String TC) {
        this.TC = TC;
    }
}
