package cn.com.servyou.sjlq.model.jkfw;

public class B2DataVo {
    private String sqlxh;
    private String params;
    private String rate;
    private String range;
    private int total;
    private int pageindex;
    private int pagesize;
    private String done;
    private String lastrun;
    private String nextrun;

    private String pic;
    private String xh;
    private String dt;

    public String getSqlxh() {
        return sqlxh;
    }

    public void setSqlxh(String sqlxh) {
        this.sqlxh = sqlxh;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public int getPagesize() {
        return pagesize;
    }

    public void setPagesize(int pagesize) {
        this.pagesize = pagesize;
    }

    public String getDone() {
        return done;
    }

    public void setDone(String done) {
        this.done = done;
    }

    public String getLastrun() {
        return lastrun;
    }

    public void setLastrun(String lastrun) {
        this.lastrun = lastrun;
    }

    public String getNextrun() {
        return nextrun;
    }

    public void setNextrun(String nextrun) {
        this.nextrun = nextrun;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getXh() {
        return xh;
    }

    public void setXh(String xh) {
        this.xh = xh;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }
}
