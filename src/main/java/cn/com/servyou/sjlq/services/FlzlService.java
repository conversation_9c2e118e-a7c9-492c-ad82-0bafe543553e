package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.mapper.sjjsbz.Clfcl4HxMapper;
import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.model.FlzlDataVo;
import cn.com.servyou.sjlq.model.FlzlParamVo;
import cn.com.servyou.sjlq.model.PageResult;
import cn.com.servyou.sjlq.utils.CommonUtils;
import cn.com.servyou.sjlq.utils.PageUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;

/**
 *
 * 附件资料档案
 * */

@Service
public class FlzlService {

    private static final Logger log = LoggerFactory.getLogger(FlzlService.class);

    @Autowired
    Clfcl4HxMapper clfcl4HxMapper;

    @Value("${flzl.view.url}")
    String flzlViewUrl;

    /**
     * function 新增附件
     * 入参：业务编号，唯一代表一笔业务
     * 附件名称，如 xxxx.jpg   abcd.pdf
     * 附件内容，base64
     * ywbh（业务编号）、ywlx（业务类型）、gdnd（归档年度）、gdrq（归档日期）、blry（办理人员）、附件内容
     */
    public CommonResult addFlzl(JSONObject ywsjObject){
        //入参
        FlzlDataVo flzlDataVo = JSONObject.parseObject(ywsjObject.toJSONString(), FlzlDataVo.class);
        return addFlzl(flzlDataVo);
    }

    public CommonResult addFlzl(FlzlDataVo flzlDataVo){
        //入参
        String newFjbh = CommonUtils.getUniqueID();
        if (StringUtils.isEmpty(flzlDataVo.getFjbh())) {
            flzlDataVo.setFjbh(newFjbh);
        }
        if (!StringUtils.isEmpty(flzlDataVo.getWjm())){
            flzlDataVo.setWjlx(flzlDataVo.getWjm().substring(flzlDataVo.getWjm().lastIndexOf(".")+1));
        }
        CommonResult commonResult = new CommonResult();
        if (StringUtils.isEmpty(flzlDataVo.getYwbh()) || StringUtils.isEmpty(flzlDataVo.getFjbh())){
            commonResult.setResult("99", "附件编号或业务编号不能为空",  null, 0);
            return commonResult;
        }
        //重复校验
        FlzlParamVo flzlParamVo = new FlzlParamVo();
        flzlParamVo.setFjbh(flzlDataVo.getFjbh());
        flzlParamVo.setYwlx(flzlDataVo.getYwlx());
        List queryList = clfcl4HxMapper.queryFlzlxxList(flzlParamVo);
        if (queryList != null && queryList.size() != 0){
            commonResult.setResult("99", "附件编号已存在",  null, 0);
            return commonResult;
        }
        try {
            clfcl4HxMapper.addFlzl(flzlDataVo);
            commonResult.setResult("00", "新增附件成功",  newFjbh, 1);
        } catch (Exception e) {
            log.error("新增附件异常", e);
            commonResult.setResult("99", "新增附件失败",  null, 0);
        }
        return commonResult;
    }

    /**
     * 作废附列资料
     * 传参 ywbh（业务编号）、flzlbh（附列资料编号）
     * 处理 更新作废标识 作废时间
     */
    public CommonResult zfFlzl(JSONObject ywsjObject){
        //入参
        FlzlDataVo flzlDataVo = JSONObject.parseObject(ywsjObject.toJSONString(), FlzlDataVo.class);
        String fjbh = flzlDataVo.getFjbh();
        String ywlx = flzlDataVo.getYwlx();

        return zfFlzl(fjbh, ywlx);
    }

    public CommonResult zfFlzl(String fjbh, String ywlx){
        //入参
        CommonResult commonResult = new CommonResult();
        if (StringUtils.isEmpty(fjbh)){
            commonResult.setResult("99", "附件编号不能为空",  null, 0);
            return commonResult;
        }
        String[] fjbhs = fjbh.split(",");
        String tempFjbhs = "";
        for (int i = 0; i < fjbhs.length; i++){
            if (i != 0){
                tempFjbhs += ",'" + fjbhs[i] + "'";
            } else {
                tempFjbhs += "'" + fjbhs[i] + "'";
            }
        }
        try {
            clfcl4HxMapper.zfFlzl(tempFjbhs, ywlx);
            commonResult.setResult("00", "删除附件成功",  null, 1);
        } catch (Exception e) {
            log.error("删除附件异常", e);
            commonResult.setResult("99", "删除附件失败",  null, 0);
        }
        return commonResult;
    }

    /**
     * function 获取附件管理档案列表信息
     * 传参 pageSize、pageNo、ywbh（业务编号）、ywlx（业务类型）、gdnd（归档年度）、gdrq（归档日期）、blry（办理人员）
     */
    public CommonResult getFlzlList(JSONObject ywsjObject){
        CommonResult commonResult = new CommonResult();
        //入参
        FlzlParamVo flzlParamVo = JSONObject.parseObject(ywsjObject.toJSONString(), FlzlParamVo.class);
        if (StringUtils.isEmpty(flzlParamVo.getYwbh()) && StringUtils.isEmpty(flzlParamVo.getFjbh()) && CollectionUtils.isEmpty(flzlParamVo.getYwbhList())){
            commonResult.setResult("99", "必填参数不能为空", null, 0);
            return commonResult;
        }
        List<FlzlDataVo> queryList = clfcl4HxMapper.queryFlzlxxList(flzlParamVo);
        if (CollectionUtils.isNotEmpty(queryList)){
            for (FlzlDataVo flzlDataVo: queryList){
                flzlDataVo.setWjurl(flzlViewUrl + "?fjbh=" + flzlDataVo.getFjbh() + "&ywlx=" + flzlDataVo.getYwlx());
            }
        }
        if(flzlParamVo.getPageNo() != null && flzlParamVo.getPageSize() != null){
            // 查询总记录数
            int total = clfcl4HxMapper.countFlzlxxList(flzlParamVo);
            // 创建并返回分页结果
            PageResult<FlzlDataVo> pageResult = PageUtils.createPageResult(queryList, total, flzlParamVo);
            commonResult.setResult("00", "查询成功",  pageResult, total);
        }else {
            commonResult.setResult("00", "查询成功",  queryList, queryList.size());
        }
        return commonResult;
    }

    /**
     * 下载附列资料
     * 传参 flzlbh（附列资料编号）
     */
    public String downloadFlzl(String fjbh,String ywlx, HttpServletRequest request, HttpServletResponse response){
        boolean isPdf;
        //入参
        try{
            ServletOutputStream out = response.getOutputStream();
            FlzlDataVo findFlzlDataVo = clfcl4HxMapper.queryFlzlxxByFjbh(fjbh,ywlx);
            if (findFlzlDataVo == null){
                return "附列资料不存在";
            }
            isPdf = "pdf".equalsIgnoreCase(findFlzlDataVo.getWjlx());
            String fileName = findFlzlDataVo.getWjm();//xxx.pdf
            String wjnr = findFlzlDataVo.getWjnr();//文件内容 base64
            byte[] buff = Base64Utils.decodeFromString(wjnr);
            if (isPdf){
                response.setContentType("application/pdf");
                if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) { // PDF,IE浏览器不预览，直接下载
                    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                }
                out.write(buff);
            } else {
                String downloadName = new String(fileName.getBytes("gb2312"), "ISO8859-1");
                response.setContentType("application/octet-stream");
                response.setHeader("content-Disposition", "attachment;filename=\"" + downloadName);
                out.write(buff);
            }
            out.flush();
            out.close();
        }catch (Exception e){
            log.error("下载附列资料异常", e);
            return "下载附列资料异常";
        }
        return null;
    }

    /**
     * 组装附列资料
     * 传参 flzlbh（附列资料编号）
     */
    public CommonResult buildFlzl(JSONObject ywsjObject){
        CommonResult commonResult = new CommonResult();
        String fjbhs = ywsjObject.get("fjbh").toString();
        String ywlx = ywsjObject.get("ywlx").toString();
        if (StringUtils.isEmpty(fjbhs)){
            commonResult.setResult("99", "组装失败：附件编号为空",  null, 0);
            return commonResult;
        }
        List<FlzlDataVo> flzlDataVoList = JSONArray.parseArray(fjbhs, FlzlDataVo.class);
        if (flzlDataVoList.size() > 20){
            commonResult.setResult("99", "组装失败：合并附件数量大于20",  null, 0);
            return commonResult;
        }
        if (flzlDataVoList.size() == 1){
            commonResult.setResult("99", "组装失败：合并附件数量少于2",  null, 0);
            return commonResult;
        }
        //校验临时文件是否已全部存在
        String tempFjbhs = "";
        for (int i = 0; i < flzlDataVoList.size(); i++){
            if (i != 0){
                tempFjbhs += ",'" + flzlDataVoList.get(i).getFjbh() + "'";
            } else {
                tempFjbhs += "'" + flzlDataVoList.get(i).getFjbh() + "'";
            }
        }
        List<FlzlDataVo> tempQueryList = clfcl4HxMapper.queryFlzlxxByFjbhs(tempFjbhs, ywlx);
        if (tempQueryList.size() != flzlDataVoList.size()){
            commonResult.setResult("99", "组装失败：附件数量对不上",  null, 0);
            return commonResult;
        }
        //按序号从小到大排列
        flzlDataVoList.sort(new Comparator<FlzlDataVo>() {
            @Override
            public int compare(FlzlDataVo o1, FlzlDataVo o2) {
                if (o1.getXh() > o2.getXh()){
                    return 1;
                } else if (o1.getXh() < o2.getXh()){
                    return -1;
                } else {
                    return 0;
                }
            }
        });
        //设置文件内容
        for (int i = 0; i < flzlDataVoList.size(); i++){
            for (int j = 0; j < tempQueryList.size(); j++){
                if (flzlDataVoList.get(i).getFjbh().equalsIgnoreCase(tempQueryList.get(j).getFjbh())){
                    flzlDataVoList.get(i).setWjnr(tempQueryList.get(j).getWjnr());
                    break;
                }
            }
        }
        //总的文件内容
        String fullWjnr = "";
        for (int i = 0; i < flzlDataVoList.size(); i++){
            fullWjnr += flzlDataVoList.get(i).getWjnr();
        }
        //新增一个文件记录
        FlzlDataVo flzlDataVo = tempQueryList.get(0);
        flzlDataVo.setWjnr(fullWjnr);
        clfcl4HxMapper.zfFlzlByFjbhs(tempFjbhs,ywlx);
        flzlDataVo.setFjbh(CommonUtils.getUniqueID());
        flzlDataVo.setGdzt("未归档");
        return addFlzl(flzlDataVo);
    }

    /**
     * 查询附件数量
     * @param ywsjObject
     * @return
     */
    public CommonResult queryFlzlCountByYwbh(JSONObject ywsjObject){
        FlzlParamVo flzlParamVo = JSONObject.parseObject(ywsjObject.toJSONString(), FlzlParamVo.class);
        int sl = clfcl4HxMapper.countFlzlxxList(flzlParamVo);
        CommonResult commonResult = new CommonResult();
        commonResult.setResult("00", "查询成功",  sl, 1);
        return commonResult;
    }

    /**
     * 附件数据更新
     * @param ywsjObject
     * @return
     */
    public CommonResult updateFlzl(JSONObject ywsjObject){
        //入参
        FlzlDataVo flzlDataVo = JSONObject.parseObject(ywsjObject.toJSONString(), FlzlDataVo.class);
        int i = clfcl4HxMapper.updateFlzl(flzlDataVo);
        CommonResult commonResult = new CommonResult();
        commonResult.setResult("00", "更新成功",  i, 1);
        return commonResult;
    }
}