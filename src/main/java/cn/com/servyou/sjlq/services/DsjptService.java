package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.utils.CommonUtils;
import cn.com.servyou.sjlq.utils.WsUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.util.*;

@Service
public class DsjptService {
    private static final Logger log = LoggerFactory.getLogger(DsjptService.class);
    private String esbkey;
    private final static String wsdlUrl = "http://98.9.1.218/bondeWebService?wsdl";
    private final static String method = "requestService";

    public CommonResult queryEsb(String sqlxh, List<Map<String,Object>> paramList){
        return queryEsb(sqlxh,"1", "10","0","false",paramList);
    }
    public CommonResult queryEsb(String sqlxh, String pageindex, String pagesize, String total, String dototal, List<Map<String,Object>> paramList){
        CommonResult commonResult = new CommonResult();
        Long start = System.currentTimeMillis();
        try {
            if(sqlxh==null){
                throw new Exception("sqlxh不能为空！");
            }
            if(pageindex==null){
                pageindex="1";
            }
            if(pagesize==null){
                pagesize="10000";
            }
            if(total==null){
                total="0";
            }
            if(dototal==null){
                dototal="false";
            }
            String queryParams=JSONObject.toJSONString(paramList);
            String requester="10000017";
            Long timestamp=System.currentTimeMillis();
            String ticket=MD5(esbkey+requester+timestamp).toUpperCase();
            String ywbw =
                    "<sqlxh>"+sqlxh+"</sqlxh>" +
                    "<pageindex>"+pageindex+"</pageindex>" +
                    "<pagesize>"+pagesize+"</pagesize>" +
                    "<total>"+total+"</total>" +
                    "<dototal>"+dototal+"</dototal>" +
                    "<queryparams>"+queryParams+"</queryparams>" +
                    "<requester>"+requester+"</requester>" +
                    "<ticket>"+ticket+"</ticket>" +
                    "<timestamp>"+timestamp+"</timestamp>";
            log.info(sqlxh+"请求："+ywbw);
            commonResult = WsUtil.httpRequest(wsdlUrl,method,"HTXXTODSJ","HTXXQD",ywbw);
            if(commonResult.isSuccess()){
                String rtn = (String)commonResult.getData();
                String resultJsonStr = rtn.substring(rtn.indexOf("<result>")+8,rtn.indexOf("</result>"));
                log.info(sqlxh+"请求返回："+resultJsonStr);
                //resultJsonStr=JsonStringConver(resultJsonStr);
                JSONObject JsonObj = JSONObject.parseObject(resultJsonStr.replace("\\",""));
                if("000".equals(JsonObj.getString("code"))){
                    JSONArray rows = JsonObj.getJSONArray("rows");
                    if(rows!=null && rows.size()>0){
                        for(int i=0;i<rows.size();i++){
                            JSONObject jo = rows.getJSONObject(i);
                            for(Map.Entry<String,Object> entry:jo.entrySet()){
                                if("null".equals(entry.getValue())){
                                    entry.setValue(null);
                                }
                            }
                        }
                    }
                    commonResult.setResult("00",JsonObj.getString("message"),JsonObj.get("rows"),JsonObj.getInteger("total"));
                }else{
                    commonResult.setData(null);
                    commonResult.setFail(JsonObj.getString("code"),JsonObj.getString("errorMsg"));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            commonResult.setResult("99",e.getMessage(),null);
        }
        commonResult.setTime(System.currentTimeMillis()-start);
        return commonResult;
    }

    public Map<String,Object> addParam(String name,String value,String type){
        return addParam(name,value,type,"null");
    }

    public Map<String,Object> addParam(String name,String value,String type,String doNull){
        if("updateTime".equals(name) && StringUtils.isEmpty(value)){
            value = CommonUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss");
        }
        if("createTime".equals(name) && StringUtils.isEmpty(value)){
            value = CommonUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss");
        }
        if("deleted".equals(name) && StringUtils.isEmpty(value)){
            value = "0";
        }
        if(StringUtils.isEmpty(value)){
            value = doNull;
        }
        Map<String,Object> pram = new HashMap<>();
        pram.put("name",name);
        pram.put("value",value);
        pram.put("type",type);
        return pram;
    }

    public void addParam(List<Map<String,Object>> paramList,String name,String value,String type){
        if(!StringUtils.isEmpty(value)){
            Map<String,Object> pram = new HashMap<>();
            pram.put("name",name);
            pram.put("value",value);
            pram.put("type",type);
            paramList.add(pram);
        }
    }

    public Map<String,Object> paramListToMap(List<Map<String,Object>> paramList){
        Map<String,Object> paramMap = new HashMap<>();
        for(Map<String,Object> tempMap:paramList){
            paramMap.put((String)tempMap.get("name"),String.valueOf(tempMap.get("value")));
        }
        return paramMap;
    }

    public List<Map<String,Object>> getList(){
        return new ArrayList<>();
    }

    public String JsonStringConver(String s){
        char[] temp = s.toCharArray();
        int n = temp.length;
        for(int i=0;i<n;i++){
            if(temp[i] == ':' && temp[i+1] == '"' && temp[i+2] != ',' && temp[i+2] != '}'){
                for(int j = i+2;j<n;j++){
                    if(temp[j] == '"'){
                        if((temp[j+1] != ',' && temp[j+1] != '}') || (temp[j+1] == ',' && temp[j+2] != '"')  ){
                            temp[j] = '"';
                        }else{
                            break;
                        }
                    }
                }
            }
        }
        return new String(temp);
    }

    public String MD5(String s) {
        char hexDigits[]={'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
        try {
            byte[] btInput = s.getBytes();
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}