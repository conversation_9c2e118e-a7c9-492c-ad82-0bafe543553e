package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.model.FlzlDataVo;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ClfService {
    private static final Logger log = LoggerFactory.getLogger(ClfService.class);
    @Autowired
    private DsjptService esbService;

    @Autowired
    FlzlService flzlService;

    /**
     * function 外部数据接收接口
     */
    public CommonResult jswbsj(JSONObject resive){
        log.info("外部数据接收接口");
        CommonResult rtn = new CommonResult();
        Long s = System.currentTimeMillis();
        List<Map<String,Object>> paramList = new ArrayList<>();
        try{
            //写入主表信息30000024
            paramList.add(esbService.addParam("id",resive.getString("id"),"string"));
            paramList.add(esbService.addParam("applyNum",resive.getString("applyNum"),"string"));
            paramList.add(esbService.addParam("applicant",resive.getString("applicant"),"string"));
            paramList.add(esbService.addParam("applyTime",resive.getString("applyTime"),"string"));
            paramList.add(esbService.addParam("idType",resive.getString("idType"),"string"));
            paramList.add(esbService.addParam("idNum",resive.getString("idNum"),"string"));
            paramList.add(esbService.addParam("phone",resive.getString("phone"),"string"));
            String realEstateUnitNum = StringUtils.isEmpty(resive.getString("realEstateUnitNum"))?"":resive.getString("realEstateUnitNum");
            paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
            paramList.add(esbService.addParam("applyStatus",resive.getString("applyStatus"),"string"));
            paramList.add(esbService.addParam("sellerStatus",resive.getString("sellerStatus"),"string"));
            paramList.add(esbService.addParam("buyerStatus",resive.getString("buyerStatus"),"string"));
            paramList.add(esbService.addParam("shareSituation",resive.getString("shareSituation"),"string"));
            paramList.add(esbService.addParam("houseType",resive.getString("houseType"),"string"));
            String remark = StringUtils.isEmpty(resive.getString("remark"))?"":resive.getString("remark");
            paramList.add(esbService.addParam("remark",remark,"string"));
            String applyRemark = StringUtils.isEmpty(resive.getString("applyRemark"))?"":resive.getString("applyRemark");
            paramList.add(esbService.addParam("applyRemark",applyRemark,"string"));
            paramList.add(esbService.addParam("housePurpose",resive.getString("housePurpose"),"string"));
            String certificateType = StringUtils.isEmpty(resive.getString("certificateType"))?"":resive.getString("certificateType");
            paramList.add(esbService.addParam("certificateType",certificateType,"string"));
            String paymentMethod = StringUtils.isEmpty(resive.getString("paymentMethod"))?"":resive.getString("paymentMethod");
            paramList.add(esbService.addParam("paymentMethod",paymentMethod,"string"));
            paramList.add(esbService.addParam("creator",resive.getString("creator"),"string"));
            paramList.add(esbService.addParam("createTime",resive.getString("createTime"),"string"));
            paramList.add(esbService.addParam("updater",resive.getString("updater"),"string"));
            paramList.add(esbService.addParam("updateTime",resive.getString("updateTime"),"string"));
            paramList.add(esbService.addParam("deleted",resive.getString("deleted"),"string"));
            String rejectTarget = StringUtils.isEmpty(resive.getString("rejectTarget"))?"":resive.getString("rejectTarget");
            paramList.add(esbService.addParam("rejectTarget",rejectTarget,"string"));
            String xxgmk = StringUtils.isEmpty(resive.getString("xxgmk"))?"":resive.getString("xxgmk");
            paramList.add(esbService.addParam("xxgmk",xxgmk,"string"));
            String xcqwj = StringUtils.isEmpty(resive.getString("xcqwj"))?"":resive.getString("xcqwj");
            paramList.add(esbService.addParam("xcqwj",xcqwj,"string"));
            rtn = esbService.queryEsb("30000024","1", "10","0","false",paramList);
            if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                List<Map<String,Object>> paramListzb = new ArrayList<>();
                paramListzb.add(esbService.addParam("id",resive.getString("id"),"string"));
                rtn = esbService.queryEsb("30000009","1", "100","0","false",paramListzb);
                if(rtn.isSuccess()) {
                    JSONArray ja = (JSONArray) rtn.getData();
                    if (ja.size() > 0) {
                        rtn.setFail("当前ID为"+resive.getString("id")+"的数据已存在!");
                        return rtn;
                    }
                    rtn = esbService.queryEsb("30000031","1", "10","0","false",paramList);
                }
            }
            JSONArray sellerInfo = resive.getJSONArray("sellerInfo");
            if(rtn.isSuccess() && sellerInfo!=null && sellerInfo.size()>0){//卖方信息
                for (int i=0;i<sellerInfo.size();i++){
                    JSONObject seller = sellerInfo.getJSONObject(i);
                    paramList.clear();
                    paramList.add(esbService.addParam("id",seller.getString("id"),"string"));
                    paramList.add(esbService.addParam("reciid",seller.getString("reciid"),"string"));
                    paramList.add(esbService.addParam("obligeeType",seller.getString("obligeeType"),"string"));
                    paramList.add(esbService.addParam("name",seller.getString("name"),"string"));
                    paramList.add(esbService.addParam("idType",seller.getString("idType"),"string"));
                    paramList.add(esbService.addParam("idNum",seller.getString("idNum"),"string"));
                    paramList.add(esbService.addParam("phone",seller.getString("phone"),"string"));
                    paramList.add(esbService.addParam("holdingRatio",seller.getString("holdingRatio"),"string"));
                    String maritalStatus = StringUtils.isEmpty(seller.getString("maritalStatus"))?"":seller.getString("maritalStatus");
                    paramList.add(esbService.addParam("maritalStatus",maritalStatus,"string"));
                    String houseNumber = StringUtils.isEmpty(seller.getString("houseNumber"))?"":seller.getString("houseNumber");
                    paramList.add(esbService.addParam("houseNumber",houseNumber,"string"));
                    paramList.add(esbService.addParam("shareSituation",seller.getString("shareSituation"),"string"));
                    paramList.add(esbService.addParam("sort",seller.getString("sort"),"string"));
                    paramList.add(esbService.addParam("creator",seller.getString("creator"),"string"));
                    paramList.add(esbService.addParam("createTime",seller.getString("createTime"),"string"));
                    paramList.add(esbService.addParam("updater",seller.getString("updater"),"string"));
                    paramList.add(esbService.addParam("updateTime",seller.getString("updateTime"),"string"));
                    paramList.add(esbService.addParam("deleted",seller.getString("deleted"),"string"));
                    String jybl = StringUtils.isEmpty(seller.getString("jybl")) ? seller.getString("holdingRatio") :seller.getString("jybl");
                    paramList.add(esbService.addParam("jybl",jybl,"string"));
                    String sfzxqszr = StringUtils.isEmpty(seller.getString("sfzxqszr"))?"否":seller.getString("sfzxqszr");
                    paramList.add(esbService.addParam("sfzxqszr",sfzxqszr,"string"));
                    String sfwtbl = StringUtils.isEmpty(seller.getString("sfwtbl"))?"否":seller.getString("sfwtbl");
                    paramList.add(esbService.addParam("sfwtbl",sfwtbl,"string"));
                    String sfxsgsjm = StringUtils.isEmpty(seller.getString("sfxsgsjm"))?"否":seller.getString("sfxsgsjm");
                    paramList.add(esbService.addParam("sfxsgsjm",sfxsgsjm,"string"));
                    String jtcyxmgx = StringUtils.isEmpty(seller.getString("jtcyxmgx"))?"":seller.getString("jtcyxmgx");
                    paramList.add(esbService.addParam("jtcyxmgx",jtcyxmgx,"string"));
                    String jtcysfzh = StringUtils.isEmpty(seller.getString("jtcysfzh"))?"":seller.getString("jtcysfzh");
                    paramList.add(esbService.addParam("jtcysfzh",jtcysfzh,"string"));
                    rtn = esbService.queryEsb("30000029","1", "10","0","false",paramList);
                    if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                        rtn = esbService.queryEsb("30000032","1", "10","0","false",paramList);
                    }
                }
            }
            JSONArray buyerInfo = resive.getJSONArray("buyerInfo");
            if(rtn.isSuccess() && buyerInfo!=null && buyerInfo.size()>0){//买方信息
                for (int i=0;i<buyerInfo.size();i++){
                    JSONObject buyer = buyerInfo.getJSONObject(i);
                    paramList.clear();
                    paramList.add(esbService.addParam("id",buyer.getString("id"),"string"));
                    paramList.add(esbService.addParam("reciid",buyer.getString("reciid"),"string"));
                    paramList.add(esbService.addParam("obligeeType",buyer.getString("obligeeType"),"string"));
                    paramList.add(esbService.addParam("name",buyer.getString("name"),"string"));
                    paramList.add(esbService.addParam("idType",buyer.getString("idType"),"string"));
                    paramList.add(esbService.addParam("idNum",buyer.getString("idNum"),"string"));
                    paramList.add(esbService.addParam("phone",buyer.getString("phone"),"string"));
                    String maritalStatus = StringUtils.isEmpty(buyer.getString("maritalStatus"))?"":buyer.getString("maritalStatus");
                    paramList.add(esbService.addParam("maritalStatus",maritalStatus,"string"));
                    String houseNumber = StringUtils.isEmpty(buyer.getString("houseNumber"))?"":buyer.getString("houseNumber");
                    paramList.add(esbService.addParam("houseNumber",houseNumber,"string"));
                    paramList.add(esbService.addParam("obligeeNature",buyer.getString("obligeeNature"),"string"));
                    paramList.add(esbService.addParam("holdingRatio",buyer.getString("holdingRatio"),"string"));
                    paramList.add(esbService.addParam("shareSituation",buyer.getString("shareSituation"),"string"));
                    paramList.add(esbService.addParam("sort",buyer.getString("sort"),"string"));
                    paramList.add(esbService.addParam("creator",buyer.getString("creator"),"string"));
                    paramList.add(esbService.addParam("createTime",buyer.getString("createTime"),"string"));
                    paramList.add(esbService.addParam("updater",buyer.getString("updater"),"string"));
                    paramList.add(esbService.addParam("updateTime",buyer.getString("updateTime"),"string"));
                    paramList.add(esbService.addParam("deleted",buyer.getString("deleted"),"string"));
                    String sfwtbl = StringUtils.isEmpty(buyer.getString("sfwtbl"))?"否":buyer.getString("sfwtbl");
                    paramList.add(esbService.addParam("sfwtbl",sfwtbl,"string"));
                    String sfxsqsjm = StringUtils.isEmpty(buyer.getString("sfxsqsjm"))?"否":buyer.getString("sfxsqsjm");
                    paramList.add(esbService.addParam("sfxsqsjm",sfxsqsjm,"string"));
                    String poxm = StringUtils.isEmpty(buyer.getString("poxm"))?"":buyer.getString("poxm");
                    paramList.add(esbService.addParam("poxm",poxm,"string"));
                    String posfzh = StringUtils.isEmpty(buyer.getString("posfzh"))?"":buyer.getString("posfzh");
                    paramList.add(esbService.addParam("posfzh",posfzh,"string"));
                    String wcnznxm1 = StringUtils.isEmpty(buyer.getString("wcnznxm1"))?"":buyer.getString("wcnznxm1");
                    paramList.add(esbService.addParam("wcnznxm1",wcnznxm1,"string"));
                    String wcnznsfzh1 = StringUtils.isEmpty(buyer.getString("wcnznsfzh1"))?"":buyer.getString("wcnznsfzh1");
                    paramList.add(esbService.addParam("wcnznsfzh1",wcnznsfzh1,"string"));
                    String wcnznxm2 = StringUtils.isEmpty(buyer.getString("wcnznxm2"))?"":buyer.getString("wcnznxm2");
                    paramList.add(esbService.addParam("wcnznxm2",wcnznxm2,"string"));
                    String wcnznsfzh2 = StringUtils.isEmpty(buyer.getString("wcnznsfzh2"))?"":buyer.getString("wcnznsfzh2");
                    paramList.add(esbService.addParam("wcnznsfzh2",wcnznsfzh2,"string"));
                    String qsjmx = StringUtils.isEmpty(buyer.getString("qsjmx"))?"":buyer.getString("qsjmx");
                    paramList.add(esbService.addParam("qsjmx",qsjmx,"string"));
                    rtn = esbService.queryEsb("30000020","1", "10","0","false",paramList);
                    if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                        rtn = esbService.queryEsb("30000033","1", "10","0","false",paramList);
                    }
                    //家庭成员信息保存
                    JSONArray jtcy = buyer.getJSONArray("jtcyList");
                    if(rtn.isSuccess() && jtcy!=null && jtcy.size()>0){
                        for (int j=0;j<jtcy.size();j++){
                            JSONObject jtcyxx = jtcy.getJSONObject(j);
                            paramList.clear();
                            paramList.add(esbService.addParam("uuid",jtcyxx.getString("uuid"),"string"));
                            paramList.add(esbService.addParam("buyerId",buyer.getString("buyerId"),"string"));
                            paramList.add(esbService.addParam("xm",jtcyxx.getString("xm"),"string"));
                            paramList.add(esbService.addParam("gx",jtcyxx.getString("gx"),"string"));
                            paramList.add(esbService.addParam("sfzh",jtcyxx.getString("sfzh"),"string"));
                            paramList.add(esbService.addParam("lrrdm",jtcyxx.getString("lrrdm"),"string"));
                            paramList.add(esbService.addParam("lrrq",jtcyxx.getString("lrrq"),"string"));
                            paramList.add(esbService.addParam("xgrdm",jtcyxx.getString("xgrdm"),"string"));
                            paramList.add(esbService.addParam("xgrq",jtcyxx.getString("xgrq"),"string"));
                            paramList.add(esbService.addParam("yxbz",jtcyxx.getString("yxbz"),"string"));
                            rtn = esbService.queryEsb("61000453","1", "10","0","false",paramList);
                            if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                                rtn = esbService.queryEsb("61000454","1", "10","0","false",paramList);
                            }
                        }
                    }
                }
            }
            JSONObject house = resive.getJSONObject("houseInfo");
            if(rtn.isSuccess() && house!=null && house.size()>0){//个人住宅详情
                paramList.clear();
                paramList.add(esbService.addParam("id",house.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",house.getString("reciid"),"string"));
                paramList.add(esbService.addParam("houseLocation",house.getString("houseLocation"),"string"));
                String realEstateCertificateNum = StringUtils.isEmpty(house.getString("realEstateCertificateNum"))?"":house.getString("realEstateCertificateNum");
                paramList.add(esbService.addParam("realEstateCertificateNum",realEstateCertificateNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(house.getString("realEstateUnitNum"))?"":house.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("buildingArea",house.getString("buildingArea"),"string"));
                paramList.add(esbService.addParam("housePurpose",house.getString("housePurpose"),"string"));
                certificateType = StringUtils.isEmpty(house.getString("certificateType"))?"":house.getString("certificateType");
                paramList.add(esbService.addParam("certificateType",certificateType,"string"));
                paymentMethod = StringUtils.isEmpty(house.getString("paymentMethod"))?"":house.getString("paymentMethod");
                paramList.add(esbService.addParam("paymentMethod",paymentMethod,"string"));
                paramList.add(esbService.addParam("realEstateName",house.getString("realEstateName"),"string"));
                paramList.add(esbService.addParam("suiteType",house.getString("suiteType"),"string"));
                paramList.add(esbService.addParam("houseRoom",house.getString("houseRoom"),"string"));
                paramList.add(esbService.addParam("houseHall",house.getString("houseHall"),"string"));
                paramList.add(esbService.addParam("houseBathroom",house.getString("houseBathroom"),"string"));
                paramList.add(esbService.addParam("houseKitchen",house.getString("houseKitchen"),"string"));
                paramList.add(esbService.addParam("residentialType",house.getString("residentialType"),"string"));
                paramList.add(esbService.addParam("groundFloors",house.getString("groundFloors"),"string"));
                paramList.add(esbService.addParam("undergroundFloors",house.getString("undergroundFloors"),"string"));
                paramList.add(esbService.addParam("toward",house.getString("toward"),"string"));
                paramList.add(esbService.addParam("pmLevel",house.getString("pmLevel"),"string"));
                paramList.add(esbService.addParam("supportingFacility",house.getString("supportingFacility"),"string"));
                paramList.add(esbService.addParam("buildingCompletionYear",house.getString("buildingCompletionYear"),"string"));
                paramList.add(esbService.addParam("declareTransactionTotalPrice",house.getString("declareTransactionTotalPrice"),"string"));
                paramList.add(esbService.addParam("creator",house.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",house.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",house.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",house.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",house.getString("deleted"),"string"));
                rtn = esbService.queryEsb("30000023","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000034","1", "10","0","false",paramList);
                }
            }

            JSONObject business = resive.getJSONObject("businessInfo");
            if(rtn.isSuccess() && business!=null && business.size()>0){//商服用房
                paramList.clear();
                paramList.add(esbService.addParam("id",business.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",business.getString("reciid"),"string"));
                paramList.add(esbService.addParam("houseType",business.getString("houseType"),"string"));
                String evaluatePartition = StringUtils.isEmpty(house.getString("evaluatePartition"))?"":house.getString("evaluatePartition");
                paramList.add(esbService.addParam("evaluatePartition",evaluatePartition,"string"));
                paramList.add(esbService.addParam("houseLocation",business.getString("houseLocation"),"string"));
                paramList.add(esbService.addParam("pmName",business.getString("pmName"),"string"));
                String realEstateCertificateNum = StringUtils.isEmpty(business.getString("realEstateCertificateNum"))?"":business.getString("realEstateCertificateNum");
                paramList.add(esbService.addParam("realEstateCertificateNum",realEstateCertificateNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(business.getString("realEstateUnitNum"))?"":business.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("propertyRightOwner",business.getString("propertyRightOwner"),"string"));
                paramList.add(esbService.addParam("idType",business.getString("idType"),"string"));
                paramList.add(esbService.addParam("idNum",business.getString("idNum"),"string"));
                paramList.add(esbService.addParam("phone",business.getString("phone"),"string"));
                paramList.add(esbService.addParam("buildingArea",business.getString("buildingArea"),"string"));
                paramList.add(esbService.addParam("floor",business.getString("floor"),"string"));
                paramList.add(esbService.addParam("buildingStructure",business.getString("buildingStructure"),"string"));
                paramList.add(esbService.addParam("landUseRightType",business.getString("landUseRightType"),"string"));
                paramList.add(esbService.addParam("landUseRightEndDate",business.getString("landUseRightEndDate"),"string"));
                String remainingLandUseErm = StringUtils.isEmpty(business.getString("remainingLandUseErm"))?"":business.getString("remainingLandUseErm");
                paramList.add(esbService.addParam("remainingLandUseErm",remainingLandUseErm,"string"));
                paramList.add(esbService.addParam("houseShape",business.getString("houseShape"),"string"));
                paramList.add(esbService.addParam("transactionDate",business.getString("transactionDate"),"string"));
                paramList.add(esbService.addParam("declareTransactionTotalPrice",business.getString("declareTransactionTotalPrice"),"string"));
                String declareTransactionUnitPrice = StringUtils.isEmpty(business.getString("declareTransactionUnitPrice"))?"":business.getString("declareTransactionUnitPrice");
                paramList.add(esbService.addParam("declareTransactionUnitPrice",declareTransactionUnitPrice,"string"));
                String houseOtherDescription = StringUtils.isEmpty(business.getString("houseOtherDescription"))?"":business.getString("houseOtherDescription");
                paramList.add(esbService.addParam("houseOtherDescription",houseOtherDescription,"string"));
                paramList.add(esbService.addParam("creator",business.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",business.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",business.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",business.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",business.getString("deleted"),"string"));
                rtn = esbService.queryEsb("30000022","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000035","1", "10","0","false",paramList);
                }
            }

            JSONObject office = resive.getJSONObject("officeInfo");
            if(rtn.isSuccess() && office!=null && office.size()>0){//办公用房
                paramList.clear();
                paramList.add(esbService.addParam("id",office.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",office.getString("reciid"),"string"));
                paramList.add(esbService.addParam("houseType",office.getString("houseType"),"string"));
                String evaluatePartition = StringUtils.isEmpty(office.getString("evaluatePartition"))?"":office.getString("evaluatePartition");
                paramList.add(esbService.addParam("evaluatePartition",evaluatePartition,"string"));
                paramList.add(esbService.addParam("houseLocation",office.getString("houseLocation"),"string"));
                paramList.add(esbService.addParam("pmName",office.getString("pmName"),"string"));
                String realEstateCertificateNum = StringUtils.isEmpty(office.getString("realEstateCertificateNum"))?"":office.getString("realEstateCertificateNum");
                paramList.add(esbService.addParam("realEstateCertificateNum",realEstateCertificateNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(office.getString("realEstateUnitNum"))?"":office.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("propertyRightOwner",office.getString("propertyRightOwner"),"string"));
                paramList.add(esbService.addParam("idType",office.getString("idType"),"string"));
                paramList.add(esbService.addParam("idNum",office.getString("idNum"),"string"));
                paramList.add(esbService.addParam("phone",office.getString("phone"),"string"));
                paramList.add(esbService.addParam("buildingArea",office.getString("buildingArea"),"string"));
                paramList.add(esbService.addParam("floor",office.getString("floor"),"string"));
                paramList.add(esbService.addParam("buildingStructure",office.getString("buildingStructure"),"string"));
                paramList.add(esbService.addParam("landUseRightType",office.getString("landUseRightType"),"string"));
                paramList.add(esbService.addParam("landUseRightEndDate",office.getString("landUseRightEndDate"),"string"));
                String remainingLandUseErm = StringUtils.isEmpty(office.getString("remainingLandUseErm"))?"":office.getString("remainingLandUseErm");
                paramList.add(esbService.addParam("remainingLandUseErm",remainingLandUseErm,"string"));
                paramList.add(esbService.addParam("yearBuilt",office.getString("yearBuilt"),"string"));
                paramList.add(esbService.addParam("transactionDate",office.getString("transactionDate"),"string"));
                paramList.add(esbService.addParam("declareTransactionTotalPrice",office.getString("declareTransactionTotalPrice"),"string"));
                String declareTransactionUnitPrice = StringUtils.isEmpty(office.getString("declareTransactionUnitPrice"))?"":office.getString("declareTransactionUnitPrice");
                paramList.add(esbService.addParam("declareTransactionUnitPrice",declareTransactionUnitPrice,"string"));
                String houseOtherDescription = StringUtils.isEmpty(office.getString("houseOtherDescription"))?"":office.getString("houseOtherDescription");
                paramList.add(esbService.addParam("houseOtherDescription",houseOtherDescription,"string"));
                paramList.add(esbService.addParam("creator",office.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",office.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",office.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",office.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",office.getString("deleted"),"string"));
                rtn = esbService.queryEsb("30000025","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000036","1", "10","0","false",paramList);
                }
            }

            JSONObject apartment = resive.getJSONObject("apartmentInfo");
            if(rtn.isSuccess() && apartment!=null && apartment.size()>0){//公寓
                paramList.clear();
                paramList.add(esbService.addParam("id",apartment.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",apartment.getString("reciid"),"string"));
                paramList.add(esbService.addParam("houseType",apartment.getString("houseType"),"string"));
                String evaluatePartition = StringUtils.isEmpty(apartment.getString("evaluatePartition"))?"":apartment.getString("evaluatePartition");
                paramList.add(esbService.addParam("evaluatePartition",evaluatePartition,"string"));
                paramList.add(esbService.addParam("houseLocation",apartment.getString("houseLocation"),"string"));
                paramList.add(esbService.addParam("pmName",apartment.getString("pmName"),"string"));
                String realEstateCertificateNum = StringUtils.isEmpty(apartment.getString("realEstateCertificateNum"))?"":apartment.getString("realEstateCertificateNum");
                paramList.add(esbService.addParam("realEstateCertificateNum",realEstateCertificateNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(apartment.getString("realEstateUnitNum"))?"":apartment.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("propertyRightOwner",apartment.getString("propertyRightOwner"),"string"));
                paramList.add(esbService.addParam("idType",apartment.getString("idType"),"string"));
                paramList.add(esbService.addParam("idNum",apartment.getString("idNum"),"string"));
                paramList.add(esbService.addParam("phone",apartment.getString("phone"),"string"));
                paramList.add(esbService.addParam("buildingArea",apartment.getString("buildingArea"),"string"));
                paramList.add(esbService.addParam("floor",apartment.getString("floor"),"string"));
                paramList.add(esbService.addParam("buildingStructure",apartment.getString("buildingStructure"),"string"));
                paramList.add(esbService.addParam("landUseRightType",apartment.getString("landUseRightType"),"string"));
                paramList.add(esbService.addParam("landUseRightEndDate",apartment.getString("landUseRightEndDate"),"string"));
                String remainingLandUseErm = StringUtils.isEmpty(apartment.getString("remainingLandUseErm"))?"":apartment.getString("remainingLandUseErm");
                paramList.add(esbService.addParam("remainingLandUseErm",remainingLandUseErm,"string"));
                paramList.add(esbService.addParam("floorHeight",apartment.getString("floorHeight"),"string"));
                paramList.add(esbService.addParam("yearBuilt",apartment.getString("yearBuilt"),"string"));
                paramList.add(esbService.addParam("buildingImplement",apartment.getString("buildingImplement"),"string"));
                paramList.add(esbService.addParam("transactionDate",apartment.getString("transactionDate"),"string"));
                paramList.add(esbService.addParam("declareTransactionTotalPrice",apartment.getString("declareTransactionTotalPrice"),"string"));
                String declareTransactionUnitPrice = StringUtils.isEmpty(apartment.getString("declareTransactionUnitPrice"))?"":apartment.getString("declareTransactionUnitPrice");
                paramList.add(esbService.addParam("declareTransactionUnitPrice",declareTransactionUnitPrice,"string"));
                String houseOtherDescription = StringUtils.isEmpty(apartment.getString("houseOtherDescription"))?"":apartment.getString("houseOtherDescription");
                paramList.add(esbService.addParam("houseOtherDescription",houseOtherDescription,"string"));
                paramList.add(esbService.addParam("creator",apartment.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",apartment.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",apartment.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",apartment.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",apartment.getString("deleted"),"string"));
                rtn = esbService.queryEsb("30000021","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000037","1", "10","0","false",paramList);
                }
            }

            JSONObject parking = resive.getJSONObject("parkingInfo");
            if(rtn.isSuccess() && parking!=null && parking.size()>0){//车库
                paramList.clear();
                paramList.add(esbService.addParam("id",parking.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",parking.getString("reciid"),"string"));
                paramList.add(esbService.addParam("houseType",parking.getString("houseType"),"string"));
                String evaluatePartition = StringUtils.isEmpty(parking.getString("evaluatePartition"))?"":parking.getString("evaluatePartition");
                paramList.add(esbService.addParam("evaluatePartition",evaluatePartition,"string"));
                paramList.add(esbService.addParam("houseLocation",parking.getString("houseLocation"),"string"));
                paramList.add(esbService.addParam("pmName",parking.getString("pmName"),"string"));
                String realEstateCertificateNum = StringUtils.isEmpty(parking.getString("realEstateCertificateNum"))?"":parking.getString("realEstateCertificateNum");
                paramList.add(esbService.addParam("realEstateCertificateNum",realEstateCertificateNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(parking.getString("realEstateUnitNum"))?"":parking.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("propertyRightOwner",parking.getString("propertyRightOwner"),"string"));
                paramList.add(esbService.addParam("idType",parking.getString("idType"),"string"));
                paramList.add(esbService.addParam("idNum",parking.getString("idNum"),"string"));
                paramList.add(esbService.addParam("phone",parking.getString("phone"),"string"));
                paramList.add(esbService.addParam("buildingArea",parking.getString("buildingArea"),"string"));
                paramList.add(esbService.addParam("floor",parking.getString("floor"),"string"));
                paramList.add(esbService.addParam("internalArea",parking.getString("internalArea"),"string"));
                paramList.add(esbService.addParam("landUseRightType",parking.getString("landUseRightType"),"string"));
                paramList.add(esbService.addParam("landUseRightEndDate",parking.getString("landUseRightEndDate"),"string"));
                String remainingLandUseErm = StringUtils.isEmpty(parking.getString("remainingLandUseErm"))?"":parking.getString("remainingLandUseErm");
                paramList.add(esbService.addParam("remainingLandUseErm",remainingLandUseErm,"string"));
                paramList.add(esbService.addParam("parkingSpaceType",parking.getString("parkingSpaceType"),"string"));
                paramList.add(esbService.addParam("transactionDate",parking.getString("transactionDate"),"string"));
                paramList.add(esbService.addParam("declareTransactionTotalPrice",parking.getString("declareTransactionTotalPrice"),"string"));
                String declareTransactionUnitPrice = StringUtils.isEmpty(parking.getString("declareTransactionUnitPrice"))?"":parking.getString("declareTransactionUnitPrice");
                paramList.add(esbService.addParam("declareTransactionUnitPrice",declareTransactionUnitPrice,"string"));
                String houseOtherDescription = StringUtils.isEmpty(parking.getString("houseOtherDescription"))?"":parking.getString("houseOtherDescription");
                paramList.add(esbService.addParam("houseOtherDescription",houseOtherDescription,"string"));
                paramList.add(esbService.addParam("creator",parking.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",parking.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",parking.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",parking.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",parking.getString("deleted"),"string"));
                rtn = esbService.queryEsb("30000026","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000038","1", "10","0","false",paramList);
                }
            }

            JSONArray fileList = resive.getJSONArray("fileList");
            if(rtn.isSuccess() && fileList!=null && fileList.size()>0){//附件信息
                for (int i=0;i<fileList.size();i++){
                    JSONObject fjxx = JSONObject.parseObject(fileList.get(i).toString());
                    FlzlDataVo flzlDataVo = JSONObject.parseObject(fileList.get(i).toString(), FlzlDataVo.class);
                    flzlDataVo.setFjbh(fjxx.getString("id"));//附件ID
                    flzlDataVo.setYwbh(fjxx.getString("reciid"));//业务id
                    flzlDataVo.setWjzllx(fjxx.getString("documentType"));//附件类型名称
                    flzlDataVo.setWjm(fjxx.getString("name"));//文件名称
                    flzlDataVo.setWjlj(fjxx.getString("path"));//路径
                    flzlDataVo.setWjurl(fjxx.getString("url"));//url
                    flzlDataVo.setWjlx(fjxx.getString("type"));//扩展名
                    flzlDataVo.setWjdx(fjxx.getString("fileSize"));//文件大小
                    flzlDataVo.setWjnr(fjxx.getString("content"));//文件内容
                    flzlDataVo.setWjgs(fjxx.getString("ownership"));//文件归属
                    flzlDataVo.setPzbh(fjxx.getString("configId"));//配置ID
                    flzlDataVo.setYxbz("1".equals(fjxx.getString("deleted")) ? "N":"Y");
                    flzlDataVo.setLrrdm(fjxx.getString("creator"));//录入人
                    flzlDataVo.setLrrq(fjxx.getString("createTime"));//录入时间
                    flzlDataVo.setXgrdm(fjxx.getString("updater"));//修改人
                    flzlDataVo.setXgrq(fjxx.getString("updateTime"));//修改时间
                    flzlDataVo.setGdzt("未归档");
                    String ywlx = "clfgr";
                    flzlDataVo.setYwlx(ywlx);//业务类型
                    if (!StringUtils.isEmpty(flzlDataVo.getFjbh()) && !StringUtils.isEmpty(flzlDataVo.getYwbh())){
                        flzlService.zfFlzl(flzlDataVo.getFjbh(),ywlx);
                        flzlService.addFlzl(flzlDataVo);
                    }
                }
            }

            JSONObject rcmt = resive.getJSONObject("rcmt");
            if(rtn.isSuccess() && rcmt!=null && rcmt.size()>0){//房管材料
                paramList.clear();
                paramList.add(esbService.addParam("id",rcmt.getString("id"),"string"));
                paramList.add(esbService.addParam("reciid",rcmt.getString("reciid"),"string"));
                String hallWindowNum = StringUtils.isEmpty(rcmt.getString("hallWindowNum"))?"":rcmt.getString("hallWindowNum");
                paramList.add(esbService.addParam("hallWindowNum",hallWindowNum,"string"));
                String acceptanceNum = StringUtils.isEmpty(rcmt.getString("acceptanceNum"))?"":rcmt.getString("acceptanceNum");
                paramList.add(esbService.addParam("acceptanceNum",acceptanceNum,"string"));
                realEstateUnitNum = StringUtils.isEmpty(rcmt.getString("realEstateUnitNum"))?"":rcmt.getString("realEstateUnitNum");
                paramList.add(esbService.addParam("realEstateUnitNum",realEstateUnitNum,"string"));
                paramList.add(esbService.addParam("sellerName",rcmt.getString("sellerName"),"string"));
                paramList.add(esbService.addParam("sellerIdNum",rcmt.getString("sellerIdNum"),"string"));
                paramList.add(esbService.addParam("buyerName",rcmt.getString("buyerName"),"string"));
                paramList.add(esbService.addParam("buyerIdNum",rcmt.getString("buyerIdNum"),"string"));
                paramList.add(esbService.addParam("pushStatus",rcmt.getString("pushStatus"),"string"));
                remark = StringUtils.isEmpty(rcmt.getString("remark"))?"":rcmt.getString("remark");
                paramList.add(esbService.addParam("remark",remark,"string"));
                paramList.add(esbService.addParam("creator",rcmt.getString("creator"),"string"));
                paramList.add(esbService.addParam("createTime",rcmt.getString("createTime"),"string"));
                paramList.add(esbService.addParam("updater",rcmt.getString("updater"),"string"));
                paramList.add(esbService.addParam("updateTime",rcmt.getString("updateTime"),"string"));
                paramList.add(esbService.addParam("deleted",rcmt.getString("deleted"),"string"));
                String fgzxblr = StringUtils.isEmpty(rcmt.getString("fgzxblr")) ? "" : rcmt.getString("fgzxblr");
                paramList.add(esbService.addParam("fgzxblr",fgzxblr,"string"));
                String fgzxblrbm = StringUtils.isEmpty(rcmt.getString("fgzxblrbm")) ? "" : rcmt.getString("fgzxblrbm");
                paramList.add(esbService.addParam("fgzxblrbm",fgzxblrbm,"string"));
                rtn = esbService.queryEsb("30000028","1", "10","0","false",paramList);
                if(!rtn.isSuccess() && rtn.getMsg().contains("ORA-00001")){
                    rtn = esbService.queryEsb("30000040","1", "10","0","false",paramList);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail(e.getMessage());
        }
        if(!rtn.isSuccess()){
            try{
                String id = resive.getString("id");
                log.info("接收返回数据发生异常回退数据中"+id);
                CommonResult htrrtn = new CommonResult();
                paramList.clear();
                paramList.add(esbService.addParam("id",id,"string"));
                paramList.add(esbService.addParam("deleted","1","string"));
                htrrtn = esbService.queryEsb("30000031","1", "10","0","false",paramList);

                JSONArray sellerInfo = resive.getJSONArray("sellerInfo");
                if(htrrtn.isSuccess() && sellerInfo!=null && sellerInfo.size()>0){//卖方信息
                    for (int i=0;i<sellerInfo.size();i++){
                        JSONObject seller = sellerInfo.getJSONObject(i);
                        paramList.clear();
                        paramList.add(esbService.addParam("id",seller.getString("id"),"string"));
                        paramList.add(esbService.addParam("deleted","1","string"));
                        htrrtn = esbService.queryEsb("30000032","1", "10","0","false",paramList);
                    }
                }
                JSONArray buyerInfo = resive.getJSONArray("buyerInfo");
                if(htrrtn.isSuccess() && buyerInfo!=null && buyerInfo.size()>0){//买方信息
                    for (int i=0;i<buyerInfo.size();i++){
                        JSONObject buyer = buyerInfo.getJSONObject(i);
                        paramList.clear();
                        paramList.add(esbService.addParam("id",buyer.getString("id"),"string"));
                        paramList.add(esbService.addParam("deleted","1","string"));
                        htrrtn = esbService.queryEsb("30000033","1", "10","0","false",paramList);
                        if(htrrtn.isSuccess()){
                            JSONArray jtcy = buyer.getJSONArray("jtcyList");
                            if(htrrtn.isSuccess() && jtcy!=null && jtcy.size()>0){
                                for (int j=0;j<jtcy.size();j++){
                                    JSONObject jtcyxx = jtcy.getJSONObject(j);
                                    paramList.clear();
                                    paramList.add(esbService.addParam("uuid",jtcyxx.getString("uuid"),"string"));
                                    paramList.add(esbService.addParam("yxbz","N","string"));
                                    htrrtn = esbService.queryEsb("61000454","1", "10","0","false",paramList);
                                }
                            }
                        }
                    }
                }
                JSONObject house = resive.getJSONObject("houseInfo");
                if(htrrtn.isSuccess() && house!=null && house.size()>0){//个人住宅详情
                    paramList.clear();
                    paramList.add(esbService.addParam("id",house.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000034","1", "10","0","false",paramList);
                }

                JSONObject business = resive.getJSONObject("businessInfo");
                if(htrrtn.isSuccess() && business!=null && business.size()>0){//商服用房
                    paramList.clear();
                    paramList.add(esbService.addParam("id",business.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000035","1", "10","0","false",paramList);
                }

                JSONObject office = resive.getJSONObject("officeInfo");
                if(htrrtn.isSuccess() && office!=null && office.size()>0){//办公用房
                    paramList.clear();
                    paramList.add(esbService.addParam("id",office.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000036","1", "10","0","false",paramList);
                }

                JSONObject apartment = resive.getJSONObject("apartmentInfo");
                if(htrrtn.isSuccess() && apartment!=null && apartment.size()>0){//公寓
                    paramList.clear();
                    paramList.add(esbService.addParam("id",apartment.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000037","1", "10","0","false",paramList);
                }

                JSONObject parking = resive.getJSONObject("parkingInfo");
                if(htrrtn.isSuccess() && parking!=null && parking.size()>0){//车库
                    paramList.clear();
                    paramList.add(esbService.addParam("id",parking.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000038","1", "10","0","false",paramList);
                }

                JSONArray fileList = resive.getJSONArray("fileList");
                if(htrrtn.isSuccess() && fileList!=null && fileList.size()>0){//附件信息
                    for (int i=0;i<fileList.size();i++){
                        JSONObject file = fileList.getJSONObject(i);
                        paramList.clear();
                        paramList.add(esbService.addParam("id",file.getString("id"),"string"));
                        paramList.add(esbService.addParam("deleted","1","string"));
                        htrrtn = esbService.queryEsb("30000030","1", "10","0","false",paramList);
                    }
                }

                JSONObject rcmt = resive.getJSONObject("rcmt");
                if(htrrtn.isSuccess() && rcmt!=null && rcmt.size()>0){//房管材料
                    paramList.clear();
                    paramList.add(esbService.addParam("id",rcmt.getString("id"),"string"));
                    paramList.add(esbService.addParam("deleted","1","string"));
                    htrrtn = esbService.queryEsb("30000040","1", "10","0","false",paramList);
                }
                if(htrrtn.isSuccess()){
                    rtn.setMsg(rtn.getMsg()+",数据回退成功！");
                }
            }catch (Exception e){
                e.printStackTrace();
                rtn.setFail("回退数据发生异常"+e.getMessage());
            }
        }
        rtn.setTime(System.currentTimeMillis()-s);
        log.info("接收返回数据："+rtn.toString());
        return rtn;
    }
    /**
     * 获取审批结果
     * @param resive
     * @return
     */
    public CommonResult hqspjg(JSONObject resive){
        String ids = resive.getString("ids");
        CommonResult result = new CommonResult();
        List<String> idList = JSONUtil.parseArray(ids).toList(String.class);
        if(!ObjectUtils.isEmpty(idList)){
            List<Map<String,Object>> paramList = new ArrayList<>();
            List<Map<String,Object>> resultList = new ArrayList<>();
            for(String id : idList){
                paramList.clear();
                paramList.add(esbService.addParam("id",id,"string"));
                result = esbService.queryEsb("30000009","1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray data = (JSONArray)result.getData();
                    if(data.size() > 0){
                        Map<String,Object> resultMap = new HashMap<>();
                        JSONObject jsonObject = data.getJSONObject(0);
                        String applyStatus = jsonObject.getString("applyStatus");
                        String applyRemark = jsonObject.getString("applyRemark");
                        String rejectTarget = jsonObject.getString("rejectTarget");
                        String xxgmk = jsonObject.getString("xxgmk");
                        String xcqwj = jsonObject.getString("xcqwj");
                        resultMap.put("id",id);
                        resultMap.put("applyStatus",applyStatus);
                        resultMap.put("applyRemark",applyRemark);
                        resultMap.put("rejectTarget",rejectTarget);
                        resultMap.put("xxgmk",xxgmk);
                        resultMap.put("xcqwj",xcqwj);
                        resultList.add(resultMap);
                    }
                }
            }
            result.setSuccess(resultList);
        }
        return result;
    }
}