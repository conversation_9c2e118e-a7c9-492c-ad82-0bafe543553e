package cn.com.servyou.sjlq.services;

//import cn.com.servyou.sjlq.log.Timing;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.model.ResultVo;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Component
@Service
public class JkptService {

    private static Logger LOG = LoggerFactory.getLogger(JkptService.class);

    public final static String jkpt_channelid = "SYQD";

    public final static String jkpt_tranid = "SY";

    public final static String jkpt_wsdl_url = "http://98.9.1.218/bondeWebService?wsdl";

    public final static String jkpt_wsdl_method = "requestService";

    //请求报文模板
    public static final String SendXmlModule = "<service xmlns=\"http://www.chinatax.gov.cn/spec/\" " +
            "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">" +
            "<head>" +
            "<tran_id>@tran_id@</tran_id>" +
            "<channel_id>@channel_id@</channel_id>" +
            "<tran_seq>@tran_seq@</tran_seq>" +
            "<tran_date>@tran_date@</tran_date>" +
            "<tran_time>@tran_time@</tran_time>" +
            "</head>" +
            "<body>" +
            "<![CDATA[@CDATA@]]>" +
            "</body>" +
            "</service>";

    /***
     * 调用接口平台查询数据
     * 入参：sqlxh、queryparams page pageindex pagesize
     * 出参：queryDataVo
     * */
    //@Timing
    public ResultVo queryJkptData(String sqlxh, String queryparams, boolean page, String pageIndex, String pageSize){
        //校验
        /*if (StringUtils.isEmpty(sqlxh)){
            return ResultVo.valueOfError("接口参数为空");
        }
        if (page){
            if (StringUtils.isEmpty(pageIndex) || StringUtils.isEmpty(pageSize) || Integer.valueOf(pageSize) <= 0 || Integer.valueOf(pageSize) > 10000){
                return ResultVo.valueOfError("分页参数为空");
            }
        }
        String url = jkpt_wsdl_url;
        String serviceName = jkpt_wsdl_method;
        String qddm = jkpt_channelid;
        String fwdm = jkpt_tranid;
        //拼接业务报文
        String ywbw = "<sqlxh>" + sqlxh + "</sqlxh>";
        if (!StringUtils.isEmpty(queryparams)){
            ywbw += "<queryparams>" + queryparams + "</queryparams>";
        }
        if (page){
            ywbw += "<dototal>true</dototal>" +
                    "<pageindex>" + pageIndex + "</pageindex>" +
                    "<pagesize>" + pageSize + "</pagesize>";
        } else {
            ywbw += "<dototal>false</dototal>";
        }
        //拼接完整请求报文
        String tranSeq = CommonUtils.getUniqueID();
        String datetime = CommonUtils.getDateTimeSimple();
        String requestXml = SendXmlModule.replace("@tran_id@", fwdm)
                .replace("@channel_id@", qddm)
                .replace("@tran_date@", datetime.substring(0, 8))
                .replace("@tran_time@", datetime.substring(8))
                .replace("@tran_seq@", tranSeq)
                .replace("@CDATA@", ywbw);
        LOG.info("{} 接口平台请求报文 {}", sqlxh, requestXml);
        ResultVo resultVo = WebServiceHelper.callWebService(sqlxh, requestXml, url, serviceName);
        if (!resultVo.isSuccess()){
            return resultVo;
        }
        String response = (String) resultVo.getValue();
        LOG.info("{} 接口平台返回报文 {}", sqlxh, response);
        ResultVo cdataResultVo = ServiceHelps.getCDataContent(response);
        if (cdataResultVo.isSuccess()){
            //真实数据<result></result>
            String realData = ServiceHelps.returnXmlStrByCode((String) cdataResultVo.getValue(), "<result>(.*)</result>");
            //转换成对象
            QueryDataVo queryDataVo = QueryDataVo.getQueryData(realData);
            return ResultVo.valueOfSuccess(queryDataVo);
        }*/
        return ResultVo.valueOfSuccess();
    }

    /***
     * 调用接口平台查询数据
     * 入参：sqlxh、queryparams page pageindex pagesize
     * 出参：queryDataVo
     * */
    //@Timing
    public ResultVo queryJkptDataEx(String sqlxh, String queryparams, boolean page, String pageIndex, String pageSize){
        //校验
        /*if (StringUtils.isEmpty(sqlxh)){
            return ResultVo.valueOfError("接口参数为空");
        }
        if (page){
            if (StringUtils.isEmpty(pageIndex) || StringUtils.isEmpty(pageSize) || Integer.valueOf(pageSize) <= 0 || Integer.valueOf(pageSize) > 10000){
                return ResultVo.valueOfError("分页参数为空");
            }
        }
        String url = jkpt_wsdl_url;
        String serviceName = jkpt_wsdl_method;
        String qddm = jkpt_channelid;
        String fwdm = jkpt_tranid;
        //拼接业务报文
        String ywbw = "<sqlxh>" + sqlxh + "</sqlxh>";
        if (!StringUtils.isEmpty(queryparams)){
            ywbw += "<queryparams>" + queryparams + "</queryparams>";
        }
        if (page){
            ywbw += "<dototal>true</dototal>" +
                    "<pageindex>" + pageIndex + "</pageindex>" +
                    "<pagesize>" + pageSize + "</pagesize>";
        } else {
            ywbw += "<dototal>false</dototal>";
        }
        //拼接完整请求报文
        String tranSeq = CommonUtils.getUniqueID();
        String datetime = CommonUtils.getDateTimeSimple();
        String requestXml = SendXmlModule.replace("@tran_id@", fwdm)
                .replace("@channel_id@", qddm)
                .replace("@tran_date@", datetime.substring(0, 8))
                .replace("@tran_time@", datetime.substring(8))
                .replace("@tran_seq@", tranSeq)
                .replace("@CDATA@", ywbw);
        LOG.info("{} 接口平台请求报文 {}", sqlxh, requestXml);
        ResultVo resultVo = WebServiceHelper.callWebService(sqlxh, requestXml, url, serviceName);
        if (!resultVo.isSuccess()){
            return resultVo;
        }
        String response = (String) resultVo.getValue();
        LOG.info("{} 接口平台返回报文 {}", sqlxh, response);
        ResultVo cdataResultVo = ServiceHelps.getCDataContent(response);
        if (cdataResultVo.isSuccess()){
            //真实数据<result></result>
            String realData = ServiceHelps.returnXmlStrByCode((String) cdataResultVo.getValue(), "<result>(.*)</result>");
            //转换成对象
            QueryDataVo queryDataVo = QueryDataVo.getQueryData(realData);
            if ("000".equals(queryDataVo.getCode())) {
                return ResultVo.valueOfSuccess(queryDataVo);
            } else {
                return ResultVo.valueOfError(queryDataVo.getMessage());
            }
        }*/
        return ResultVo.valueOfSuccess();
    }

    //@Timing
    public CommonResult queryJkptDataEx(JSONObject ywsjObject){
        CommonResult commonResult = new CommonResult();
        /*String sqlxh = ywsjObject.getString("sqlxh");
        boolean page = "true".equalsIgnoreCase(ywsjObject.getString("dototal"));
        String pageIndex = ywsjObject.getString("pageIndex");
        String pageSize = ywsjObject.getString("pageSize");
        String queryparams = ywsjObject.getString("queryparams");
        //校验
        if (StringUtils.isEmpty(sqlxh)){
            commonResult.setResult("99", "sqlxh不能为空", null, 0);
            return commonResult;
        }
        if (page){
            if (StringUtils.isEmpty(pageIndex) || StringUtils.isEmpty(pageSize) || Integer.valueOf(pageSize) <= 0 || Integer.valueOf(pageSize) > 10000){
                commonResult.setResult("99", "分页参数错误", null, 0);
                return commonResult;
            }
        }
        String url = jkpt_wsdl_url;
        String serviceName = jkpt_wsdl_method;
        String qddm = jkpt_channelid;
        String fwdm = jkpt_tranid;
        //拼接业务报文
        String ywbw = "<sqlxh>" + sqlxh + "</sqlxh>";
        if (!StringUtils.isEmpty(queryparams)){
            ywbw += "<queryparams>" + queryparams + "</queryparams>";
        }
        if (page){
            ywbw += "<dototal>true</dototal>" +
                    "<pageindex>" + pageIndex + "</pageindex>" +
                    "<pagesize>" + pageSize + "</pagesize>";
        } else {
            ywbw += "<dototal>false</dototal>";
        }
        //拼接完整请求报文
        String tranSeq = CommonUtils.getUniqueID();
        String datetime = CommonUtils.getDateTimeSimple();
        String requestXml = SendXmlModule.replace("@tran_id@", fwdm)
                .replace("@channel_id@", qddm)
                .replace("@tran_date@", datetime.substring(0, 8))
                .replace("@tran_time@", datetime.substring(8))
                .replace("@tran_seq@", tranSeq)
                .replace("@CDATA@", ywbw);
        LOG.info("{} 接口平台请求报文 {}", sqlxh, requestXml);
        ResultVo resultVo = WebServiceHelper.callWebService(sqlxh, requestXml, url, serviceName);
        if (!resultVo.isSuccess()){
            commonResult.setResult("99", resultVo.getMessage(), null, 0);
            return commonResult;
        }
        String response = (String) resultVo.getValue();
        LOG.info("{} 接口平台返回报文 {}", sqlxh, response);
        ResultVo cdataResultVo = ServiceHelps.getCDataContent(response);
        if (cdataResultVo.isSuccess()){
            //真实数据<result></result>
            String realData = ServiceHelps.returnXmlStrByCode((String) cdataResultVo.getValue(), "<result>(.*)</result>");
            //转换成对象
            QueryDataVo queryDataVo = QueryDataVo.getQueryData(realData);
            if ("000".equals(queryDataVo.getCode())) {
                commonResult.setResult("00", "成功", queryDataVo, queryDataVo.getTotal());
                return commonResult;
            } else {
                commonResult.setResult("99", queryDataVo.getMessage(), null, 0);
                return commonResult;
            }
        }
        commonResult.setResult("99", cdataResultVo.getMessage(), null, 0);*/
        return commonResult;
    }
}
