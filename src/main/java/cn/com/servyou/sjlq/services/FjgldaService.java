package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import com.aisino.cq.utlis.EncodedTools;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Service
public class FjgldaService {
    private static final Logger log = LoggerFactory.getLogger(FjgldaService.class);

    private static final String fjywlx = "fjglda";
    //档案新增sql序号
    private static final String daInsertSqlxh = "30000055";
    //档案查询sql序号
    private static final String daQuerySqlxh = "30000057";
    //档案更新sql序号
    private static final String daUpdateSqlxh = "30000059";

    @Autowired
    private DsjptService esbService;
    @Autowired
    private FlzlService flzlService;
    @Autowired
    private DzzlkService dzzlkService;
    @Resource(name = "sfzzThreadPool")
    private Executor executor;
    /**
     * 入库附件管理档案主表数据
     * @param file
     * @return
     */
    public CommonResult jswbsj(JSONObject file){
        List<Map<String,Object>> paramList = new ArrayList<>();
        String uuid = file.getString("uuid");
        paramList.add(esbService.addParam("uuid",uuid,"string"));
        CommonResult result = esbService.queryEsb(daQuerySqlxh, "1", "100", "0", "false", paramList);
        if(result.isSuccess()){
            JSONArray data = (JSONArray)result.getData();
            if(data != null && data.size() > 0){
                log.info("档案信息已存在，无需重复推送");
            }else {
                log.info("档案信息不存在，开始推送");
                paramList.clear();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                paramList.add(esbService.addParam("ywbh",file.getString("ywbh"),"string"));
                paramList.add(esbService.addParam("ywlx",file.getString("ywlx"),"string"));
                paramList.add(esbService.addParam("gdnd",file.getString("gdnd"),"string"));
                paramList.add(esbService.addParam("gdrq",file.getString("gdrq"),"string"));
                paramList.add(esbService.addParam("blry",file.getString("blry"),"string"));
                paramList.add(esbService.addParam("fjsl",file.getString("fjsl"),"string"));
                paramList.add(esbService.addParam("ywid",file.getString("ywid"),"string"));
                paramList.add(esbService.addParam("gdzt","未归档","string"));
                paramList.add(esbService.addParam("gdfs",file.getString("gdfs"),"string"));
                paramList.add(esbService.addParam("nsrmc",file.getString("nsrmc"),"string"));
                paramList.add(esbService.addParam("nsrsbh",file.getString("nsrsbh"),"string"));
                paramList.add(esbService.addParam("lrrdm", org.apache.commons.lang.StringUtils.isBlank(file.getString("lrrdm")) ? "1":file.getString("lrrdm"),"string"));
                paramList.add(esbService.addParam("lrrq",file.getString("lrrq"),"string"));
                paramList.add(esbService.addParam("xgrdm", StringUtils.isBlank(file.getString("xgrdm")) ? "1" :file.getString("xgrdm"),"string"));
                paramList.add(esbService.addParam("xgrq",file.getString("xgrq"),"string"));
                paramList.add(esbService.addParam("yxbz",file.getString("yxbz"),"string"));
                //档案主表数据入库
                result = esbService.queryEsb(daInsertSqlxh,"1", "10","0","false",paramList);
                if(result.isSuccess()){
                    //推送电子资料库
                    result = ywqbgd(uuid,file.getString("lrrdm"),"政务端推送");
                }
            }
        }
        return result;
    }
    /**
     * 将该业务下全部未归档附件进行归档
     * @param zbuuid
     * @return
     */
    private CommonResult ywqbgd(String zbuuid, String userCode, String userName){
        //大致分为两类，一类是股权变更和征纳互动是手动归档的只需要处理归档表相关就可以了，一类是存量房个人、存量房企业和大额欠税是自动归档的，需要先找到业务表的相关数据再进行归档
        List<Map<String,Object>> paramList = new ArrayList<>();
        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
        //查询主表获取业务类型
        CommonResult rtn = esbService.queryEsb(daQuerySqlxh, "1", "10", "0", "false", paramList);
        if(rtn.isSuccess()){
            JSONArray daJsonArray = (JSONArray)rtn.getData();
            JSONObject daJsonObject = daJsonArray.getJSONObject(0);
            String ywlx = daJsonObject.getString("ywlx");
            String ywid = daJsonObject.getString("ywid");
            String ywbh = daJsonObject.getString("ywbh");
            String nsrmc = daJsonObject.getString("nsrmc");
            String nsrsbh = daJsonObject.getString("nsrsbh");
            if(!EncodedTools.isFullContains("股权变更，征纳互动",ywlx)){
                //查询要归档的数据
                JSONObject ywsj = new JSONObject();
                ywsj.put("ywbh",zbuuid);
                ywsj.put("gdzt","未归档");
                ywsj.put("ywlx",fjywlx);
                rtn = flzlService.getFlzlList(ywsj);
                if(rtn.isSuccess()){
                    List<String> fjuuidList = new ArrayList<>();
                    // 2. 获取data字段（字符串）
                    JSONArray ja = (JSONArray)rtn.getData();
                    if(ja.size() > 0){
                        for(int i=0;i<ja.size();i++){
                            JSONObject obj = ja.getJSONObject(i);
                            fjuuidList.add(obj.getString("uuid"));
                        }
                        String join = StringUtils.join(fjuuidList, ",");
                        Map<String,String> param = new HashMap<>();
                        param.put("nsrmc",nsrmc);
                        param.put("nsrsbh",nsrsbh);
                        param.put("ywlx",ywlx);
                        param.put("ywid",ywid);
                        param.put("ywbh",ywbh);
                        param.put("fjuuids",join);
                        rtn = dzzlk(param,zbuuid,userCode,userName);
                    }else {
                        rtn.setSuccess(null);
                    }
                }
            }else {
                throw new RuntimeException("不支持的业务类型："+ywlx);
            }
        }
        return rtn;
    }
    /**
     *function 归档
     * @param param 参数集合
     * @param zbuuid 档案表uuid
     */
    private CommonResult dzzlk(Map<String,String> param, String zbuuid, String userCode, String userName){
        //本次要归档的附件uuid集合
        String fjuuids = param.get("fjuuids");
        CommonResult result = new CommonResult();
        try{
            //关联单号
            String gldh = esbService.MD5(zbuuid);
            //登记序号
            String djxh= param.get("nsrsbh");
            //纳税人名称
            String nsrmc = param.get("nsrmc");
            //纳税人识别号
            String nsrsbh = param.get("nsrsbh");
            CommonResult initDzzlkRtn = dzzlkService.init(gldh, djxh, nsrsbh, nsrmc);
            if(initDzzlkRtn.isSuccess()){
                String[] uuids = StringUtils.split(fjuuids, ",");
                List<CompletableFuture<String>> futures = new ArrayList<>();
                for(String fjuuid : uuids){
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                        CommonResult rtn = new CommonResult();
                        List<Map<String,Object>> paramList = new ArrayList<>();
                        paramList.clear();
                        JSONObject ywsj = new JSONObject();
                        ywsj.put("fjbh",fjuuid);
                        ywsj.put("ywlx",fjywlx);
                        CommonResult commonResult = flzlService.getFlzlList(ywsj);
                        if (commonResult.isSuccess()) {
                            //附件数据
                            JSONArray ja = (JSONArray)commonResult.getData();
                            if (ja.size() == 0) {
                                return "success";
                            }
                            JSONObject obj = ja.getJSONObject(0);
                            String uuid = obj.getString("uuid");
                            String wjlx = obj.getString("wjlx");
                            String wjzllx = obj.getString("wjzllx");
                            String gdzt = obj.getString("gdzt");
                            String wjurl = obj.getString("wjurl");
                            if("已归档".equals(gdzt)){
                                return "success";
                            }
                            String zllxdm = "";
                            wjzllx = wjzllx == null ? "" : wjzllx;
                            if (wjzllx.contains("申报表")) {//税费申报表
                                zllxdm = "125131";
                            } else if (wjzllx.contains("凭证")) {//完税凭证
                                zllxdm = "125132";
                            } else if (wjzllx.contains("采集表")) {//房屋采集表
                                zllxdm = "125133";
                            } else if (wjzllx.contains("税")) {//个人所得税扣除
                                zllxdm = "125134";
                            } else if (wjzllx.contains("土地")) {//土地增值税扣除
                                zllxdm = "125135";
                            } else if (wjzllx.contains("受理")) {//不动产登记受理通知单
                                zllxdm = "125136";
                            } else if (wjzllx.contains("采集表")) {//存量房交易采集表
                                zllxdm = "125137";
                            } else if (wjzllx.contains("房产证")) {//房产证
                                zllxdm = "123493";
                            } else if (wjzllx.contains("身份证")) {//身份证
                                zllxdm = "001832";//对应资料库000750
                            } else if (wjzllx.contains("合同")) {//简易合同
                                zllxdm = "000109";//对应资料库000315
                            } else if (wjzllx.contains("营业执照")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("委托书")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("股权变更")) {
                                zllxdm = "000000"; //TODO
                            } else if (wjzllx.contains("征纳互动")) {
                                zllxdm = "000000"; //TODO
                            } else {
                                zllxdm = "000000";//其他资料
                            }
                            if (StringUtils.isNotEmpty(zllxdm)) {
                                try {
                                    String zldata = "";
                                    //通过文件url读取文件流
                                    try (InputStream inputStream = new URL(wjurl).openStream()) {
                                        byte[] bytes = IoUtil.readBytes(inputStream);
//                                        zldata = new String(bytes, Charset.defaultCharset());
                                        zldata = Base64Utils.encodeToString(bytes);
                                    }
                                    if (StringUtils.isNotEmpty(zldata)) {
                                        rtn.addResult(dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata, wjlx));
                                        if (rtn.isSuccess()) {
                                            Map<String, Object> zlscObj = (Map<String, Object>) rtn.getData();
                                            String zldjxh = (String) zlscObj.get("zldjxh");
                                            String zlurlnw = (String) zlscObj.get("zlurlnw");
                                            //更新业务附件表状态
                                            ywsj.clear();
                                            ywsj.put("fjbh",fjuuid);
                                            ywsj.put("ywbh",zbuuid);
                                            ywsj.put("ywlx",fjywlx);
                                            ywsj.put("gdzt","已归档");
                                            ywsj.put("zlurlnw",zlurlnw);
                                            ywsj.put("zldjxh",zldjxh);
                                            ywsj.put("gldh",gldh);
                                            commonResult = flzlService.updateFlzl(ywsj);
                                            if(!commonResult.isSuccess()){
                                                rtn.setFail(commonResult.getMsg());
                                            }
                                        }
                                    } else {
                                        rtn.setFail(fjuuid + "文件不存在");
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.info(uuid + "归档时发生异常" + e.getMessage());
                                    rtn.setFail(e.getMessage());
                                }
                            }
                        }
                        return "success";
                    }, executor).exceptionally(ex -> {
                        log.error("uuid为：【" + fjuuid + "】的数据电子归档失败，原因为：" + ex.getMessage());
                        return "【uuid："+fjuuid+"】";
                    });
                    futures.add(future);
                }
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                //默认推送成功
                boolean flag = true;
                StringBuffer stringBuffer = new StringBuffer("以下数据归档异常：");
                //等待所有异步任务完成
                allFutures.get(300L, TimeUnit.SECONDS);
                for(CompletableFuture<String> future : futures){
                    String taskResult = future.get();
                    if(!StringUtils.equals(taskResult,"success")){
                        //有推送失败的，则整体标记为失败
                        flag = false;
                        stringBuffer.append(taskResult);
                    }
                }
                if(flag){
                    result = dzzlkService.sxzttz(gldh, djxh, nsrsbh, nsrmc);
                    if(result.isSuccess()){
                        // 通知成功后，插入主表
                        param.put("zbuuid",zbuuid);
                        result = czdasj(param, userCode, false);
                    }
                }else {
                    result.setFail(stringBuffer.toString());
                }
            }
        }catch (Exception e){
            log.error("归档发生异常：{}",e.getMessage());
            result.setFail(e.getMessage());
        }
        return result;
    }
    /**
     * 操作档案主表数据
     * @param param  参数集合
     * @param userCode 用户编码
     * @param zcflag 是否暂存
     * @return
     */
    private CommonResult czdasj(Map<String,String> param, String userCode, boolean zcflag){
        //业务类型 股权变更、征纳互动
        String ywlx = param.get("ywlx");
        //业务编号
        String ywbh = param.get("ywbh");
        //附件uuid集合
        String fjuuids = param.get("fjuuids");
        //纳税人名称
        String nsrmc = param.get("nsrmc");
        //纳税人识别号
        String nsrsbh = param.get("nsrsbh");
        //主表uuid
        String zbuuid = param.get("zbuuid");
        String[] fjuuidArr = StringUtils.split(fjuuids, ",");
        List<Map<String,Object>> paramList = new ArrayList<>();
        //不为空时，先查询是否存在，存在更新，不存在生成
        paramList.clear();
        paramList.add(esbService.addParam("uuid",zbuuid,"string"));
        CommonResult rtn = esbService.queryEsb(daQuerySqlxh, "1", "10", "0", "false", paramList);
        if(rtn.isSuccess()){
            JSONArray data = (JSONArray) rtn.getData();
            if(data.size() > 0){
                //查询当前已归档的附件数量并重新计算
                JSONObject ywsj = new JSONObject();
                ywsj.put("ywbh",zbuuid);
                ywsj.put("ywlx",fjywlx);
                ywsj.put("gdzt","已归档");
                rtn = flzlService.queryFlzlCountByYwbh(ywsj);
                if(rtn.isSuccess()){
                    int sl = (Integer) rtn.getData();
                    //这个是编辑页面，更新档案主表数据
                    paramList.clear();
                    String gdnd = String.valueOf(LocalDate.now().getYear());
                    String gdrq = LocalDateTimeUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss");
                    String gdzt = "已归档";
                    if(zcflag){
                        gdnd = "-";
                        gdrq = "-";
                        gdzt = "未归档";
                    }
                    paramList.add(esbService.addParam("xgrdm",userCode,"string"));
                    paramList.add(esbService.addParam("fjsl",String.valueOf(sl),"string"));
                    paramList.add(esbService.addParam("ywlx",ywlx,"string"));
                    paramList.add(esbService.addParam("ywbh",ywbh,"string"));
                    paramList.add(esbService.addParam("nsrmc",nsrmc,"string"));
                    paramList.add(esbService.addParam("nsrsbh",nsrsbh,"string"));
                    paramList.add(esbService.addParam("gdzt",gdzt,"string"));
                    paramList.add(esbService.addParam("gdnd",gdnd,"string"));
                    paramList.add(esbService.addParam("gdrq",gdrq,"string"));
                    paramList.add(esbService.addParam("uuid",zbuuid,"string"));
                    rtn = esbService.queryEsb(daUpdateSqlxh,"1", "10","0","false",paramList);
                }
            }
        }
        return rtn;
    }
}