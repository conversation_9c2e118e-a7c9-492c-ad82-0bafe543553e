package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.model.FlzlDataVo;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ClfQyService {
    private static final Logger log = LoggerFactory.getLogger(ClfQyService.class);

    //主表新增sql序号
    private static final String qyInsertSqlxh = "30000041";
    //主表查询sql序号
    private static final String qyQuerySqlxh = "30000047";
    //主表更新sql序号
    private static final String qyUpdateSqlxh = "30000051";
    //主表审批sql序号
    private static final String qyShenpiSqlxh = "30000050";
    //经办人新增sql序号
    private static final String jbrInsertSqlxh = "30000042";
    //经办人查询sql序号
    private static final String jbrQuerySqlxh = "30000048";
    //经办人修改sql序号
    private static final String jbrUpdateSqlxh = "30000052";
    //房屋新增sql序号
    private static final String fwInsertSqlxh = "30000043";
    //房屋修改sql序号
    private static final String fwUpdateSqlxh = "30000053";
    //房屋分页查询
    private static final String fwQueryPageSqlxh = "30000045";
    //房屋单条查询
    private static final String fwQuerySqlxh = "30000046";


    @Autowired
    private DsjptService esbService;

    @Autowired
    FlzlService flzlService;

    /**
     * function 接收外部数据
     */
    public CommonResult jswbsj(JSONObject resive){
        log.info("存量房信息采集企业数据接收接口");
        CommonResult rtn = new CommonResult();
        List<Map<String,Object>> paramList = new ArrayList<>();
        boolean fwExist = false; //房屋是否存在
        try{
            //以房屋信息维度为准先插房屋信息
            JSONObject fwxx = resive.getJSONObject("fwxx");
            if(fwxx!=null && fwxx.size()>0){//房屋信息
                paramList.add(esbService.addParam("uuid",fwxx.getString("uuid"),"string"));
                rtn = esbService.queryEsb(fwQuerySqlxh,"1", "10","0","false",paramList);
                paramList.add(esbService.addParam("zbuuid",fwxx.getString("zbuuid"),"string"));
                paramList.add(esbService.addParam("fwlx",fwxx.getString("fwlx"),"string"));
                paramList.add(esbService.addParam("zldz",fwxx.getString("zldz"),"string"));
                paramList.add(esbService.addParam("jzmj",fwxx.getString("jzmj"),"string"));
                paramList.add(esbService.addParam("cqzh",fwxx.getString("cqzh"),"string"));
                paramList.add(esbService.addParam("jzjg",fwxx.getString("jzjg"),"string"));
                paramList.add(esbService.addParam("sbdj",fwxx.getString("sbdj"),"string"));
                paramList.add(esbService.addParam("sbzj",fwxx.getString("sbzj"),"string"));
                paramList.add(esbService.addParam("fgslbh",fwxx.getString("fgslbh"),"string"));
                paramList.add(esbService.addParam("fgtszt",fwxx.getString("fgtszt"),"string"));
                paramList.add(esbService.addParam("lrrdm",fwxx.getString("lrrdm"),"string"));
                paramList.add(esbService.addParam("lrrq",fwxx.getString("lrrq"),"string"));
                paramList.add(esbService.addParam("xgrdm",fwxx.getString("xgrdm"),"string"));
                paramList.add(esbService.addParam("xgrq",fwxx.getString("xgrq"),"string"));
                paramList.add(esbService.addParam("yxbz",fwxx.getString("yxbz"),"string"));
                if(rtn.isSuccess() && ((JSONArray)rtn.getData()).size() > 0){
                    fwExist = true;
                    log.info("更新房屋表信息");
                    rtn = esbService.queryEsb(fwUpdateSqlxh,"1", "10","0","false",paramList);
                }else {
                    log.info("插入房屋表信息");
                    rtn = esbService.queryEsb(fwInsertSqlxh,"1", "10","0","false",paramList);
                }
            }
            if(rtn.isSuccess()){
                //查询主表信息，若已存在且房屋信息不存在，则不再插入，若已存在且房屋信息也已存在则更新，若不存在则插入
                paramList.clear();
                paramList.add(esbService.addParam("zbuuid",resive.getString("uuid"),"string"));
                rtn = esbService.queryEsb(qyQuerySqlxh,"1", "10","0","false",paramList);
                paramList.clear();
                paramList.add(esbService.addParam("uuid",resive.getString("uuid"),"string"));
                paramList.add(esbService.addParam("sqbh",resive.getString("sqbh"),"string"));
                paramList.add(esbService.addParam("sqzt",resive.getString("sqzt"),"string"));
                paramList.add(esbService.addParam("sqr",resive.getString("sqr"),"string"));
                paramList.add(esbService.addParam("xsfdwzcmc",resive.getString("xsfdwzcmc"),"string"));
                paramList.add(esbService.addParam("xsftyshxydm",resive.getString("xsftyshxydm"),"string"));
                paramList.add(esbService.addParam("xsffddbr",resive.getString("xsffddbr"),"string"));
                paramList.add(esbService.addParam("xsflxdz",resive.getString("xsflxdz"),"string"));
                paramList.add(esbService.addParam("xsflxdh",resive.getString("xsflxdh"),"string"));
                paramList.add(esbService.addParam("gmfdwzcmc",resive.getString("gmfdwzcmc"),"string"));
                paramList.add(esbService.addParam("gmftyshxydm",resive.getString("gmftyshxydm"),"string"));
                paramList.add(esbService.addParam("gmffddbr",resive.getString("gmffddbr"),"string"));
                paramList.add(esbService.addParam("gmflxdz",resive.getString("gmflxdz"),"string"));
                paramList.add(esbService.addParam("gmflxdh",resive.getString("gmflxdh"),"string"));
                paramList.add(esbService.addParam("bhyy",resive.getString("bhyy"),"string"));
                paramList.add(esbService.addParam("lrrdm", StringUtils.isEmpty(resive.getString("lrrdm")) ? "1":resive.getString("lrrdm"),"string"));
                paramList.add(esbService.addParam("lrrq",resive.getString("lrrq"),"string"));
                paramList.add(esbService.addParam("xgrdm",StringUtils.isEmpty(resive.getString("xgrdm")) ? "1" :resive.getString("xgrdm"),"string"));
                paramList.add(esbService.addParam("xgrq",resive.getString("xgrq"),"string"));
                paramList.add(esbService.addParam("yxbz",resive.getString("yxbz"),"string"));
                if(rtn.isSuccess() && ((JSONArray)rtn.getData()).size() > 0){
                    if(fwExist){
                        log.info("更新主表信息");
                        rtn = esbService.queryEsb(qyUpdateSqlxh,"1", "10","0","false",paramList);
                    }else {
                        log.info("存量房企业数据以房屋维度推送，主数据重复是正常现象，无需重复插入！");
                    }
                }else {
                    log.info("插入主表信息");
                    rtn = esbService.queryEsb(qyInsertSqlxh,"1", "10","0","false",paramList);
                }
            }

            JSONArray jbrxxList = resive.getJSONArray("jbrxxList");
            if(rtn.isSuccess() && jbrxxList!=null && jbrxxList.size()>0){//经办人信息
                for (int i=0;i<jbrxxList.size();i++){
                    JSONObject jbrxx = jbrxxList.getJSONObject(i);
                    paramList.clear();
                    paramList.add(esbService.addParam("uuid",jbrxx.getString("uuid"),"string"));
                    rtn = esbService.queryEsb(jbrQuerySqlxh,"1", "10","0","false",paramList);
                    paramList.add(esbService.addParam("zbuuid",jbrxx.getString("zbuuid"),"string"));
                    paramList.add(esbService.addParam("jyjs",jbrxx.getString("jyjs"),"string"));
                    paramList.add(esbService.addParam("jbrxm",jbrxx.getString("jbrxm"),"string"));
                    paramList.add(esbService.addParam("sfzh",jbrxx.getString("sfzh"),"string"));
                    paramList.add(esbService.addParam("lxdh",jbrxx.getString("lxdh"),"string"));
                    paramList.add(esbService.addParam("lxdz",jbrxx.getString("lxdz"),"string"));
                    paramList.add(esbService.addParam("wtrq",jbrxx.getString("wtrq"),"string"));
                    paramList.add(esbService.addParam("lrrdm",StringUtils.isEmpty(jbrxx.getString("lrrdm")) ? "1":jbrxx.getString("lrrdm"),"string"));
                    paramList.add(esbService.addParam("lrrq",jbrxx.getString("lrrq"),"string"));
                    paramList.add(esbService.addParam("xgrdm",StringUtils.isEmpty(jbrxx.getString("xgrdm")) ? "1" :jbrxx.getString("xgrdm"),"string"));
                    paramList.add(esbService.addParam("xgrq",jbrxx.getString("xgrq"),"string"));
                    paramList.add(esbService.addParam("yxbz",jbrxx.getString("yxbz"),"string"));
                    if((rtn.isSuccess() && ((JSONArray)rtn.getData()).size() > 0)){
                        if(fwExist){
                            log.info("更新经办人表信息");
                            rtn = esbService.queryEsb(jbrUpdateSqlxh,"1", "10","0","false",paramList);
                        }else {
                            log.info("存量房企业数据以房屋维度推送，经办人数据重复是正常现象，无需重复插入！");
                        }
                    }else {
                        log.info("插入经办人表信息");
                        rtn = esbService.queryEsb(jbrInsertSqlxh,"1", "10","0","false",paramList);
                    }
                }
            }

            JSONArray fjxxList = resive.getJSONArray("fjxxList");
            if(rtn.isSuccess() && fjxxList!=null && fjxxList.size()>0){//附件信息
                for (int i=0;i<fjxxList.size();i++){
                    JSONObject fjxx = JSONObject.parseObject(fjxxList.get(i).toString());
                    FlzlDataVo flzlDataVo = JSONObject.parseObject(fjxxList.get(i).toString(), FlzlDataVo.class);
                    flzlDataVo.setFjbh(fjxx.getString("uuid"));//附件ID
                    flzlDataVo.setYwbh(fjxx.getString("zbuuid"));//业务id
                    flzlDataVo.setWjzllx(fjxx.getString("wjzllx"));//附件类型名称
                    flzlDataVo.setWjm(fjxx.getString("wjm"));//文件名称
                    flzlDataVo.setWjlj(fjxx.getString("wjlj"));//路径
                    flzlDataVo.setWjurl(fjxx.getString("wjurl"));//url
                    flzlDataVo.setWjlx(fjxx.getString("wjlx"));//扩展名
                    flzlDataVo.setWjdx(fjxx.getString("wjdx"));//文件大小
                    flzlDataVo.setWjnr(fjxx.getString("wjnr"));//文件内容
					flzlDataVo.setWjgs(fjxx.getString("wjgs"));//文件归属
                    flzlDataVo.setPzbh(fjxx.getString("pzbh"));//配置ID
                    flzlDataVo.setYxbz(fjxx.getString("yxbz"));
                    flzlDataVo.setLrrdm(fjxx.getString("lrrdm"));
                    flzlDataVo.setLrrq(fjxx.getString("lrrq"));
                    flzlDataVo.setXgrdm(fjxx.getString("xgrdm"));
                    flzlDataVo.setXgrq(fjxx.getString("xgrq"));
                    flzlDataVo.setGdzt("未归档");
                    String ywlx = "clfqy";
                    flzlDataVo.setYwlx(ywlx);
                    if (!StringUtils.isEmpty(flzlDataVo.getFjbh()) && !StringUtils.isEmpty(flzlDataVo.getYwbh())){
                        flzlService.zfFlzl(flzlDataVo.getFjbh(), ywlx);
                        flzlService.addFlzl(flzlDataVo);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail(e.getMessage());
        }
        if(!rtn.isSuccess()){
            try{
                String uuid = resive.getString("uuid");
                log.info("接收返回数据发生异常回退数据中"+uuid);
                CommonResult htrrtn = new CommonResult();
                paramList.clear();
                paramList.add(esbService.addParam("uuid",uuid,"string"));
                paramList.add(esbService.addParam("yxbz","N","string"));
                htrrtn = esbService.queryEsb(qyUpdateSqlxh,"1", "10","0","false",paramList);

                JSONArray jbrxxList = resive.getJSONArray("jbrxxList");
                if(htrrtn.isSuccess() && jbrxxList!=null && jbrxxList.size()>0){//经办人信息
                    for (int i=0;i<jbrxxList.size();i++){
                        JSONObject jbrxx = jbrxxList.getJSONObject(i);
                        paramList.clear();
                        paramList.add(esbService.addParam("uuid",jbrxx.getString("uuid"),"string"));
                        paramList.add(esbService.addParam("yxbz","N","string"));
                        htrrtn = esbService.queryEsb(jbrUpdateSqlxh,"1", "10","0","false",paramList);
                    }
                }
                JSONObject fwxx = resive.getJSONObject("fwxx");
                if(htrrtn.isSuccess() && fwxx!=null && fwxx.size()>0){//房屋信息
                    paramList.clear();
                    paramList.add(esbService.addParam("uuid",fwxx.getString("uuid"),"string"));
                    paramList.add(esbService.addParam("yxbz","N","string"));
                    htrrtn = esbService.queryEsb(fwUpdateSqlxh,"1", "10","0","false",paramList);
                }
            }catch (Exception e){
                e.printStackTrace();
                rtn.setFail("回退数据发生异常"+e.getMessage());
            }
        }
        return rtn;
    }

    /**
     * 获取审批结果
     * @param resive
     * @return
     */
    public CommonResult hqspjg(JSONObject resive){
        String uuids = resive.getString("uuids");
        CommonResult result = new CommonResult();
        List<String> uuidList = JSONUtil.parseArray(uuids).toList(String.class);
        if(!CollectionUtils.isEmpty(uuidList)){
            List<Map<String,Object>> paramList = new ArrayList<>();
            List<Map<String,Object>> resultList = new ArrayList<>();
            for(String uuid : uuidList){
                paramList.clear();
                paramList.add(esbService.addParam("zbuuid",uuid,"string"));
                result = esbService.queryEsb(qyQuerySqlxh,"1", "10","0","false",paramList);
                if(result.isSuccess()){
                    JSONArray data = (JSONArray)result.getData();
                    if(data.size() > 0){
                        Map<String,Object> resultMap = new HashMap<>();
                        JSONObject jsonObject = data.getJSONObject(0);
                        String sqzt = jsonObject.getString("sqzt");
                        String bhyy = jsonObject.getString("bhyy");
                        resultMap.put("uuid",uuid);
                        resultMap.put("sqzt",sqzt);
                        resultMap.put("bhyy",bhyy);
                        resultList.add(resultMap);
                    }
                }
            }
            result.setSuccess(resultList);
        }
        return result;
    }
}