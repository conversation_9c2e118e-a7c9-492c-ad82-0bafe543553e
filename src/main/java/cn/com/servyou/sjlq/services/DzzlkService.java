package cn.com.servyou.sjlq.services;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.utils.WsUtil;
import cn.com.servyou.sjlq.utils.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 电子资料库
 */
@Service
public class DzzlkService {
    private static final Logger log = LoggerFactory.getLogger(DzzlkService.class);
    @Value("${syzlkUrl}")
    private String syzlkUrl = "";
    @Value("${syzlkMethod}")
    private String syzlkMethod = "";

    public static void main(String[] args) {
        DzzlkService dzzlkService = new DzzlkService();
        String gldh="217874e5f88949a1b82085c0da98a290";
        String djxh="500201199601205648";
        String nsrsbh="500201199601205648";
        String nsrmc="张三";
        //dzzlkService.init(gldh,djxh,nsrsbh,nsrmc);
        String zllxdm="000750";//存量房销售信息表
        String zldata="/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCACWAGQDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD+yP4G+Cb3wj4d8YabcaEuhwX3jfUdU0vTory9vYU0u40DwpFD9kvNQs7DUJ7RJre5ggXUbK31O3hg+waoJtRt7y+l9QltCrSR424UIwIxuxuXCgj8hzjpyDyfBfwzrVt8ONO/t6fz9XvLi8vbmZzI1w7NMsTyXLudkt5cTwTXN3eIqtqEs0upX73OsXeo6jN1GoadNA75TksVDbSRwXUsxUnA3A4JIzljkhcjwsJSnTweHjNxvCjGLV1vzTa5VzXaaldb6Nq65bvtzGt7bMcdJ1YVpKu4SrU2+Sq6aUPaQ5teWSgn6t3b0Z5DqWl6hpt1Jq2giSV8xi/0XMKW+oI0jIkmnySIkVlqVsXd4jNPBpt8XitNbubaGDTNasWDxXYixbULYC4t4YWlvGmkjsI7GOC4aG5+3yalJapaXVvP+4FnchJxdxXcFwLc287jqdbvoNF0y/1O5bbDZQNNKzkBAfnQbjldqeaVBJYbAwLMQpLfzZ/tT/tj+Jdb+IXjWTSNW1HTPAWhX9zp93Z6bfyaefiDrkUjLJp95NEqmLRNNu3tI9ciDCfUJA3h6ZodG0/Vrabz80xOIo0lSwkJTxFS/K42fs4ppc0o6uzUrx35knZSa0MFhI4qUnUmo0YWU9G3NtzXJGyaekZqX8rkr6O7/enx18e/B8XgPW9TtLqKG30mYQSzWd9aXTXd9DJME+xR+HL69uDBJJbF4r3+0La2dSGWUiN2P57eBf8Ago9o1t8bfB3wf8V6BZqfE9nLrFr4hbV9OttJ8jTri5uL9tQuL26gu3vrHTLe8m8m1imuLmxs3ULcTWsrV/PR4k/a4+K/i3U5LmXxn4jTRLOVYJWtX1LQ7GOM27rNpGkvb+JJ3sgwBtri6mvLjU5LWK21Nr4XAjul4mX9oL4heKviNp0ml/2Anh620S81C38MeHdJ8M6Rbtq9u3iCa2QzajbwvrF/qa3tz/Yt9ealqDXV5ayztZm8urq8fzctweOc8TPMK01OUVJezquPNLlUopJLtyxkl9q6fvJpeliVhKVDkp004xlGyqLR6TbdrqzbUbddXu2mf0jfE7/gpnL9t1bS/ht4VmTQrG9ubK51BMT675XnSSSagkV9DqWnz2bqBBALSWe6SC5tVS2kiwG474Zf8FQdEl8S6doXj15hp1/eyWUM0g+zapCqK06FLe5EMF3cxRSFrnTE+zzFEkMFtazNBA/82f8AwsnWYrPTrm9sdf0O+kEtje2F9byWVzDE1zdvpz6bI8f2/wAyGwntZkicPPLqUUi2U9wDbSNbv/GF9rUD6ZriXOoWd4l9qEN1ZvbT37QWNzG02paJFqMiWH9uaDDfC/utFuZRpPiPT5rex1U28N5Ya7ac8cHjqOIeIo4utCTS5m3eNlKWklJWt5tp/ErN6mrWGnTcamHp1ItRTvd1Eru3I07391tJ9d7rV/3Y+EPFXh/x14e03xN4Y1G31TSNRs4Ly1v7KVZ7aaKYZVtyyOYGxLGJLeXbLDIyxOpJDt0qJjq2Aq7skYB2tIuOTxnrnt0wetfzP/8ABPD9rPUvg3rcXgXxj4lXUfDBGgpqN815K+m3nhjXJXvfA3xJspL6WZlmksLm90XxbHI9tqL6vpF9b61Gmv6J4gu9Q/pgimjuYFuoZElilViksUkU0bgSyrujkiLxyKxAZHRmjkRgyMy819HgsVLFwkqkZRxEG41ItP37aqcG3eacWnJrSL0vrc8XFYZYebUG5UnpCT3unP3Xpa6XTe2+t7TyLufOf4xxjsPMAPXoRznpyOeCTEqBQTgHkfN8wx98AfexyWPUE8kdQDTz97AGB8oAwwP3X5wTnJ2j8zgU5GAVuA2cYDBtvORknIAIxuAJznHX+LsOK1oSV+34S/r/ADIFXdvJBGGYDnqA7jPI9c8c8gjJIJop6vEN4ZwGDuCAc4IkkJzjOOWOOeRtIyDklH3fcvLy8vz3u78/NLpWXbaPRpd/6u+rbfZ/BrxDe6t4QsZ9Tha0eWW7WKORpD+7guntW8szhJjGJ4JQWmTzTJ5rySTkm7k9S1GzS6hOFBYKzKQASeScDPILgsARzubOc5NeO6VdraxwxoqRxJHHDFFGqpFHDCGjhjSNSFWNEREVFUIFwqqQrGu4tPEbqrC4ljAVg292OxYwJNzMWbIRBtZuQAM8ggkZYaDpYahSqzcnRowpynu5cjmrt97LX8NlfqxM+fE4ivThyxq1ueMFry882rLrvey1aTSu7a/nn+3f8VV8A/CnxNomlq0+s3ehtLsLOtuL/U9ZstA8L6ZeFJoZBHrepahM0kQJLW1hcIdwlCn+TH9oDWY7eT7HcXnlJp95qmmSuZmW61LxDBcSv4imlupHf7Wuizm/0qO5eOSRbuDUAJFmhtI2/cb9p74uL8Zb3xr47sL6wu/Dln418TanZWb31tbaZJD8PNLtrbwbomoTkzRfZ9X17RdU1C8uZJ7WGG/mZrhxbRK7fzG/FHxNeeNPGr6B4eiv9VijmttC0+K2iae+1LSxfywQQwyrHNGNS8U6kk+s69qETSG2+06hfkJI6SScGHpTxOMr4iU17LDu0ruydOM3yqLb0bjyu/VyitXGbfvUU6FChRjC86jcpSttKbja6vtFtuTv0d0rI5291K51qeG0tdPDWwEun6Bo9k6fZ5jE+25uZlKyFrdnjS5uLtmkudSv3BUvBp8bv9K/C39k6fxva61qHiaK6luJbC2ktrlxOrx3wtrURxQPPGBGYZYJH8yExvF5R8pzEoVvrf8AZg/Yp1a1ns/FnxHtRDqDQ74rNWjEdlHGhS3s4In3hbe3CxReXIFkcMwErEsT+kDeBdK0S0MFvarbwYFrEWdVGxnuJJWcJwzzELG7t86qwKsWjirixePqU5yWG93Wzk+WTsmlaz72T9LtvRX9zD4Gm+aVe0rqFo+bvrZt7NW7pOXS9/5yfFvwk+K3w2sdR0fV4brxHoVmrTaBqdz5j3Fld6Y5ktrS9mnnkilsrqyju7dQ5Xybu30+SKMQ3epQR83pnxHsp/D016L2ysBY3OnNeoAp1Dwn4tsLDULbRr8WfmyTyaR4j0mXUdPS+siYre9XWvCl7DGNY0q8vv6F/Gfw00TVbFopkzHMrmRVLeeWljnSRgD5mSpkZs7T8hXAYnj8d/2tv2O7rw3Z6l8VvhfpVxetFC0HizQLKRy19p53GS6jtSzqlxbSw209qU8kNFBLFLGpIu0WX5gqs3SxFK7UPiWkJScpK9rvRb20e1220efisK1Tm6EtU+u6V3drVeS7/Fa7seDz/EyKJI4bH7bBqegte634aWK7cRyaZqthZL8QfB8EyLcW8WmX7aKPE+kafas2kQ63aXmrQS3GpeLPGmp6h/Vb/wAEcP2n7r47/s3an8NPEerrqfjb4H6hZaR9vmkne51b4b+ILRtS+HOoXzTsyx6hFZ2Ws6BHbQTXM6aboWl3N9dz3N1JLX8PFl4mvbU2d7ETcNp1/HfwJdCZJZ202Tz9S0a6t5IvOSTVLFrjRjZQtMqLDbwmSOWS9ir9kf8AgiB8eJvh1/wUI8M+BppZLnw38Zvh98QfhOZZ57iGys9ais9J+LHgLWnDOIbvUJo/CWreB7di5ZLjxWyRedJCwX1pQ9g6eIjZWmoScU0pc1l0X2X37K125N+HTkp0q9KbdSbvKEtfdlBr3mmrtSSs+tktre9/bjbwzTM4UFirZBP90A455yfQHJ5Iz1z1OlaBbMub2SSY7UPlISkAwWY7iCHfcODkgMABtGCTT0/y8blAC7Fcsdq/KVcnuBhvlI55BGclWrp7W8jRMIm8kL8oAwFDMCxGRxj/ANlBOOa7Lfkn8un3/wBXPMcm01p0v978+v37avU6S1s7SCIxw28MaKzYWOJUXrjJAzknuWJbGAWbAIKo/bpOdsiIOeAobPzOckkZyQVyOQMAAkA5KLen3ry8/wCtezOF0p3fu9e6/mn5+a+/fdnjUV8F3IzfwJ8xPBxIx6HHJ+vbqSK8B/a1+Lx+Dv7NPxd8fx3LRXuneGbnTdJ2yrBKdQ1uG5023ME7yIEkt0kk1BfnSRltSIZFlwx6bxT8RfC3g6SOHxDrUNpfTrb/AGTQoIp77xJqMlwss1rBp2i2aS3s819Ht+x+ZDFDIWVvOYOgr8T/APgsv+0Dq9p8AtH+Htvp2p+FZvFeqf22un6zeWy63qmk6Wk8dnfXmlWJuv7Cijv5ABp9/JczsIpluZYhMbYcrxWHcvYqpzSm1TbhFyVPmc05N/C3FNySu1J8is05H0FDB1nJSlBxppSqKbslNU237qbu1fkvotHJXurv8vvEPx7fQv2W7JYr15rvXTrmmefDMq27Qa/4iu9V1OC6E9wLmd5PDVhA18kroYJ/EFu6MZHBXS/4Jhfs8D4meKvFvxh8UaM17a2l3aW/h+G5hjnW0UXkRRPLa3dLi6jtljS4lDKkEiybVwqCvyisfiLYXfwyOiSXBlj8D2Ej6NaMszXGueI9YvdOlE1w8jSQWg+wxXiRIAsKx29pAymZ7qVv6tf2K/g9rvgP9ib4ZaRb3p0HxV4t8F6ZrniDX7ZX+36ff+KCb7UbmyErExagI7oW9izSs1sZHuk2pCjHix0VhMPWw8J2WJxU/aTvblw1OTUeqSUoqHM/ii+VX5pSb9vAyp13Kq52caVuVxVk6zV5K9/gUZW66pWbTkeieJPGfwp8H6tc+H7zxh4U0XVbSZkewv8AXbCCdQqqLgSwC6jlTy44fNuE2qsCLKZ9q5NQS3+n6ja/bbSWz1G2aOFrS80+5tdQtJg+ZEZLuOWVGLAsADmQgPhSqlq8V+JnwE/Zx8FaRIviOeysNTgEV+99qt3qesa751zE4i1GX7Fa3upaYb2WBrq1dGhkvikzW5niiu2r5z+HfhfwzDrN3P4G8Ria4U2+99Kv4rqy1Syt3uIURmjkd4ZoXV4rq2ugt5ZTqLa7ghKm3Hz85YeHNaq6kmorncWoKMVypKVuWTa1lq5Xum07HtUnVUffcpRtaPupSbukm4RSa0Ts7dW225H2Z5cF3ponCowWKVYmjuAYvMtr64sptxEYVSksRIQZLAPyNjVLY+DLXxAs2hwaMt21+k888TRkF1fz1l8wqhdk2sZWYq0XlFVLq+7HnPiG41TQPC+h2C3kkV/qy6svnMSRFI1/JdO6uAhUmO4SUCNgUdZHL5KCvlyb9nrxZ4+1W81vW/if4n+yC5aOwm+3WtjBps7xK0lnpF5czhbm7Y3KSXKkXOoROI2gCRS1GEqUY1G6lWNOPuxu7p6zk7pb293Xzb3UW3GIpXjL2UJNOLvvu5ebfz7XW90fnt/wVM/YB139nxLj9ovwB4fu7L4f6trOnH4leHo12x+Eddu55o7Dx5ZGWONT4e1W8uba01e1VY4LPU1t74E2t9qVxF+ev7H/AMU5Phj+0b+zJ8QbnUJbaD4fftEfBhL1LOaRW/4R+y+ImkeK9PSGYzEPbyeHz4+8P6xJNFJLc2cdlLKywXZNf1jaH8JPFx+H2tfAX4g+Kb74q/DPxx4evPBniPQ/GlvLd69otjr+m6hp9xc6PqKILoXOlwXwvpdMugi6hZ/aPskVvMbV2/jN8R/Djxl8F/ir8Ufg5q0DzeMvhNceK/Dv2qOOeK41Sw8IXc2t+HNftbaCJ5GuLqHRoZbOQBWTwlrviK/EhsLK5kf6+nUVTBzp0pKaWsJJ35kuZO6ezWjfVaOztc+Ylh5UayqTTlKcnGpCyUbSbi7ySSirapq3TW92f6okUk8Mwa6YSTSLA0h3DHmSQpLLhudwV5MDGMDIPI50obiRgSAIwojUguck+aMkKIy2QrFsEAYzlgo3HkfCvi/w58RvD/hr4geCr1b3wl478NeGfG/hbVIYjENR8NeJtB07XdGvgME7LzTdRtbuMA7DHKJIlAdievtrfIc/MAhAA743FD1BOMnOTnvyQQK6YNuN27pqHJ/ht/n318zxnGEXJNawfLLV739e3r63NGO5wHIkOHbeMqAcHAxgSN6YOecqeCoDEpI4UZTy2AWA5HIVnXJyvXrn8OtFUYnxtc2Pww+BfhPWfHHiS90XwboOmWySa94t1eW5utRmNzeBYoLrUrltR13WrzUb940sdIsvtVzd3Ugh03TVKSIv8rv/AAWZ+LkHjz4rtqHh06imkWXgvSodLW/tLiyvJtLFppFzqMz6NMVvbNxqOrz3nkX0MGpwn7PBPY2863Vun9JP7XepDxz4L1z4fTL8P9J8Gf8ACW/D2+8TeN/G174M8ZeHfC3wl1j4eeLfFvg/4v6PYaZ4k0TUvD978Y/izoVx+zb4EvtSuL+ePXNJ1bxboWgzaXqFo5/ki/4KUxz6t8XvF2oXmltYjUtD8ayWmlvC8I0aS11LVbuKwktLxvtHmwTWcheRWEqyWygKyhGPFiOahi8uw0oKFGU5ShCyUZOKfuxivdSVoy225tbvX38Ap1oY+tUqVJ1fY04RlOcpNJ1m5Xk25e91Xe2r1Z+dPwT0O51Hwj4vmvDAItV17RNHs1QSPfTNqmrHRbaMo0UEsQnZ9SdoEk/0i1RrWa5it5XFf6CKeEorXwB4f0eKdbU6foenRi7W3eTypLTTo41lSKOeAYLu7MgYK3mcNgMa/gH+BviG30bwRe3k0kcqaV8Xfh/f3MflKshtvDV3Lql4rMXZodiyWFy8O8HZNGCojkiZv9Bm61+BfDunGBoRLLp8R3OWMYRlhjTCgMrAjIXklchsAhSeHPa8KmIrUXGMFBVIpKKtONSMHyySa0vBXTvq03d6y9PLMPfCUlTTbnpVk2rW53CPW7Sak0tdLqyWsvwO/bS/4J/eCPi1JfX/AI5+LXxY1fW/7Se+s9UufDh8U+G4GubdjdCy8H6LbWeleHbqOJ7dotSsXTU7n7FbReINa1WztIdNHmv7FX7L/jr4aePdMtrPXtZ17wDpNlp2nj+2/DOreHbiefTbJ7NdSs21HxBq32z+1cCTU7eC2s4LK5htWs0EU9yq/tDrlk9/f3k04Mdu04klEGcFHaeIzM22N1Uxk+Y7syOrNFHIWLQHzrwH488N3HxQ/wCEHtZ45tbtdAv9RfTkkuJ7m1sY59Bjs729jkby1F3/AGnauEkMihy5jYf6Q1fL1MRahDCTcFQjUTp0/ZxSdWTqtWaT11lL5rVu9/oIYfmnOpRpznXjTj7WfPJxVGHLBy5G7JJRgrrVbdZM1Piz4Gk1rwt4k0/RrGM6tb+H4JtFy2yb+0ruK4+zrFmF1jaW5haFSylcojSoUYZ/nk+Nnwh/aL+K3iOKGD9o7S/gMlneXOnaVY3ureKNNu9O0O3tvDFvZX+t6fD8P4I59R1HUrPxt4jmtbbxsmk3MGveE7JLu1vdE8QWmp/1Da5o1zqVxLp+nanpj+LLDSRqP9i3Ra3l1/7BcRTXejadfQXMlsNdlsG1AaZbXStbXWpLb6Z5tq90dSj+O/C+v+DfiHqHivT/ABJpumXup+FPE+oaLqcd7bJe3ejXDw2msWsU6XBlnNtdQX9tqMNjOWSCOWKOS3S4CrV81KhTo11RoyqOSpKVWnGcY/E+a0ot33211SvazfLQp1qkqtOftpQjShWapVZU5pOSXNz811FctmrrVtNtNN+U/sF/DD49eHbeLQL74u+Dfj/4BtLe3eHV7mbW/A/iXw0wuLLUYZfC+i22heKvCnivSY03QzvpeseCy160N7Pocmpyahrd3+Vf/BYf4D3Xg/8Ab2tPHvhjR/LPxE/Z+h8XmCOLybLUfiD4E1Dxdonie0u7m3RRHa6/4Q/4RfRNfnuY1tUtLnTr+/uFtbae2uP6a/BGlaD4PMcVqlnFazrY3GkXtosdrFPFKjb4Io44iLfyoY+Y45niCedHIuUSVPyl/wCCs/ie18MfG39hb4hiVY47Nvj/AG+o28yxyWOsabpusfs+6rc6Tq8TyRvdafd6WNU+32LRTQXtgdT0+ZIvtImPtYPErD05VedTn7SUqkYXjTinFRl7OLfu3Sjd21V0tpN+fjaTr1uRSdOLkk+RKnUco7c7ho7vV7trzP1g/wCCM/xUuPjZ/wAE4P2XPFl8cy6P4O1P4eurKsFzaw/DLxr4y+GWmafdWYkLwPaab4Mt7eMSASvbxQzTQ28kpth+qcFuyAgZ5JYrtHOSx25JOMHLA5z/AA5xzX8+3/Bt9rk7fsOeK/hvcyEt8LP2jfjL8PrKKbJmjtbIaJ4xgMv7ybYgn8fSWkCLLIMWNwysZTMW/opgt2EYEgycqdwPzbQxzycHnb09C2SQSB7+ElGpQ92XN7NuF3fVxqzhdXd7e58rpas+QxK/f1mtIy9nOy0Xv01NKye6U4pvvs3YwfLZcgADJY4IGfvtn17g8Z4z3BNFbJtUychjzyeP7z+reoY/VieSTRW55/t1r7r003XeS/8AbfxXW58Oftj/ALIfxB/aOuPBQ8B+OtB8EaPoVjrEnjHw1eprtpZfE7U1S3XwTbeLrjRIZ0utM8GTpqMugTavp+vDQ5db1SbS9KWeTdJ/LT/wWC+FXxB8BfFrwJe/FrTR/wAJL4q1DyvEurW09le6VrEviODx7HdeItOubKy04XkHiDU5b1Vkm0+wu7csmmTaXYz295E39e2jHxX4k+I/hzXr6fX9R0608N+FbPVbGW/iOhx6hJL8O/EyazZ+GNKg1HRU1mDV9bkfU9Vl1TVJbDQ40i8PX1np0S6pc/ih/wAF4/CMvxa8J/EO78N2q3PiD9n/AMFeAfGN6kdq0Wq3GkW/ji2n1e20mSa4QahLMvjTw3pcFwgjsbM6zq32h5JbN2E5xSk6NLEQjzVcHJ15KGvLh3VlTqu7dlKUKaqRTf2Ut3d+xlNSNGu6E3+7xbhRkno03U92S1d7yUFy6tXtd3bP4rfDdhr1jpvi34fuZINZ1LWxYT2kUi7zqeqPbaHJFG4wZbkGztZ7bETypK8sjCS0SWMf3ifs2+Orz4nfs5/BPxvfO6an4h+FXgrWdSjSMBIdZuPDlvJqdokjzuUNtdo1rEzZ2s8C4JVjX8TPxE0VtC8e2vjLRDPp48QrFrVo8bpd3Ees2Rt2l1OzeQbb/U4rOF/EKRyCWD+0bm0dXht3vp4v6mv+CUHxh1b4xfsMfDbxDr0Oj6d4w0DVPir4Q8TaXoiGPSdK1HQfin41tNMWwVkuJotP1Lw82ia9p6hubPVbd0YRssg+SzibrQo4+FP923OnUfNeTq1Yxkk0r2t7Kd3qrtXbtd/Z5bD2dDEYTapCq6iSvZ0YurBtXV3ZzpNLb33q+WUpfX3xB1pNMh1e8hIhxF5EbQmRPPktpLssykTKJIWkciK3kZoB5x3oQwr4u+GepfF/4d6b8fvjJ4I8HT+K/GnxW0HRNE+HtzJoWr6pFoN34RuPGEfh2PV7XSNPvNbsNH1288QJeane2phkEGnR3fmicaekf0n40hi1bVtH0/VfNttK1a8WS7ubvLPa6ZHp09/eR3jySAO8kNtOkMZZQLt7aIorl1fute+OXwf+F3hiI6zrNrpEf2eMaT4b0yKK88S30UcU0FqLPTzHbw2trmEOk99cxxLE8jJI+GYfM2hXdRTqSpShKnyOm/e5uWTTs9WmpS0t2d227+7QnWoxVOhh3i/bezhOHLzK0ZRmr78vvKL5t466NSbPyg8f/GP9sXTfgaIx4TsvE/7WOn6DcxXPh3RNM+Iml+Cx4kg8SX0Fr4qJEGq6+mgJotpDqKaPp1/f6l/wkOzw5bX9vZMb6voM+PtR8U/H1/ik/hvVtB0b4i/DrwP4V8SaprGi6v4aj8T/ABR8N6t8TGfxJe6Fq+n6fqH2pfBVz4C8N3etXtmGvL6zjtYZ2Frfxx9jrP7cfwo0mKPWdQ8D/FOK2utQeYDS28Ly6e8FrMlrcXmq6lNqUcMjCW7YW+nSwXMMl5mxiZI4IL5u4l+Mvwp+NfhmabSNZs9dsb7U4rdLlreO08VeGNakgmnsDq1pcGK+0qWcWtxHHPAbnS76IXU0F0/kGca0qFKphJxjWUK6jGcFUi4e0lGdR07S5ktJSbtvyycrNO60nLMcFUVStl/LCcXSnWbcpQhVaUkrR0UnGN29r2btI9y8E+Mpn0210C9YoLS2NogkYm2yp2qnlGXYTE4b7FKsYaMLJHFKpZxL+Y3/AAWAlvpPCX7PF1eaTKthZ+ONE0T4e+Kp5LZrZfiX4n1fxJH4h0KRTIbgLdfD3wpq+o+ZO0fm3FvpbWi3JivLi3+q/Bus3sGrQKdkkpuYrJm3zJm4t7maxJAR3YpG2/dIqNhgfM3bGz+Sf/BWL9ovxprvx9tv2YNHjhbwX8Dbv4bfG/XJp4PM1y5+IWk/AzxX8Q9Lt4rq3WAWemReB/iBrEU26WWVpLqwufMt4oLtpe/KqlbG0HSqRc5Lmi5Rd+VJyVnd25W03datN2u9X4mJlSpV3OEopyU5OUm7yetkleVnfRXb1dtXc/V3/g2k8QDxX8Hv2pPEsEF1baX41/aIk+L2gW1y4Dw6P8QdAlkjgmhSMQpdR6d4ZtxNLbtIgS4t1kcz28sZ/qMjgPlIABjy1OA3J5cFs4PDFc+nIXJK7j/Oh/wbYfDb/hGv2KfFetSwRwPffEqbw1bRrI0jyaZofw/8GxxAOpMZW0vb/U7GGUSXEZb7QbdkVnSv6SI4QFOAw+UJgDgAO5GBgkdAev8AEeCTmvvMopOeFqpuSjOtXlTbi17n1quo2TeqaSad7OPK18Vz4TF+9WlH+WjhIXt1jQV+u97prdaXbdzAMAy3A6/89B/ek9v8+porZMUeWyi5zySOSd0nX36n8TRXpKrKK5eSL5bK93rZ1Ff4fJP7t7a+P713to7bvvJdv7v49bFKHTLeM2zEsRZoy28MReCyhDgRbfskJjim8qONI7Y3ETraxEJaGJfMB838U/BL4ReLdL+I1hrXgDwxJJ8VPDms+E/HupwaLZWuueJ9D1nT7nS9QsNV1q2gj1K6ieBovISW6YQzQ2VxbBZ7W2lX5d8cftvaboglt9H8JXOiKA8kerfGDXLH4SWt5DFE8gutL8H6vZ+IfjNrETN5aJHZ/CBVlLpsuW81MfjX/wAFIP8Agqb8Zvhx8DtSi+F3xAOk+J/GMR0y01nwL8PNX8Ox+H9B1Jpo77W9E8W/EltZ1rVNVsmDWthqCeE/Adzppaa9torqS3tBb+Bh+I8JmmOllmW0pY2pOn+9qKLhh1Q55KUqlSV7R7Rs3zO3vc2nu1spxmGoRx+KccLCMqSjeXNV55T91QsknNK0rcy5Y6t2cW/50f2mfglrXwc8WfEr4U6zPNrOq/AD4meI/A6StpBspNY03wBPey61rCafbRuLbR/G/wANprDW9MtVe4t7fTodMk+2PcRJcp99/wDBFf4yaf4du/2iv2e7+a3hGn6n4W+LfhNwEQ6xoHijSNM8GahLCnnb4k0vTvDHggw2MTTAwarNq0UhtxcT1+Lfw4/aE8W+M/inrKfGvxRrXxE13xSHtvEHjjxtq15qvifxzZWWq3d2kOveJPEN1c6jqWu6dDrOt2DC5luJ9S0i90LR3BtNGtoUvfBz4yah+yn8ePhV8Y7pp9V0DRU1T9nj4t3iSrZx3HhfQpYTpOtM7yRyQXFt4d1fwXqcGqef5d8/gfRUJaG11fS7vx8Xl9elQxuXT1q0+bGUoK8ub2cpv2cGopSXJKVrJc0uRq15J/U5fjKFWpSr3ajOEMPO7Ss3aMqmqtGKnCLd27Rcldyuz+wH413I8Q+Eb3w74aDNrGtaz4f0iG7ilEUlnpmoTwwahLiQOB5ttJcQglGhkBdAxJiZuL079mL4H+E/CV3f6J4E0/T9a1Ky0/8A4TC/uo7vxFqHii7S3DS6h4nXxSdTi1qZpJ555rgQQpNE8saWxhaK0ri/CfjHwx490/S9e0XWLXVbe+xcW72sodLhIS8kWwNtkAZU81EdswuUEjRyjI+1NDuPDeo6Vao91m0gstKjNvPJ58sUEdqqvbz3EgEjPPDHbwqZ185XLkypJLLn4XDV6scZUjFwp1PaQi1KOnLFSTT5m7O7V9brZ2Tkz6aM50INUakoP2kailF+87x5VZ2+BrXRbte87pH5H+J/g/8As9z65d29j8A/hTcXkAtLdtTH7P3wsUrNZxuFvCIPh22ly3lz9lsppZ57Py55yZJ4mjZi/at+yz8H9Ys5fHXhrQj8NPiBpsWmpo/iPwJPe+CdG8qwnsfs1v4j+GPw5u/DHw88RaZdrZ2+k6naa74YN81ldS3un31tq8MF4PvPxLZ+AI3meDR9Nu1ZZfItYr21S7kgea3d44o5ry3iExkTyxJIIyPPUTspcFsKL/hF9Egv7y2kiFhdRmeSK6i0wSQad9hf9wHNu0caRmMyqxX94FbzSzwwsfRji68KVSnWrKpTacFCMad02787k4NrVNOzX/gSbeeLxtWUYSk6vPGUeRyb5dGndp6NO3pq7u6scZ8PW0waf4VWUsNSjFnbah9pVRI15cam1zcTsrM21kllZbjnPmJNthZ331/Ph+0pqmm/GD9rb9qb4jyyxeTrHjD4n+DtJkgmmki/4Rj4feHJf2fNH1uSRbsw3Gnalonh/SHhtY/Lbz3iuoUklizH+l37Qf7Rekfs0eAfin8XJtR0+4/4Rq3uW8HaLq188FtrPjzUjaWHgPw9PcvKfK07VfFNxpMV7eKkgstNk1PVyhtbO+NfiV+z7qt14m8OzeAJp9Wu9S+Ffhqf4U+KJpJUKt41k8M/DC88RXTlpxcX+q3vje78V6jeG5i+z3rJHPBPZz2w0y39bKIVaOCniZR5adObjKUU0ppXV3eUrWbjzNWvpZO7PmsfUhVxMKEaic6kZNvRWTcm21ZK7TVvmndts/ui/wCCEHh5dG/4Jafs13E+mz2Osayfi9qmtG7hWG8ubtvj18Tre0luAuFlMOmQ2VrbXPzO9nDAPOeNYyf2EQFQQfWvzl/4JO+J/hjrv/BPP9lO1+GXifRvE1n4a+D3gbw340Oi36XjeHvipa+GtE1D4leF9bgkMeoaVruk+KtT1H+0dN1e3t9UQTW97PE8N5b3c36NKwYEjtwc9c/ma/Rsrv8A2fhVLRrD0XbzdNSn12UpteStdt3PjXJSqVmp896ktb32k10XkvnJ720pt95/94/+hSUUN95/94/+hSUUPeXr/wC3VDge8vX/ANuqH8Ydp4C8U/GXwbaeMtG+MOp/C74d6jZ3Wva5qng3QrefxVq3hbS5Z11yDTYo7bS7+x1+6sbXUrfTZVufEN5HqhtSvhy8vimnSfl/+2F4ol8d6W1o1hb6Zd6HaWOqLo9jpNt4e8jSWUWVjZy6Z4r+Lfj74ra3HYQLHpMOu+MfDfhSR4LOM6hDc3ayyS/YHxy+M938Av2I/gVpz6ZqlxY+NPFHjjwDf61YeNNa8Fy2F/ofi/xn4m0PSW1jw9pFzrv2zxHZ6XrxhutJ1rwxHBNoSWWqa/YrfIy/kZ8SPjufF+ljRb2701tMgWVNOs73xd8aPFupWyzs8cUl/qvx0+KHxO8SebbqZ47UaDr0GgNFLLNaW91AVmPlcAZbhcJktfGU4r6xjZ04Tdm5KNOWsedtvl5/eUU0r6tXP0vxVqQp5vl2WYdThQweX4TEzi5+7PEY5e0lUcU+VOMIQiuru+118I+ILUW159u0aZnuLWZJl2ube4t5lnSS2vLPmX95GwBAZHhO4CRSH8szt4ybxrqOqaT4kaCG38Z6XDoeuwSJb2FmmvaZdXz+GtTgecw29hZzvqeoaHq2ZAkNjdQ3V5OINO05mS+uYZJ51hnjkQyNNwiq7IZmKOACF5AdFTYHVEAYAhq848Q6KmrW08ALwysFFvfRySJPHcCRFinVlKpHIrYkV2YEMuzBZCD9TjMuVVU614+1SbpSs1ZNxlaXLyuSukrNt2TSaer/AD2hjfZSnCcW4fCldtOzmk7J9db901o7a/pd+yL+1H8VfgHoMXw+8dXOoz3Hg67n0ppZzJL5lhYTywaXrEd1JdCLU9I1TT2tb+z1Gzkl8sSnTzqK266ZA/6XRf8ABSG7urNR4U1SxkubpYv7RsL+SCyuN0cE43Wk1xNDb3E255DL5sjIfJDNdItuZ2/mS+HfxJ8TaLqM/hLxFcXd7fadbsunTfbrKBzpM7yGG50+DU9I1GzeBre1uobjStYOpaXl1ht9AFxJZTv3N349s0tn1Vk8M65aKkzrp+tazqngjxNcPDDp1xMmjtaeFvF+mazP5OoOItN0aHTru5YJdJpcFkPPf8xzDhyNTHyTVWFSVWMm0kqc7xTck48rTd1ZJu9pSqNz1PtMDn/ssNqlOMILVyU3GPMk42lezu48uuzai+W5+9h/bq8dCBodTfzprcXBTUoRcW86BnfYTa/brhJLhQ8TST2t1N5oRfMjaNitJcft869fWk2lz3EEVlcCVWtopY7rVL8Qq8UMV01pfzWdpArSEFUu7krHG0Yuj5rofwY0T9oT4CNb3P8Abnhr4sLc2sUP2hPD58Ka5p6i5e6Fs8Vzqfj/AMM3v2W6jFsq3Q0poraV7WC5KXxa2r1e2/a6/Zu+H9jca/ofw/8AiV4u12wVJ9L0PW9L8P8AhPSZdQl86OD+2vEY8R+KtWs7OBY55nutG8K+IjPL9ls45La6nF1FjLhjFxaprD4io5tWfI1Fe9qm1LRWWjeur1dkKpxNQr0p+0xOHtHltdwjJfE9OWN3tbuvO3vb3/BQT40+Nfix4w+E3g3xBq02m6Rbard+PbvSoZbp5NOtrnSNS8P6Trj200sqSHTLFdfawedFjjWbUIbh5byWaNfcP2LNbhvtY+NWoXq295P8SvjBeeI2vLu3QHTdR8f/AB4+I3w98I6bBO19HLqbXF3qvgy8gtJUhso7XVtK8h3Dz3ll+SXij41az8ZfiV4h+Imv6TZWKanp9tpZ0u2uNSlsbHw/pcWqWptbSK6ub2a4nFxrwvb7VL94n1PXNT/tWy0uxhRtFg/RH9k29n8NfAXXPiFcy3Vrc6N4j8I+PNcaWKWIiP4K6/4Y+IPiKeyeO5ktWsrXTPhF4mik+zrKiRJq+pXVohh1e+j+rq5esFl+FwVSCjL3IVlzSfPKdSU03eXuuKhbSy1Ts23f5z61TxFevi4yb5IzdOSuuWMXFKyWkrPW7vpvq9fe/wBjv/goV8dv2fPjFpfx+/Z68aSeBZfF7m88eeCXt7rWvh58RbSfxbr3iI6J8QfCtpqqy63bx2+oppGia5YXKeJ/B2jvPD4L13RNX1TVpo/77/8Agnh/wVQ+Av7dHh+08LR3em/DL9oPStKS+8X/AAP1/wAQQS6tcwx/aorrxL8M9Tuo7NfiT4RElnNLqF5osH9seFBLYw+NdH0qLUvDOoan/l3fA97vTtD8LaVIZZG042LebA86GaDSbd70IkQlfynkNjGsCfaNxM5tJXkmaUv+hXw68Z+ItG1rQPEmn694h8J+KdF1bSPEPhrxf4a1S60rxR4W1nSLw3Wjah4e1aAJNY3emyoriVViYQG4V3NrczlfsoYOMoulGXK4RpU1OMUtbKMFyuy16uz6O71PjI4yvhq9SpD3qc5pVISt70uaorptNxWidotJ6XV1d/6oTSZZjjqxxz/tSe30/McnqSv5T/gL/wAF/fiB4U+GejeHfjz8BtX+Nnj3Smks7n4p/DvWfBvhDTvGWlwpCmm6v4g8J3Eb6foHjGZA6+JrTwwtv4SnvlGp+H9K0i0u28P2pXE8nzO8l7K+tr3jr70knvddXbzS13No5nh5K/PJXt/y7b+1Prbz9NVq9T+ej/gp1+0boPij4A/s9fDDwj8K/Hul2unfFn4meJLnxN4m120m1G/udK8OW1qmg2/gTwhPf6J4etbVfHst9/bup67qd5ew3KWlvfSJ9qs4vyF8NeEfix4w8BePfiv4V+DPxl1z4afDG+OmfEr4n6N8IviT4g+Ffw91LyIL02vxE+Iek+FL/wAKeApYrK+sb64g8V61p2NM1HStSMa2Opadcv8Advx88W6J47j8EfDqDVNRuPFqXF74vi8LaJ4d8QeMNYj0PV4f7LV7rSPCrahf2hTU9M1Jw76bdPNbWUc63kOmF3m+xP2Nf27/AI+f8E/vgb8WPgL8OviF8PvEXw1+NvibXvEfinwZ8Wv2e/GWnjQvEXiDwT4a8FeKLr4ceMz8RvBeo+IbnWfDvhrSYtb8O69pnjLStPvNMtrlbDTbC81qwvO+hllDLsI8Nl1DlhRnBKj7aXLK9Rc83KS0ai9V26yk0e7mWaYzNcR9bzLE1K9f2VOk6tSc5NQpSnGnFOcpNQgvdpr7MXCKTUbr8NG16zscLczXskhaGbyFs5ywAkmiN1PHDEJlaQec292RBb5YII4XmPR6DdProxa6brM1vIgZnh0nUpmQytMFPzQ2cLF5HVtvnKVVHKvvQpXuNxoXw38ONGvhRdYmEZgU6jrzW+parcOGcrLd6jpmhaTY+epjIhmTT7aZgFaW5MrvO8tleaHfW0yatptvqr7ZHN1d3muG8aWMEqqPZazaCQOwMe+5inEYMW5HjMgTuk406cY1IpyfPyuLUuRXdlCUtfdv7r7Wd9LnmN+7KSk3o2pJ6v8AiNSTu9dE079tW05HyZ8ZPhZqsmmDU7C2EmtaZLNP4evtPku4muTD50l3pCXCNBcWmoT20R8i3kYJDqUUb7xEJPM8h8L6xa+KdPeWW6vJZjE95Lc2n2WLUreG3ZbzU7e9srjT7+aexW3s7aePUtP/ALPvrN7VBa61Bh4pfrH4sfFLwF4F1HQPBzaJc3Uni1NYu9cubXWHtpPD8On2mmvoWpRaLd6bey6i0t1Nq0j3UmoxXLnT3traaNpNTin+JPFwtvCOra5JaXDOt5rNjqtrJa3sFuIrq6u9SmmuLe38tTc6fGt5c2dpd26tb7Y9O3ypC728nkYukqnJKlFucZw5ZSSu3ednN6txtyq2r20bTY8FiZUlWjKbjTbhdqTvKV23z6pNPXRu+2rbbfn+tWGofaLx7y2i3vNd2kl7bt5++5MlysiTSJdP5UjxxuRF5UCr5yX8awmeIDLTS5ILN97w26Sfa94wYixCrKssrSSTRGRFgvnCrGzPGqNhnIJ6+11bRvEM+by2sbeWZPNv7oLJaLdSPIJJp9Rt0lVHuoA7WKXkUdtNcpFCDFG5LnotV0HQbNrbbps6WirfR/abLU7cs7WqbvKtZHiv7W0mlQ3avDcq7xyK1yUZC8zd9KlNYbnk5RkorZtKfvOPM1fysm9deis3wucFGcZR5ud6NpaJN92+yt/id9jkNA1GXStP8YXqbdmn6K1pve4ZRG8t5cCaRIYsu8v2u2t/KMcUmI4Ld3Vmjlif75T4pQfD34Z/FP4PQQxmXw54f8V/B6J9QvDHbR+JpPDz/BDx/Jo9jd300c83iLVNa+IHxNvrL7Pc2EV5rUxsGma1sbdPnv4OeB9Cn8beFZNfvbKTwt4l+Ivws8K3NxMzaDFPF4y8eWEups1xqZ/sq1k0q1sdYuhBItzHeaTYanvXTxNHLB4F4U1LUtT8RaVYaldgz2R1LxM0H7qVLfWdZutHWdVka4umvL17dYL7Ug8iXFjqMl/YTLDcWMZPhYjCrHYv2TnKMaE6NSVm05WTaSs9nz636X0dz1qdSdLCJbtqcZO71TV15uyS30T0s7a/angiyk01Eup7iWWUq0e3cTHErWaFnnICzzEq8kQnkjKTSmS5CyYgr6e8Ka26G3s5JEaCOC0eKOe4TzAgnIlli813aMLJhGj5GH+QEkkfMtpqMMb5VljtWSK4A+zxF4S5mYRQFI2KIscQbLOrESmUFQknm+l+HPFekaHJ51/FOtsrPPdPbWM2oXwS3SSadYbe3lZmnaOIi28pJJ5pStvCA7MG+hoSTq9vcgnpvOGil631Teq7t6nzlaNR1JSjFyjaPVJc12m9W9brffa71u/u3QvEl3FpVtFNa/2oqKRBcm5EJEDu8qxFY7WXcEeRmBcrIA6xtGoQElfL2iftDfBvUtPS9tPFN3aLcEvJBrulXtlqCyjCljFb3F3C0TqqukiXMoyzoWBQxqV6Sqqy1b21u9fP/wAmXXout2cXsK2tlZdr26yff+9+fZ3f8RP2lNP0EQeBYdd0S6mjtoJLbQZtYj0rSr7XJ45r3Uv7Q0SHxJptx4uvLW8vJYrVdSgMFtauILO2utNbSp2+d5/iHrmome7nup0jmeZCyvexWMtpamR4WgsLuYxxCIsxeOB3iPzx2m0kyP6e/i26fTLnS7ie4e2a3lE1i887WsschumVfszFUIcnYYi0invEGIY/KXiDR9JtZZfsVlHp7PNPzp08umqqjCDZBZywRbpVCSbzG0SLGECrEY0rx3Uk4yjf3ZWut72bad/v/wA21dfXVoKEnG6l7sXe396r0u9uX/yb0PWm8Y3UsbmeUlI4YxD5jzOZYiqyqY2FyQI2jVYiio7r+8CoskZYOXxstgYpp/njilgkuYJZvK85UvXMsNu7CbY03leVbz3Amkil2TLAsy+XXyn4g8T+IvDxXElzqNlBCqrb3HkwyqGNwwlMws2LNE4W5kWRpJiFkiSUrm5qxD4wml0i3v7nyfLvLcXI2TvJsMsseIZYRbXB4ESMpM3lO0cR82GMSy1CqSS5E9Gnd91dK1m+y/Hbe/O0uVxuldNL/wAmW1/Pb8bu59IR2/wu8S6zI83h+/8AFisbiN9S8a3mjaGttPfy2VvDbW9v4S1qPWJ5Uljt5LVdM8Qac+nCbUtRjt5LWR5hmeO/2crTx1oqar8JtUujq2nWX9mf8IX4iubER3txZi8tLvS7HxXNHpo0u/sbq3NvbaT4vhvbm9S+ubi68eWrRww3Hi2ifEPwrobRzXN6/nv5ok/faesCvIJSGMdtqstwyFnh2eZbxybiPMit3Ywt7Bovx2+Hvh66utRsvHGn/Z9TENzeaNLZ+IVmj1e2jmhsbxBLosFvLcTwn7PI0c88Miq3nBEVJJO2Co14NuCjKLhH4m3KyaTtdbpJrro222jjlzUnJRaeqv7u6tLpe/brpZPd3fxHDDdaHquoaLrdjdaZeade3mj6vpeoWs9nqOkanaXYt9Rsb+0vIhe6ffWJt5Uube4USQvGV8nYUz7LausunW4ZEmidJZ4ntxLFLbwNbQ2SNbCd90Vykcc5WNYo5zcwRXAvJETZX0R491b9nT9oe0WR/GeheFfHdtbwLp3jrVLw28djbx3tjbxWXxBa1tryxfwreHULDSrfXb7UbbU/DN8ljfWL6hJLqXw6v/mubR/Evw51G20DxVZwWt1f2kWs6Xfade6bq/hjxTolzcXNtB4m8Ha7pU99Y+KfC11cpdWkV/pF/NBFfW9xpdw9prFlf6dFnTm5wdC2j09pq0lzSadrW15V9r7Sd9NcJKU6alyyjom9Hp7011Sdny3T0tzNbp34/TfEt74c1bTtZYzGXQPEegaranzleOefRtTg1S1tp7d4St9KJrOGLZMxgCNdRyW0ktxJMKHwzFpH4vuru8nm+zaTpd9OL6aCcy38L6npujSCYjzJDc3Fpqlzd2sIM80uqC2to3kfyI66LxjpscPhvSdUnW3RJry1vEvFDK2oWtybrSggkknuIxa2+oaFrQj+zqt21xLcxXDBYjEnrPwA0Hw/bafPc67oOj30khi8QXupXI1eWS2e8lit/D2nbrPWEhnSHbHfWamyk8vUdVuIpop7i4soEiWGhCV1Jc0rqcrLaKVrq73st323a11p1JyjOF3JJ3ik3u9FHfqtU++jTWpswXHi28D2Phry7e7WKE3urahYy3h0lZ0kmjsLbSrm5hee8k3hZJb0xWdgFjWEtcssaReG/A3xf8ZfEDwT8OvhvqF743+Jnj3x34G8C+BfClnJp2k6zrfxA8a+LdK8K+CdC0yd9aFtpd5rHiHUrKxtr29fTrKzMzXV/eWlrDcajX6H+E/hLLrlxqfwhn+BOleMfiFpvhq7jk0XT7HXNO1b4HRa1psukSa14v8AGHhnxD4MvfD3xsju/FWnW3hb4UanrWmeGPDnxDuPDegfGnwj8RPik97+zr4f8/034i+A/B2uaHpfhjwr488A+O9C1fw/qieLPA/i74sweO/CPiXwRrGi+ItJ8Q6fomua5r2naN478G63bwXWn6lq3heXxHpmvaLBqc9/EdK1KM50KsLYqMKtFVoOlFKo7qHOpNTkk76uHuxekmn710rx79GMZV8PX5eaN0or31zSuk+fTRXdrtJN8rbZ5L8eP2G/2p/gD4k8MaN8bv2PPHuna/458D6R8RtAvPhDBqnjjwp4i8Kapq/iLw/b+IZb34Jy+L/BOlapd634X161l02zl0ye906z0zxbFpt/pGvaV4z1Uq9+07+0/wDBb48/FOfxT+1TH+0z+0f8S9D0XTfCSePYfHnh/S4rHw3ZXOp6tpvhhtG8Q2HxA03TL+yvNc1PVtefwMPCPgvXfEesav4wg+HGheJdb8USXRURljLLmxWB5tL+5LdN/wB576PrqmrtuV9VOg9fZ43WzSUqdrNysleadrLS6vaSurp3zr+WS2ZjvJO4ANgPhec4D8gksPmV1OC6nILbvM9Wtxc3DsrbS8gQEgLlvmG5gqkNkM2d+/BOcN82SilD+HD/AAL/ANKPWxGlR/4Y/hKou/b/AIdvU8w1Oysry4u7SVZGWfyoAWOAjNFPhlCuNh3DLMMhwWzECxrz6ytjYpqWgzSNKumXHlxOGkZGtrm4NukPlySbV8mVDKuFwyyMjZ2IaKKmUnFpJ6NpPRP7bjpdNrTtrtu1c4Z/FD/F/wC3f1/mznZfD8UrfLcOjM5BGxNjKF2oG2LG2NjK+MlEcFArf645cngxZVZhchJhJbqjY3KGlRW+YeWvBZRuYLuUBNq5BNFFdEP4tP1pGUv4svWH/uQ9L8R+I/hD4H0L4A/E/wCAtt8RrfXZ/A3iDwV+0F4E+KlxoOs+CtY+JHhNrLTvH6eB9d8N3Ol6tq/wo+L3gjxVo17NoPiHRtE8W+AvEl14m8PaL4g1qwsvDnjQ7/xUk8O/B34q+IPhv4WbXvHfwQ1j/hFPiR4L0Hx5NbaT420XR/iN8LPCfjzRo7rXPDstza2Pj3w3ofiPS/CereMdGiTR/GFxoGn6vr/g6Tw6YfAEJRW2C/hS8pxS1fSVu/bvrte7Sa6q8U8PNW2SmraWkpWTutbW+z8PlfU43xeDZTXmjQ3E8kOqXOg3KOWeApZ6uula5YxzRi4nSV4LfWLGOWLHlR3EVxtkuQiTy/ob8KriX4EfCr4Z+MvD0X2r4wftFeBfE/j7wZ4s+2XGn6X8EvhLpnxW+JfwYuNV8JxWSR6hd/tAeM/GnwX8awaN41eW20r4C/Daz0DxF8N38RfHDx1pnjf4VlFclaUvrcI30lOakujSjNq/ezimvO+rbkziwiSw+MqpLnpuPs5buPNPlk0npdrrq/O+p6f8Df2pPHn7El5qur/Bbw54NvdH+IFt8MZtY8M/EdfG+u6bpHiD4Q+LtU8ZfDLxF4Mvvh145+Dnijw8vhu51PXtC1bwleeKdY8CeN/CPiPWvD3jLw1eQQaNPb/F/wAYPF/imax8f/F/4ieKtZ8Q/Ff49eItT+MPi/xBpK2+lf2l4w+Kmpah4nl1DVbu2e0ins49Z8S3Woan4Ys9FtfDj3l5q1zo1tp08mnvblFSsLho4upUjSgp1sJhHVkk17RxljeVyV7O3Ts23du9yM5zw7jOUpKFZ8t29L06d7O99eur6b2Phu3tJRFujuZoxKfOf960jPJMzyPJI5VS8hOd7tlpG/eMd7MKKKK61ZKyjC2i+CHS9t436vz11Gltv06v/p35/wBadj//2Q==";
        String zlext="PNG";
        dzzlkService.zlsc(gldh, djxh, nsrsbh, nsrmc, zllxdm, zldata, zlext);
        //dzzlkService.ycjqdcx(gldh);
        //String zldjxh = "332ad75c086d494f8a567b1a34224e9b";
        //dzzlkService.zlyc(gldh, zldjxh);
        //dzzlkService.sxzttz( gldh, djxh, nsrsbh, nsrmc);
    }

    /**
     * 初始化
     */
    public CommonResult init(String gldh, String djxh, String nsrsbh, String nsrmc){
        String ywbw = "<root>\n" +
                "<gldh>"+gldh+"</gldh>\n" +
                "<swsxdm>CQ250000645</swsxdm>\t\n" +
                "<nsrlx>0102</nsrlx>\n" +
                "<djxh>"+djxh+"</djxh>\n" +
                "<wgzbh></wgzbh>\n" +
                "<nsrsbh>"+nsrsbh+"</nsrsbh>\n" +
                "<nsrmc>"+nsrmc+"</nsrmc>\n" +
                "<ztdm>00</ztdm>\n" +//00初始化,03需要税务人参与,06办结
                "<ztmc></ztmc>\n" +
                "<spyj></spyj>\n" +
                "</root>";
        CommonResult result = Esbdo("CQ.QT.DZSWJ.SYDZZLK.INIT",ywbw);
        if(!result.isSuccess()){
            if(result.getMsg().contains("已存在")){
                result.setSuccess("Y");
            }
        }
        return result;
        //<service xmlns='http://www.chinatax.gov.cn/spec/' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><head><tran_id>CQ.QT.DZSWJ.SYDZZLK.INIT</tran_id><channel_id>CQSW.NFXT.CLFSFSH</channel_id><tran_seq>142b1b0a6254453db5dfbf5737f22c5b</tran_seq><tran_date>20250117</tran_date><tran_time>181915</tran_time><expand><name>password</name><value>null</value></expand></head><body><![CDATA[<?xml version="1.0" encoding="UTF-8"?><root><code>000</code><message></message><result><info>Y</info></result></root>
        //<service xmlns='http://www.chinatax.gov.cn/spec/' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><head><tran_id>CQ.QT.DZSWJ.SYDZZLK.INIT</tran_id><channel_id>CQSW.NFXT.CLFSFSH</channel_id><tran_seq>142b1b0a6254453db5dfbf5737f22c5b</tran_seq><tran_date>20250117</tran_date><tran_time>181915</tran_time><expand><name>password</name><value>null</value></expand></head><body><![CDATA[<?xml version="1.0" encoding="UTF-8"?><root><code>999</code><message>关联单号：857682a495be491a87769cf06c5c3819已存在</message></root>
    }

    /**
     * 已采集清单查询
     */
    public CommonResult ycjqdcx(String gldh){
        String ywbw = "<root>\n" +
                "<gldh>"+gldh+"</gldh>\n" +
                "</root>";
        return Esbdo("CQ.QT.DZSWJ.SYDZZLK.YCJQDCX",ywbw);
        //<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
        //   <S:Body>
        //      <ns2:requestServiceResponse xmlns:ns2="http://entrance.gate.bonde.servyou.com.cn/">
        //         <return><![CDATA[<service xmlns='http://www.chinatax.gov.cn/spec/' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><head><tran_id>CQ.QT.DZSWJ.SYDZZLK.YCJQDCX</tran_id><channel_id>CQSW.NFXT.CLFSFSH</channel_id><tran_seq>142b1b0a6254453db5dfbf5737f22c5b</tran_seq><tran_date>20250117</tran_date><tran_time>181915</tran_time><expand><name>password</name><value>null</value></expand></head><body><![CDATA[<?xml version="1.0" encoding="UTF-8"?><root><code>000</code><message></message><bbzl>fasle</bbzl><info><zlurlww></zlurlww><zlurlnw></zlurlnw><zldm>003386</zldm><zlmc>存量房销售信息表</zlmc><zlzt>未上传</zlzt></info><info><zlurlww></zlurlww><zlurlnw></zlurlnw><zldm>000055</zldm><zlmc>房屋转让合同</zlmc><zlzt>未上传</zlzt></info><info><zlurlww></zlurlww><zlurlnw></zlurlnw><zldm>000875</zldm><zlmc>房产证复印件</zlmc><zlzt>未上传</zlzt></info></root>]]]]>><![CDATA[</body></service>]]></return>
        //      </ns2:requestServiceResponse>
        //   </S:Body>
        //</S:Envelope>
        /*Map<String,Object> rtnMap = XmlUtils.xmlToMap(xml,"info");
                if("000".equals(rtnMap.get("code"))){
                    result.setSuccess(rtnMap);
                }else {

                }*/
    }

    /**
     *资料上传
     */
    public CommonResult zlsc(String gldh,String djxh,String nsrsbh,String nsrmc,String zllxdm,String zldata,String zlext){
        String ywbw = "<root>\n" +
                "<gldh>"+gldh+"</gldh>\n" +
                "<swsxdm>CQ250000645</swsxdm>\n" +
                "<nsrlx>0102</nsrlx>\n" +
                "<djxh>"+djxh+"</djxh>\n" +
                "<wgzbh></wgzbh>\n" +
                "<nsrsbh>"+nsrsbh+"</nsrsbh>\n" +
                "<nsrmc>"+nsrmc+"</nsrmc>\n" +
                "<zllxdm>"+zllxdm+"</zllxdm>\n" +
                "<zldata>"+zldata+"</zldata>\n" +
                "<zlext>"+zlext+"</zlext>" +
                "</root>";
        return Esbdo("CQ.QT.DZSWJ.SYDZZLK.ZLSC",ywbw);
/*        <root>
	<code>000</code>
	<message/>
	<info>
		<zldjxh>c864bf2d6d5e4ee6ab46118edb14a71e</zldjxh>
		<zlurlww>http://119.84.46.180:28002/api/dzzl/querySingleDzzlYl?token=86040dc8149531a1220ca229152358fc1008317213641bc05c796be0684bd4334a8f4fb29e09e8daef32fe3ca869d816</zlurl>
		<zlurlnw>http://98.11.153.86:8001/api/dzzl/querySingleDzzlYl?token=c864bf2d6d5e4ee6ab46118edb14a71e</zlurlnw>
	</info>
</root>*/
    }

    /**
     * 资料移除
     */
    public CommonResult zlyc(String gldh,String zldjxh){
        String ywbw = "<root>\n" +
                "<gldh>"+gldh+"</gldh>\n" +
                "<zldjxh>"+zldjxh+"</zldjxh>" +
                "</root>";
        return Esbdo("CQ.QT.DZSWJ.SYDZZLK.ZLYC",ywbw);
        //<root><code>000</code><message></message><result><info>Y</info></result></root>
    }

    /**
     * 事项状态通知
     */
    public CommonResult sxzttz(String gldh,String djxh,String nsrsbh,String nsrmc){
        String ywbw = "<root>\n" +
                "<gldh>"+gldh+"</gldh>\n" +
                "<swsxdm>CQ250000645</swsxdm>\t\n" +
                "<nsrlx>0102</nsrlx>\n" +
                "<djxh>"+djxh+"</djxh>\n" +
                "<wgzbh></wgzbh>\n" +
                "<nsrsbh>"+nsrsbh+"</nsrsbh>\n" +
                "<nsrmc>"+nsrmc+"</nsrmc>\n" +
                "<ztdm>03</ztdm>\n" +//00初始化,03需要税务人参与,06办结
                "<ztmc></ztmc>\n" +
                "<spyj></spyj>" +
                "</root>";
        return Esbdo("CQ.QT.DZSWJ.SYDZZLK.SXZTTZ",ywbw);
        //<root><code>000</code><message></message><result><info>Y</info></result></root>
    }

    private CommonResult Esbdo(String fwid ,String ywbw){
        CommonResult result = new CommonResult();
        try{
            log.info(fwid+"请求："+ywbw);
            result = WsUtil.httpRequest(syzlkUrl,syzlkMethod,fwid,"CQSW.NFXT.CLFSFSH",ywbw);
            log.info(fwid+"请求返回："+result);
            if(result.isSuccess()) {
                String rtn = (String) result.getData();
                if (rtn != null && rtn.contains("<root>")) {
                    String xml = rtn.substring(rtn.indexOf("<root>"), rtn.indexOf("</root>") + 7);
                    Map<String, Object> rtnMap = XmlUtils.xmlToMap(xml);
                    if ("000".equals(rtnMap.get("code"))) {
                        result.setSuccess(rtnMap.get("info"));
                    } else {
                        result.setFail((String) rtnMap.get("message"));
                    }
                } else {
                    result.setFail("请求返回为空");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setFail("请求异常"+e.getMessage());
        }
        return  result;
    }
}
