package cn.com.servyou.sjlq.mapper.sjjsbz;

import cn.com.servyou.sjlq.model.FlzlDataVo;
import cn.com.servyou.sjlq.model.FlzlParamVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface Clfcl4HxMapper {

    //查询(基础信息，不含文件内容)
    List<FlzlDataVo> queryFlzlxxList(FlzlParamVo flzlParamVo);
    
    //查询总记录数
    int countFlzlxxList(FlzlParamVo flzlParamVo);

    //新增
    int addFlzl(FlzlDataVo flzlDataVo);

    //作废
    int zfFlzl(@Param("fjbh") String fjbh, @Param("ywlx") String ywlx);

    //下载前的查询
    FlzlDataVo queryFlzlxxByFjbh(@Param("fjbh") String fjbh, @Param("ywlx") String ywlx);

    //合并前的查询
    List<FlzlDataVo> queryFlzlxxByFjbhs(@Param("fjbh") String fjbh, @Param("ywlx") String ywlx);

    //合并后更新
    int zfFlzlByFjbhs(@Param("fjbh") String fjbh, @Param("ywlx") String ywlx);

    //附件数据更新
    int updateFlzl(FlzlDataVo flzlDataVo);
}
