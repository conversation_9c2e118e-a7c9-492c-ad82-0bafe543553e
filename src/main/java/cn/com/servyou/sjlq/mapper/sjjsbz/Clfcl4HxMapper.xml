<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.sjlq.mapper.sjjsbz.Clfcl4HxMapper">
    <select id="queryFlzlxxList" resultType="cn.com.servyou.sjlq.model.FlzlDataVo" parameterType="cn.com.servyou.sjlq.model.FlzlParamVo">
        <if test="pageNo != null and pageSize != null">
            SELECT * FROM (
                SELECT ROWNUM AS rowno, t.* FROM (
        </if>
        select fjbh, ywbh, pzbh, wjzllx, wjgs, wjm, wjlj, wjurl, wjlx, wjdx,
        lrrdm, to_char(lrrq, 'yyyy-MM-dd HH24:mi:ss') lrrq,
        xgrdm, to_char(xgrq, 'yyyy-MM-dd HH24:mi:ss') xgrq, yxbz,
        nsrsfqz, sfxyswqr, swsfyqr, swsfqz, gdzt, gldh, zldjxh, zlurlnw, sfts,ywlx
        from CLFXXFJXX_NEW
        where yxbz = 'Y'
        <if test="ywbh != null and ywbh != ''">
            and ywbh = #{ywbh}
        </if>
        <if test="ywbhList != null and ywbhList.size() > 0">
            and ywbh in
            <foreach collection="ywbhList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fjbh != null and fjbh != ''">
            and fjbh = #{fjbh}
        </if>
        <if test="ywlx != null and ywlx != ''">
            and ywlx = #{ywlx}
        </if>
        <if test="gdzt != null and gdzt != ''">
            and gdzt = #{gdzt}
        </if>
        order by lrrq desc
        <if test="pageNo != null and pageSize != null">
                ) t WHERE ROWNUM &lt;= #{pageNo} * #{pageSize}
            ) WHERE rowno &gt; (#{pageNo} - 1) * #{pageSize}
        </if>
    </select>
    
    <!-- 添加查询总记录数的方法 -->
    <select id="countFlzlxxList" resultType="int" parameterType="cn.com.servyou.sjlq.model.FlzlParamVo">
        select count(1)
        from CLFXXFJXX_NEW
        where yxbz = 'Y'
        <if test="ywbh != null and ywbh != ''">
            and ywbh = #{ywbh}
        </if>
        <if test="ywbhList != null and ywbhList.size() > 0">
            and ywbh in
            <foreach collection="ywbhList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fjbh != null and fjbh != ''">
            and fjbh = #{fjbh}
        </if>
        <if test="ywlx != null and ywlx != ''">
            and ywlx = #{ywlx}
        </if>
        <if test="gdzt != null and gdzt != ''">
            and gdzt = #{gdzt}
        </if>
    </select>
    <insert id="addFlzl" parameterType="cn.com.servyou.sjlq.model.FlzlDataVo">
        insert into CLFXXFJXX_NEW(fjbh, ywbh, pzbh, wjzllx, wjgs, wjm,
        wjlj, wjurl, wjlx, wjdx, lrrdm, lrrq, xgrdm, xgrq,
        yxbz, nsrsfqz, sfxyswqr, swsfyqr,
        swsfqz, gdzt, gldh, zldjxh, zlurlnw, sfts, wjnr, ywlx)
        values
        (#{fjbh,jdbcType=VARCHAR}, #{ywbh,jdbcType=VARCHAR}, #{pzbh,jdbcType=VARCHAR}, #{wjzllx,jdbcType=VARCHAR}, #{wjgs,jdbcType=VARCHAR}, #{wjm,jdbcType=VARCHAR},
        #{wjlj,jdbcType=VARCHAR}, #{wjurl,jdbcType=VARCHAR}, #{wjlx,jdbcType=VARCHAR}, #{wjdx,jdbcType=VARCHAR}, #{lrrdm,jdbcType=VARCHAR}, sysdate, #{xgrdm,jdbcType=VARCHAR}, sysdate,
        'Y', #{nsrsfqz,jdbcType=VARCHAR}, #{sfxyswqr,jdbcType=VARCHAR}, #{swsfyqr,jdbcType=VARCHAR},
        #{swsfqz,jdbcType=VARCHAR}, #{gdzt,jdbcType=VARCHAR}, #{gldh,jdbcType=VARCHAR}, #{zldjxh,jdbcType=VARCHAR},
        #{zlurlnw,jdbcType=VARCHAR}, #{sfts,jdbcType=VARCHAR}, #{wjnr,jdbcType=VARCHAR}, #{ywlx,jdbcType=VARCHAR})
    </insert>
    <update id="zfFlzl" parameterType="java.lang.String">
        update CLFXXFJXX_NEW set yxbz = 'N' where fjbh in (${fjbh}) and ywlx = #{ywlx}
    </update>
    <select id="queryFlzlxxByFjbh" resultType="cn.com.servyou.sjlq.model.FlzlDataVo" parameterType="java.lang.String">
        select *
        from CLFXXFJXX_NEW
        where fjbh = #{fjbh}
        and yxbz = 'Y'
        and ywlx = #{ywlx}
    </select>
    <!--合并用-->
    <select id="queryFlzlxxByFjbhs" resultType="cn.com.servyou.sjlq.model.FlzlDataVo" parameterType="java.lang.String">
        select *
        from CLFXXFJXX_NEW
        where fjbh in (${fjbh})
        and yxbz = 'Y'
        and ywlx = #{ywlx}
    </select>
    <!--合并用-->
    <update id="zfFlzlByFjbhs" parameterType="java.lang.String">
        delete from CLFXXFJXX_NEW where fjbh in (${fjbh}) and ywlx = #{ywlx}
    </update>
    <!--附件数据更新-->
    <update id="updateFlzl" parameterType="java.lang.String">
        update CLFXXFJXX_NEW set xgrq = sysdate
        <if test="gdzt != null and gdzt != ''">
            ,gdzt = #{gdzt}
        </if>
        <if test="yxbz != null and yxbz != ''">
            ,yxbz = #{yxbz}
        </if>
        <if test="zlurlnw != null and zlurlnw != ''">
            ,zlurlnw = #{zlurlnw}
        </if>
        <if test="zldjxh != null and zldjxh != ''">
            ,zldjxh = #{zldjxh}
        </if>
        <if test="gldh != null and gldh != ''">
            ,gldh = #{gldh}
        </if>
        <if test="ywbh != null and ywbh != ''">
            ,ywbh = #{ywbh}
        </if>
        <if test="xgrdm != null and xgrdm != ''">
            ,xgrdm = #{xgrdm}
        </if>
        where ywlx = #{ywlx}
        <if test="fjbh != null and fjbh != ''">
            and fjbh = #{fjbh}
        </if>
        <if test="fjbhList != null and fjbhList.size() > 0">
            and fjbh in
            <foreach collection="fjbhList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>