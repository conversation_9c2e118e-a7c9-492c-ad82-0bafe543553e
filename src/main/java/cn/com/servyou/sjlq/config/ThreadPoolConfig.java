package cn.com.servyou.sjlq.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
public class ThreadPoolConfig {

    private Executor getInstance(String threadNamePrefix,Integer corePoolSize,Integer maxPoolSize,Integer queueCapatity){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置核心线程数
        executor.setCorePoolSize(corePoolSize);
        //设置最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //线程池所用的缓冲队列的大小
        executor.setQueueCapacity(queueCapatity);
        //等待任务在关机时完成--表明等待所有线程执行完
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //等待时间，默认为0，此时立即停止；设置则等待XX秒停止
        executor.setAwaitTerminationSeconds(60);
        //线程名称前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        //初始化线程
        executor.initialize();
        return executor;
    }
    @Bean("sfzzThreadPool")
    public Executor getCommonAsyncExecutor(){
        return getInstance("sfzz-",8,8,300);
    }
}
