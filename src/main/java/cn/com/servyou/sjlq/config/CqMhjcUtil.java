package cn.com.servyou.sjlq.config;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class CqMhjcUtil {

    public void handleSession(Map<String, Object> map){
        HttpServletRequest request = (HttpServletRequest) map.get("request");
        request.getSession().setAttribute("swrydm", (String) map.get("swrydm"));
        request.getSession().setAttribute("swrymc", (String) map.get("swrymc"));
        request.getSession().setAttribute("swjgdm", (String) map.get("swjgdm"));
        request.getSession().setAttribute("swjgmc", (String) map.get("swjgmc"));
        request.getSession().setAttribute("sjswjgdm", (String) map.get("sjswjgdm"));
        request.getSession().setAttribute("sjswjgmc", (String) map.get("sjswjgmc"));
        request.getSession().setAttribute("swrysfdm", (String) map.get("swrysfdm"));
        request.getSession().setAttribute("swrysfmc", (String) map.get("swrysfmc"));
        request.getSession().setAttribute("zsfbz", (String) map.get("zsfbz"));
    }
}
