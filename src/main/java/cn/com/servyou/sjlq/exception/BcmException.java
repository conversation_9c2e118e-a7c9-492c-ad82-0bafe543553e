package cn.com.servyou.sjlq.exception;

/**
 * 通用业务异常
 */
public class BcmException extends RuntimeException {
    private int code;
    private String msg;

    public BcmException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;

    }

    public BcmException(String msg) {
        this(999, msg);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
