package cn.com.servyou.sjlq.exception;

import cn.com.servyou.sjlq.model.ResultVo;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger("异常");

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public String excetpionHandler(Exception e){
        if (e instanceof BcmException){
            log.error("业务异常：" + ((BcmException) e).getMsg());
            return JSONObject.toJSONString(ResultVo.valueOfError(((BcmException) e).getMsg(), ((BcmException) e).getCode()));
        }
        log.error("未知异常：", e);
        return JSONObject.toJSONString(ResultVo.valueOfError("服务出现未知异常，请联系管理员！", 500));
    }
}
