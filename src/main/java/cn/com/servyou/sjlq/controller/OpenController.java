package cn.com.servyou.sjlq.controller;

import cn.com.servyou.sjlq.model.CommonResult;
import cn.com.servyou.sjlq.services.*;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

@RestController
@RequestMapping("/open")
public class OpenController {

    private static final Logger log = LoggerFactory.getLogger(OpenController.class);

    @Autowired
    private ClfQyService clfQyService;

    @Autowired
    private ClfService clfService;

	@Autowired
    private FlzlService flzlService;

	@Autowired
    private JkptService jkptService;

    @Autowired
    private FjgldaService fjgldaService;

	@Value("${env}")
    private String env;

    /**
     * 航信业务
     */
    @ResponseBody
    @RequestMapping(value = "/hxyw", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Object hxyw(HttpServletRequest request){
        log.info("来活了，数据通用接收接口");
        CommonResult rtn = new CommonResult();
        Long s = System.currentTimeMillis();
        String body = null;
        try {
            BufferedReader reader = request.getReader();
            body = IOUtils.toString(reader);
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("数据通用接收接口入参:{}",body);
        if(StringUtils.isEmpty(body)){
            log.info("请求报文为空");
            rtn.setFail("请求报文为空");
            return rtn;
        }
        try {
            //解析最外层报文
            JSONObject resive = JSONObject.parseObject(body);
            //获取业务类型
            String ywlx = resive.getString("ywlx");
            //获取业务数据
            String ywsj = resive.getString("ywsj");
            //解析业务数据
            JSONObject ywsjObject = JSONObject.parseObject(ywsj);
            if ("pro-pro".equalsIgnoreCase(env)) {
                //测试用
                if ("clfqy".equals(ywlx)) {
                    //成功
                    CommonResult commonResult = new CommonResult();
                    commonResult.setResult("00", "新增业务成功", null, 1);
                    return commonResult.zrfc();
                } else {
                    //失败
                    CommonResult commonResult = new CommonResult();
                    commonResult.setResult("99", "新增业务失败：代码已存在", null, 0);
                    return commonResult.zrfc();
                }
            }
            if ("sqlxh".equalsIgnoreCase(ywlx)){
                rtn = jkptService.queryJkptDataEx(ywsjObject);
            } else if ("clfqy".equals(ywlx)) {
                //存量房企业接收外部数据
                rtn = clfQyService.jswbsj(ywsjObject);
            } else if ("clfgr".equals(ywlx)) {
                //存量房个人接收外部数据
                rtn = clfService.jswbsj(ywsjObject);
            } else if ("clfqyspjg".equals(ywlx)) {
                //获取存量房企业审批结果
                rtn = clfQyService.hqspjg(ywsjObject);
            } else if ("clfgrspjg".equals(ywlx)) {
                //获取存量房个人审批结果
                rtn = clfService.hqspjg(ywsjObject);
            } else if ("flzlxz".equals(ywlx)) {
                //附件新增，不分片小附件 或 已分片的附件
                rtn = flzlService.addFlzl(ywsjObject);
            } else if ("flzlhb".equals(ywlx)) {
                //附件分片完成，通知组装，入库明细数据
                rtn = flzlService.buildFlzl(ywsjObject);
            } else if ("flzlsc".equals(ywlx)) {
                //附件删除
                rtn = flzlService.zfFlzl(ywsjObject);
            } else if ("flzllb".equalsIgnoreCase(ywlx)) {
                //附件列表
                rtn = flzlService.getFlzlList(ywsjObject);
            } else if ("flzlsl".equals(ywlx)) {
                //附件列表数量
                rtn = flzlService.queryFlzlCountByYwbh(ywsjObject);
            } else if ("flzlgx".equals(ywlx)) {
                //附件更新
                rtn = flzlService.updateFlzl(ywsjObject);
            } else if("fjglda".equals(ywlx)){
                //附件管理档案主表数据入库
                rtn = fjgldaService.jswbsj(ywsjObject);
            } else {
                rtn = new CommonResult();
                rtn.setResult("99", "业务不存在" + ywlx, null, 0);
            }
        } catch (Exception e){
            rtn = new CommonResult();
            rtn.setResult("99", "格式化请求报文出现异常", null, 0);
        }
        rtn.setTime(System.currentTimeMillis()-s);
        log.info("数据通用接收接口返回："+rtn.toString());
        return rtn.zrfc();
    }

    //预览
    @RequestMapping("/viewFlzl")
    public void getFjxx(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fjbh = request.getParameter("fjbh");
        String ywlx = request.getParameter("ywlx");
        if (StringUtils.isEmpty(fjbh)){
            throw new Exception("附件编号不能为空");
        }
        String message = flzlService.downloadFlzl(fjbh, ywlx, request, response);
        if (!StringUtils.isEmpty(message)){
            throw new Exception(message);
        }
    }
}