package cn.com.servyou.sjlq.utils;

import cn.com.servyou.sjlq.model.CommonResult;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class WsUtil {
    private static RestTemplate restTemplate = new RestTemplate();

    public static CommonResult httpRequest(String wsdlUrl, String wsdMethod, String fwid, String channelId, String ywbw){
        CommonResult rtn = new CommonResult();
        try {
            String sopXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ent=\"http://entrance.gate.bonde.servyou.com.cn/\">\n" +
                            "   <soapenv:Header/>\n" +
                            "   <soapenv:Body>\n" +
                            "      <ent:"+wsdMethod+">\n" +
                            "         <arg0><![CDATA[\n" +
                            "           <service xmlns=\"http://www.chinatax.gov.cn/spec/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                            "           <head>\n" +
                            "               <tran_id>"+fwid+"</tran_id>\n" +
                            "               <channel_id>"+channelId+"</channel_id>\n" +
                            "               <tran_seq>"+ UUID.randomUUID().toString().replace("-", "")+"</tran_seq>\n" +
                            "               <tran_date>"+new SimpleDateFormat("yyyyMMdd").format(new Date())+"</tran_date>\n" +
                            "               <tran_time>"+new SimpleDateFormat("HHmmss").format(new Date())+"</tran_time>\n" +
                            "               <expand></expand>\n" +
                            "           </head>\n" +
                            "           <body><![CDATA["+ywbw+"]]]]>><![CDATA[</body></service>]]>\n" +
                            "       </arg0>\n" +
                            "      </ent:"+wsdMethod+">\n" +
                            "   </soapenv:Body>\n" +
                            "</soapenv:Envelope>";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(new MediaType(MediaType.TEXT_XML, StandardCharsets.UTF_8));
            HttpEntity<String> requrstEntity = new HttpEntity<>(sopXml,headers);
            ResponseEntity<String> response = restTemplate.postForEntity(wsdlUrl.replace("?wsdl",""),requrstEntity,String.class);
            if(response.getStatusCodeValue()==200){
                String body = response.getBody();
                if(!StringUtils.isEmpty(body)){
                    body = body.replaceAll("&lt;","<").replaceAll("&gt;",">");
                    String result = body.substring(body.indexOf("<return>")+8,body.indexOf("</return>"));
                    rtn.setSuccess(result);
                }else{
                    rtn.setFail("请求返回为空");
                }
            }else {
                rtn.setFail("http"+response.getStatusCodeValue());
            }
        }catch (Exception e){
            e.printStackTrace();
            rtn.setFail("请求异常"+e.getMessage());
        }
        return rtn;
    }
}
