package cn.com.servyou.sjlq.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class CommonUtils {
    public static String getUniqueID(){
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }

    public static String getDateTime(){
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public static String getDateTimeSimple(){
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }

    public static String formatDate(Date date, String formatString){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatString);
        return simpleDateFormat.format(date);
    }
}
