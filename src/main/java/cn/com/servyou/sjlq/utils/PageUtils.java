package cn.com.servyou.sjlq.utils;

import cn.com.servyou.sjlq.model.FlzlParamVo;
import cn.com.servyou.sjlq.model.PageResult;

import java.util.List;

/**
 * 分页工具类
 */
public class PageUtils {

    /**
     * 创建分页结果
     * @param list 数据列表
     * @param total 总记录数
     * @param paramVo 分页参数
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> createPageResult(List<T> list, int total, FlzlParamVo paramVo) {
        Integer pageNo = paramVo.getPageNo();
        Integer pageSize = paramVo.getPageSize();
        
        // 如果未设置分页参数，则返回所有数据
        if (pageNo == null || pageSize == null) {
            return new PageResult<>(1, list.size(), total, list);
        }
        
        return new PageResult<>(pageNo, pageSize, total, list);
    }
}