package cn.com.servyou.sjlq.utils;

import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileUtil {

    private static final int BUFFER_SIZE = 2 * 1024;

    public static void main(String[] args) throws Exception{
        String base64 = FileUtil.getBase64ByFile("D:/pdfs/eloamPlugin接口文档.pdf");
        File file = new File("D:/pdfs/eloamPlugin接口文档_base64.txt");
        FileWriter fileWriter = new FileWriter(file);
        BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
        bufferedWriter.write(base64);
        bufferedWriter.close();
        fileWriter.close();
        System.out.println("done");
        //byte[] buff = FileUtil.getBytesByBase64(base64);
        //boolean succ = FileUtil.setFileByBytes(buff, "D:/fxglbb1231_create.pdf");
        //System.out.println(succ);
    }

    /**
     * 将base64字符串转换成文件字节数组
     * */
    public static byte[] getBytesByBase64(String base64){
        return Base64.decodeBase64(base64);
    }
    
    /**
     * 将文件转换成Byte数组
     * @param pathStr
     * @return 成功返回字节数组，失败返回null
     */
    public static byte[] getBytesByFile(String pathStr) {
        File file = new File(pathStr);
        FileInputStream fis = null;
        ByteArrayOutputStream bos = null;
        try {
            fis = new FileInputStream(file);
            bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            byte[] data = bos.toByteArray();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != bos)
                    bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (null != fis)
                    fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 将Byte数组转换成文件
     * @param bytes,filePath
     * @return 成功返回true，失败返回false
     */
    public static boolean setFileByBytes(byte[] bytes, String filePath) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        boolean result = true;
        try {
            int index = filePath.lastIndexOf("/");
            String dirName = filePath.substring(0, index);
            // String fileName = filePath.substring(index + 1);
            File dir = new File(dirName);
            if (!dir.exists()) {// 判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 将文件转换成Base64字符串
     * @param pathStr
     * @return 成功返回Base64字符串，失败返回null
     */
    public static String getBase64ByFile(String pathStr) {
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;
        try {
            // 建立读取文件的文件输出流
            fin = new FileInputStream(pathStr);
            // 在文件输出流上安装节点流（更大效率读取）
            bin = new BufferedInputStream(fin);
            // 创建一个新的 byte 数组输出流，它具有指定大小的缓冲区容量
            baos = new ByteArrayOutputStream();
            // 创建一个新的缓冲输出流，以将数据写入指定的底层输出流
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            // 刷新此输出流并强制写出所有缓冲的输出字节，必须这行代码，否则有可能有问题
            bout.flush();
            byte[] bytes = baos.toByteArray();
            // sun公司的API
            // return encoder.encodeBuffer(bytes).trim();
            // apache公司的API
            return Base64.encodeBase64String(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != bout)
                    bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (null != bin)
                try {
                    bin.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            if (null != fin)
                try {
                    fin.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
        }
        return null;
    }

    /**
     * 将文件转换成Base64字符串(电子档案专用) 用的是sun的base64编码方法
     * @param pathStr
     * @return 成功返回Base64字符串，失败返回null
     */
    @SuppressWarnings("restriction")
    public static String getBase64ByFileForDzda(String pathStr) {
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;
        try {
            // 建立读取文件的文件输出流
            fin = new FileInputStream(pathStr);
            // 在文件输出流上安装节点流（更大效率读取）
            bin = new BufferedInputStream(fin);
            // 创建一个新的 byte 数组输出流，它具有指定大小的缓冲区容量
            baos = new ByteArrayOutputStream();
            // 创建一个新的缓冲输出流，以将数据写入指定的底层输出流
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            // 刷新此输出流并强制写出所有缓冲的输出字节，必须这行代码，否则有可能有问题
            bout.flush();
            byte[] bytes = baos.toByteArray();
            // sun公司的API
            // return encoder.encodeBuffer(bytes).trim();
            return new sun.misc.BASE64Encoder().encode(bytes);
            // apache公司的API
            // return Base64.encodeBase64String(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != bout)
                    bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (null != bin)
                try {
                    bin.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            if (null != fin)
                try {
                    fin.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
        }
        return null;
    }

    /** 
     * 将base64编码转换成文件 
     * @param base64sString、文件名
     * return 成功返回true，失败返回false
     */
    public static boolean setFileByBase64(String base64sString, String fileName) {
        // 移除空格
        base64sString.replaceAll("\r|\n", "");
        BufferedInputStream bin = null;
        FileOutputStream fout = null;
        BufferedOutputStream bout = null;
        boolean result = true;
        try {
            int index = fileName.lastIndexOf("/");
            String dirName = fileName.substring(0, index);
            // String fileName = filePath.substring(index + 1);
            File dir = new File(dirName);
            if (!dir.exists()) {// 判断文件目录是否存在
                dir.mkdirs();
            }
            // 将base64编码的字符串解码成字节数组
            byte[] bytes = Base64.decodeBase64(base64sString);
            // apache公司的API
            // byte[] bytes = Base64.decodeBase64(base64sString);
            // 创建一个将bytes作为其缓冲区的ByteArrayInputStream对象
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            // 创建从底层输入流中读取数据的缓冲输入流对象
            bin = new BufferedInputStream(bais);
            // 指定输出的文件
            File file = new File(fileName);
            // 创建到指定文件的输出流
            fout = new FileOutputStream(file);
            // 为文件输出流对接缓冲输出流对象
            bout = new BufferedOutputStream(fout);
            byte[] buffers = new byte[1024];
            int len = bin.read(buffers);
            while (len != -1) {
                bout.write(buffers, 0, len);
                len = bin.read(buffers);
            }
            // 刷新此输出流并强制写出所有缓冲的输出字节，必须这行代码，否则有可能有问题
            bout.flush();
        } catch (IOException e) {
            e.printStackTrace();
            result = false;
        } finally {
            try {
                if (null != bout)
                    bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (null != fout)
                try {
                    fout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            if (null != bin)
                try {
                    bin.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
        }
        return result;
    }

    /**
         * 压缩成ZIP 方法1
         * @param srcDir 压缩文件夹路径 
         * @param out    压缩文件输出流
         * @param KeepDirStructure  是否保留原来的目录结构,true:保留目录结构; 
         *                          false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
         * @throws RuntimeException 压缩失败会抛出运行时异常
         */
    public static boolean toZip(String srcDir, OutputStream out, boolean KeepDirStructure) throws RuntimeException {
        long start = System.currentTimeMillis();
        boolean result = true;
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            File sourceFile = new File(srcDir);
            compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
            long end = System.currentTimeMillis();
            System.out.println("压缩完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            result = false;
            throw new RuntimeException("压缩文件目录失败", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 压缩成ZIP 方法2
     * @param out    压缩文件输出流
     * @param KeepDirStructure  是否保留原来的目录结构,true:保留目录结构; 
     *                          false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static boolean toZipByFileList(List<String> srcDirs, OutputStream out, boolean KeepDirStructure)
            throws RuntimeException {
        long start = System.currentTimeMillis();
        boolean result = true;
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            for (int i = 0; i < srcDirs.size(); i++) {
                File sourceFile = new File(srcDirs.get(i));
                compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
            }
            long end = System.currentTimeMillis();
            System.out.println("压缩完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            result = false;
            throw new RuntimeException("压缩文件目录失败：" + e.getMessage());
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 递归压缩方法
     * @param sourceFile 源文件
     * @param zos        zip输出流
     * @param name       压缩后的名称
     * @param KeepDirStructure  是否保留原来的目录结构,true:保留目录结构; 
     *                          false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws Exception
     */
    private static void compress(File sourceFile, ZipOutputStream zos, String name, boolean KeepDirStructure)
            throws Exception {
        byte[] buf = new byte[BUFFER_SIZE];
        if (sourceFile.isFile()) {
            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
            System.out.println("电子资料打包：" + name);
            zos.putNextEntry(new ZipEntry(name));
            // copy文件到zip输出流中
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            // Complete the entry
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (KeepDirStructure) {
                    // 空文件夹的处理
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    // 没有文件，不需要文件的copy
                    zos.closeEntry();
                }
            } else {
                System.out.println("子目录文件-总文件数量：" + listFiles.length);
                for (int j = 0; j < listFiles.length; j++) {
                    File file = listFiles[j];
                    System.out.println("子目录文件-" + j + "：" + file.getName());
                    // 判断是否需要保留原来的文件结构
                    if (KeepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zos, name + "/" + file.getName(), KeepDirStructure);
                    } else {
                        compress(file, zos, file.getName(), KeepDirStructure);
                    }
                }
            }
        }
    }
}