-- Create table
create table SJJS_BZ.CLFXXFJXX_NEW
(
  fjbh     VARCHAR2(64) not null,
  ywbh     VARCHAR2(64) not null,
  pzbh     VARCHAR2(255),
  wjzllx   VARCHAR2(255),
  wjgs     VARCHAR2(255),
  wjm      VARCHAR2(255),
  wjlj     VARCHAR2(512),
  wjurl    VARCHAR2(1024),
  wjlx     VARCHAR2(128),
  wjdx     NUMBER(10) not null,
  lrrdm    VARCHAR2(64),
  lrrq     DATE default SYSDATE not null,
  xgrdm    VARCHAR2(64),
  xgrq     DATE default SYSDATE not null,
  yxbz     VARCHAR2(1) default 'Y' not null,
  nsrsfqz  VARCHAR2(10),
  sfxyswqr VARCHAR2(10),
  swsfyqr  VARCHAR2(10),
  swsfqz   VARCHAR2(10),
  gdzt     VARCHAR2(10),
  gldh     VARCHAR2(100),
  zldjxh   VARCHAR2(100),
  zlurlnw  VARCHAR2(100),
  sfts     VARCHAR2(10),
  wjnr     CLOB
)
tablespace TS_DAT_DSJPZK
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the table
comment on table SJJS_BZ.CLFXXFJXX_NEW
  is '附件信息';
-- Add comments to the columns
comment on column SJJS_BZ.CLFXXFJXX_NEW.fjbh
  is '唯一，附件编号';
comment on column SJJS_BZ.CLFXXFJXX_NEW.ywbh
  is '业务编号（一笔业务）';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjzllx
  is '文件类型（身份证正面）';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjgs
  is '文件归属';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjm
  is '文件名 xxx.pdf';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjurl
  is '预览（下载地址）';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjlx
  is '文件后缀名 pdf';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjdx
  is '文件大小';
comment on column SJJS_BZ.CLFXXFJXX_NEW.lrrdm
  is '录入人';
comment on column SJJS_BZ.CLFXXFJXX_NEW.lrrq
  is '录入日期';
comment on column SJJS_BZ.CLFXXFJXX_NEW.yxbz
  is '有效标识';
comment on column SJJS_BZ.CLFXXFJXX_NEW.gdzt
  is '归档状态（已归档 未归档）';
comment on column SJJS_BZ.CLFXXFJXX_NEW.gldh
  is '关联单号（资料库对应值）';
comment on column SJJS_BZ.CLFXXFJXX_NEW.zldjxh
  is '资料登记序号';
comment on column SJJS_BZ.CLFXXFJXX_NEW.zlurlnw
  is '内外预览地址';
comment on column SJJS_BZ.CLFXXFJXX_NEW.sfts
  is '是否推送';
comment on column SJJS_BZ.CLFXXFJXX_NEW.wjnr
  is '文件内容';
-- Create/Recreate indexes
create index SJJS_BZ.IDX_CFXXFJXXYXBZ on SJJS_BZ.CLFXXFJXX_NEW (YXBZ)
  tablespace TS_DAT_DSJPZK
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create index SJJS_BZ.IDX_CLFXXFJXXYWID on SJJS_BZ.CLFXXFJXX_NEW (YWBH)
  tablespace TS_DAT_DSJPZK
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Create/Recreate primary, unique and foreign key constraints
alter table SJJS_BZ.CLFXXFJXX_NEW
  add constraint PK_CLFXXFJXX primary key (FJBH)
  using index
  tablespace TS_DAT_DSJPZK
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

-----------20250804增加业务类型,调整主键约束--------------
ALTER TABLE DEV01.CLFXXFJXX_NEW ADD YWLX VARCHAR2(100) NULL;
COMMENT ON COLUMN DEV01.CLFXXFJXX_NEW.YWLX IS '业务类型 clfgr（存量房个人）、clfqy（存量房企业）、fjglda（附件管理档案）';

-- 删除原主键约束
ALTER TABLE SJJS_BZ.CLFXXFJXX_NEW DROP CONSTRAINT PK_CLFXXFJXX;

-- 增加联合主键约束
ALTER TABLE SJJS_BZ.CLFXXFJXX_NEW
  ADD CONSTRAINT PK_CLFXXFJXX PRIMARY KEY (FJBH, YWLX)
  USING INDEX
  TABLESPACE TS_DAT_DSJPZK
  PCTFREE 10
  INITRANS 2
  MAXTRANS 255
  STORAGE
  (
    INITIAL 64K
    NEXT 1M
    MINEXTENTS 1
    MAXEXTENTS UNLIMITED
  );
