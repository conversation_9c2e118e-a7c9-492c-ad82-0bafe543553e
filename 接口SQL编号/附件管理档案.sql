--20250321 开始--
--附件管理档案表更新 （修改） 30000059
UPDATE
	sjjs_bz.fjglda
SET
	xgrq = sysdate
	[,ywbh = @ywbh@]
	[,ywlx = @ywlx@]
	[,gdnd = @gdnd@]
	[,gdrq = @gdrq@]
	[,blry = @blry@]
	[,fjsl = @fjsl@]
	[,gdbz = @gdbz@]
	[,gdzt = @gdzt@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--附件管理档案明细表更新 （修改） 30000060
UPDATE
	sjjs_bz.fjgldamx
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,pzbh = @pzbh@]
	[,wjzllx = @wjzllx@]
	[,wjgs = @wjgs@]
	[,wjm = @wjm@]
	[,wjlj = @wjlj@]
	[,wjurl = @wjurl@]
	[,wjlx = @wjlx@]
	[,wjdx = @wjdx@]
	[,wjnr = @wjnr@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
	[,nsrsfqz = @nsrsfqz@]
	[,sfxyswqr = @sfxyswqr@]
	[,swsfyqr = @swsfyqr@]
	[,swsfqz = @swsfqz@]
	[,gdzt = @gdzt@]
	[,gldh = @gldh@]
	[,zldjxh = @zldjxh@]
	[,zlurlnw = @zlurlnw@]
	[,sfts = @sfts@]
WHERE
	[uuid = @uuid@]

--查询 附件管理档案列表（修改） 30000057
SELECT
	*
FROM
	sjjs_bz.fjglda
WHERE
	yxbz = 'Y'
	[AND ywbh like '%@ywbh@%']
	[AND ywlx = @ywlx@]
	[AND gdnd = @gdnd@]
	[AND gdrq = @gdrq@]
	[AND blry = @blry@]
ORDER BY
	lrrq DESC;

--增加归档方式字段
ALTER TABLE FJGLDA ADD GDFS VARCHAR2(255) NULL;
COMMENT ON COLUMN FJGLDA.GDFS IS '归档方式';

--插入附件管理档案主表数据 （修改）30000055
INSERT
	INTO
	sjjs_bz.fjglda ( uuid,
	ywbh,
	ywlx,
	gdnd,
	gdrq,
	blry,
	fjsl,
	gdbz,
	gdzt,
	gdfs,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@ywbh@],
[@ywlx@],
[@gdnd@],
[@gdrq@],
[@blry@],
[@fjsl@],
[@gdbz@],
[@gdzt@],
[@gdfs@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@]);

--附件管理档案表更新 (修改) 30000059
UPDATE
	sjjs_bz.fjglda
SET
	xgrq = sysdate
	[,ywbh = @ywbh@]
	[,ywlx = @ywlx@]
	[,gdnd = @gdnd@]
	[,gdrq = @gdrq@]
	[,blry = @blry@]
	[,fjsl = @fjsl@]
	[,gdbz = @gdbz@]
	[,gdzt = @gdzt@]
	[,gdfs = @gdfs@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--20250321 结束--

--20250322 开始--
--附件管理档案表新增以下字段
ALTER TABLE FJGLDA ADD NSRMC VARCHAR2(255) NULL;
COMMENT ON COLUMN FJGLDA.NSRMC IS '纳税人名称';
ALTER TABLE FJGLDA ADD NSRSBH VARCHAR2(255) NULL;
COMMENT ON COLUMN FJGLDA.NSRSBH IS '纳税人识别号';
--附件管理档案表重命名以下字段
ALTER TABLE FJGLDA RENAME COLUMN GDBZ TO YWID;

--插入附件管理档案主表数据 （修改）30000055
INSERT
	INTO
	sjjs_bz.fjglda ( uuid,
	ywbh,
	ywlx,
	gdnd,
	gdrq,
	blry,
	fjsl,
	ywid,
	gdzt,
	gdfs,
	nsrmc,
	nsrsbh,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@ywbh@],
[@ywlx@],
[@gdnd@],
[@gdrq@],
[@blry@],
[@fjsl@],
[@ywid@],
[@gdzt@],
[@gdfs@],
[@nsrmc@],
[@nsrsbh@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@]);

--附件管理档案表更新 (修改) 30000059
UPDATE
	sjjs_bz.fjglda
SET
	xgrq = sysdate
	[,ywbh = @ywbh@]
	[,ywlx = @ywlx@]
	[,gdnd = @gdnd@]
	[,gdrq = @gdrq@]
	[,blry = @blry@]
	[,fjsl = @fjsl@]
	[,ywid = @ywid@]
	[,gdzt = @gdzt@]
	[,gdfs = @gdfs@]
	[,nsrmc = @nsrmc@]
	[,nsrsbh = @nsrsbh@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--查询 附件管理档案列表（修改） 30000057
SELECT
	*
FROM
	sjjs_bz.fjglda
WHERE
	yxbz = 'Y'
	[AND ywbh like '%@ywbh@%']
	[AND ywlx = @ywlx@]
	[AND gdnd = @gdnd@]
	[AND gdrq = @gdrq@]
	[AND blry = @blry@]
	[AND uuid = @uuid@]
	[AND ywid = @ywid@]
ORDER BY
	lrrq DESC


--获取附件明细列表 （修改） 30000058
SELECT * FROM sjjs_bz.fjgldamx
WHERE yxbz = 'Y'
[AND uuid in (@uuids@)]
[AND zbuuid = @zbuuid@]
[AND gdzt = @gdzt@]
[AND wjzllx = @wjzllx@];

--附件管理档案明细表更新 （修改） 30000060
UPDATE
	sjjs_bz.fjgldamx
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,pzbh = @pzbh@]
	[,wjzllx = @wjzllx@]
	[,wjgs = @wjgs@]
	[,wjm = @wjm@]
	[,wjlj = @wjlj@]
	[,wjurl = @wjurl@]
	[,wjlx = @wjlx@]
	[,wjdx = @wjdx@]
	[,wjnr = @wjnr@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
	[,nsrsfqz = @nsrsfqz@]
	[,sfxyswqr = @sfxyswqr@]
	[,swsfyqr = @swsfyqr@]
	[,swsfqz = @swsfqz@]
	[,gdzt = @gdzt@]
	[,gldh = @gldh@]
	[,zldjxh = @zldjxh@]
	[,zlurlnw = @zlurlnw@]
	[,sfts = @sfts@]
WHERE
	[uuid in (@uuids@)]

--20250322 结束--

--20250324 开始--
--查询 附件管理档案列表（修改） 30000057
SELECT
	*
FROM
	sjjs_bz.fjglda
WHERE
	yxbz = 'Y'
	[AND ywbh like '%'||@ywbh@||'%']
	[AND ywlx = @ywlx@]
	[AND gdnd = @gdnd@]
	[AND TO_DATE(@gdrq@, 'YYYY-MM-DD') = @gdrq@]
	[AND blry = @blry@]
	[AND uuid = @uuid@]
	[AND ywid = @ywid@]
ORDER BY
	lrrq DESC

--20250324 结束--

--20250326 结束--
--查询附件管理档案列表（修改） 30000057
SELECT
	*
FROM
	sjjs_bz.fjglda
WHERE
	yxbz = 'Y'
	[AND ywbh like '%'||@ywbh@||'%']
	[AND ywlx = @ywlx@]
	[AND gdnd = @gdnd@]
	[AND gdrq like @gdrq@||'%']
	[AND blry = @blry@]
	[AND uuid = @uuid@]
	[AND ywid = @ywid@]
ORDER BY
	lrrq DESC

--20250326 结束--

--20250327 开始--
--获取附件明细表数量 （新增）
30000098
SELECT COUNT(1) SL FROM sjjs_bz.fjgldamx
WHERE yxbz = 'Y'
[AND uuid in (@uuids@)]
[AND zbuuid = @zbuuid@]
[AND gdzt = @gdzt@]
[AND wjzllx = @wjzllx@]

--20250327 结束--

--20250328 开始 --
--获取附件明细列表 （修改） 30000058
SELECT * FROM sjjs_bz.fjgldamx
WHERE yxbz = 'Y'
[AND uuid in (@uuids@)]
[AND zbuuid = @zbuuid@]
[AND gdzt = @gdzt@]
[AND wjzllx = @wjzllx@]
order by lrrq desc

--20250328 结束 --