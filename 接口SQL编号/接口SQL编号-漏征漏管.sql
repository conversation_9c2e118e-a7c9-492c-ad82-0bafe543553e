

-----------

新增数据：漏征漏管清单信息主表数据   LZLGQDXXZBSJ
30000089
INSERT INTO sjjs_bz.LZLGQDXXZBSJ ( 
uuid,
tjrq,
dwzcmc,
tyshxydm,
nsrzt,
zcd,
jyfw,
fddbr,
lxfs,
kzztdjlx,
ygsl,
yyqx,
ccwp,
zgswksdm,
zgswks,
jdxzdm,
jdxz,
ssglydm,
ssgly,
sfjxswdj,
sfycsj,
sftsgs,
djrwzt,
djjg,
zt,
bz,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@tjrq@],
[@dwzcmc@],
[@tyshxydm@],
[@nsrzt@],
[@zcd@],
[@jyfw@],
[@fddbr@],
[@lxfs@],
[@kzztdjlx@],
[@ygsl@],
[@yyqx@],
[@ccwp@],
[@zgswksdm@],
[@zgswks@],
[@jdxzdm@],
[@jdxz@],
[@ssglydm@],
[@ssgly@],
[@sfjxswdj@],
[@sfycsj@],
[@sftsgs@],
[@djrwzt@],
[@djjg@],
[@zt@],
[@bz@],
[@lrrdm@],

 sysdate, [@xgrdm@], sysdate, [@yxbz@] )


更新数据：漏征漏管清单信息主表数据   LZLGQDXXZBSJ
30000090
UPDATE sjjs_bz.LZLGQDXXZBSJ SET 
xgrq = sysdate
[,tjrq = @tjrq@]
[,dwzcmc = @dwzcmc@]
[,tyshxydm = @tyshxydm@]
[,nsrzt = @nsrzt@]
[,zcd = @zcd@]
[,jyfw = @jyfw@]
[,fddbr = @fddbr@]
[,lxfs = @lxfs@]
[,kzztdjlx = @kzztdjlx@]
[,ygsl = @ygsl@]
[,yyqx = @yyqx@]
[,ccwp = @ccwp@]
[,zgswksdm = @zgswksdm@]
[,zgswks = @zgswks@]
[,jdxzdm = @jdxzdm@]
[,jdxz = @jdxz@]
[,ssglydm = @ssglydm@]
[,ssgly = @ssgly@]
[,sfjxswdj = @sfjxswdj@]
[,sfycsj = @sfycsj@]
[,sftsgs = @sftsgs@]
[,djrwzt = @djrwzt@]
[,djjg = @djjg@]
[,zt = @zt@]
[,bz = @bz@]
[,lrrdm = @lrrdm@]
[,lrrq = @lrrq@]
[,xgrdm = @xgrdm@]
[,xgrq = @xgrq@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

数据查询：漏征漏管清单信息主表数据   LZLGQDXXZBSJ
30000091
select * from sjjs_bz.LZLGQDXXZBSJ WHERE 1=1 
[ AND uuid = @uuid@]
[ AND tjrq = @tjrq@]
[ AND dwzcmc = @dwzcmc@]
[ AND tyshxydm = @tyshxydm@]
[ AND nsrzt = @nsrzt@]
[ AND zcd = @zcd@]
[ AND jyfw = @jyfw@]
[ AND fddbr = @fddbr@]
[ AND lxfs = @lxfs@]
[ AND kzztdjlx = @kzztdjlx@]
[ AND ygsl = @ygsl@]
[ AND yyqx = @yyqx@]
[ AND ccwp = @ccwp@]
[ AND zgswksdm = @zgswksdm@]
[ AND zgswks = @zgswks@]
[ AND jdxzdm = @jdxzdm@]
[ AND jdxz = @jdxz@]
[ AND ssglydm = @ssglydm@]
[ AND ssgly = @ssgly@]
[ AND sfjxswdj = @sfjxswdj@]
[ AND sfycsj = @sfycsj@]
[ AND sftsgs = @sftsgs@]
[ AND djrwzt = @djrwzt@]
[ AND djjg = @djjg@]
[ AND zt = @zt@]
[ AND bz = @bz@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]
ORDER BY xgrq DESC;





-----------

新增数据：漏征漏管清单下发及反馈信息表数据   LZLGQDXFJFKXXBSJ
30000092
INSERT INTO sjjs_bz.LZLGQDXFJFKXXBSJ ( 
uuid,
zbuuid,
xfr,
xfrq,
xfbz,
fkr,
fkrq,
fkbz,
djrwwcrq,
djrwzt,
djjg,
djry,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@zbuuid@],
[@xfr@],
[@xfrq@],
[@xfbz@],
[@fkr@],
[@fkrq@],
[@fkbz@],
[@djrwwcrq@],
[@djrwzt@],
[@djjg@],
[@djry@],
[@lrrdm@],
 sysdate, [@xgrdm@], sysdate, [@yxbz@] )


更新数据：漏征漏管清单下发及反馈信息表数据   LZLGQDXFJFKXXBSJ
30000093
UPDATE sjjs_bz.LZLGQDXFJFKXXBSJ SET 
xgrq = sysdate
[,zbuuid = @zbuuid@]
[,xfr = @xfr@]
[,xfrq = @xfrq@]
[,xfbz = @xfbz@]
[,fkr = @fkr@]
[,fkrq = @fkrq@]
[,fkbz = @fkbz@]
[,djrwwcrq = @djrwwcrq@]
[,djrwzt = @djrwzt@]
[,djjg = @djjg@]
[,djry = @djry@]
[,lrrdm = @lrrdm@]
[,lrrq = @lrrq@]
[,xgrdm = @xgrdm@]
[,xgrq = @xgrq@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

数据查询：漏征漏管清单下发及反馈信息表数据   LZLGQDXFJFKXXBSJ
30000094
select * from sjjs_bz.LZLGQDXFJFKXXBSJ WHERE 1=1 
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND xfr = @xfr@]
[ AND xfrq = @xfrq@]
[ AND xfbz = @xfbz@]
[ AND fkr = @fkr@]
[ AND fkrq = @fkrq@]
[ AND fkbz = @fkbz@]
[ AND djrwwcrq = @djrwwcrq@]
[ AND djrwzt = @djrwzt@]
[ AND djjg = @djjg@]
[ AND djry = @djry@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]
ORDER BY xgrq DESC;




数据查询：多表查询-漏征漏管清单 与反馈表
30000095
select f.UUID, z.uuid ZBUUID, f.DJRWWCRQ, f.DJRWZT,f.DJJG,
z.tjrq, z.dwzcmc, z.tyshxydm, z.nsrzt, z.zcd, z.jyfw, z.fddbr, z.lxfs, z.kzztdjlx, z.ygsl, z.yyqx, z.ccwp,
z.zgswksdm, z.zgswks, z.jdxzdm, z.jdxz, z.ssglydm, z.ssgly, z.sfjxswdj, z.sfycsj, z.sftsgs, z.zt, z.bz
from sjjs_bz.lzlgqdxxzbsj z
JOIN sjjs_bz.lzlgqdxfjfkxxbsj f on z.uuid = f.ZBUUID
WHERE 1=1
[ AND z.uuid = @zbuuid@]
[ AND z.tjrq = @tjrq@]
[ AND z.dwzcmc = @dwzcmc@]
[ AND z.tyshxydm = @tyshxydm@]
[ AND z.nsrzt = @nsrzt@]
[ AND z.zcd = @zcd@]
[ AND z.jyfw = @jyfw@]
[ AND z.fddbr = @fddbr@]
[ AND z.lxfs = @lxfs@]
[ AND z.kzztdjlx = @kzztdjlx@]
[ AND z.ygsl = @ygsl@]
[ AND z.yyqx = @yyqx@]
[ AND z.ccwp = @ccwp@]
[ AND z.zgswksdm = @zgswksdm@]
[ AND z.zgswks = @zgswks@]
[ AND z.jdxzdm = @jdxzdm@]
[ AND z.jdxz = @jdxz@]
[ AND z.ssglydm = @ssglydm@]
[ AND z.ssgly = @ssgly@]
[ AND z.sfjxswdj = @sfjxswdj@]
[ AND z.sfycsj = @sfycsj@]
[ AND z.sftsgs = @sftsgs@]
[ AND z.djrwzt = @zdjrwzt@]
[ AND z.zt = @zt@]
[ AND z.bz = @bz@]
[ AND z.lrrdm = @lrrdm@]
[ AND z.lrrq = @lrrq@]
[ AND z.xgrdm = @xgrdm@]
[ AND z.xgrq = @xgrq@]
[ AND z.yxbz = @zyxbz@]

[ AND f.uuid = @uuid@]
[ AND f.djrwwcrq = @djrwwcrq@]
[ AND f.djjg = @djjg@]
[ AND f.djrwzt = @djrwzt@]
[ AND f.yxbz = @fyxbz@]


统计图表数据：漏征漏管清单信息主表数据   LZLGQDXXZBSJ
30000099
select substr(tjrq, 1, 7) yf, count(1) sl
from sjjs_bz.LZLGQDXXZBSJ
where sfjxswdj = [@sfjxswdj@] and tjrq LIKE [@tjrq@]||'%'
group by substr(tjrq, 1, 7)
