提交税友更新时间
2025年3月21日 17


新增数据：企业欠税管理-欠税清单表  QYQSGLQSQDB
30000061
INSERT INTO sjjs_bz.qyqsglqsqdb (
uuid,
tjrq,
sjly,
shxydm,
nsrmc,
nsrxydj,
nsrzt,
jkqx,
sfzrr,
fddbrsfzhm,
fddbrxm,
yzfsrq,
qslb,
jyqk,
zgswksdm,
zgswks,
jdxzdm,
jdxz,
ssglydm,
ssgly,
qsye,
wncq,
bnxq,
qsfxdj,
qcnldf,
djje,
zzsldje,
skcllx,
kkjssfqr,
sfxq,
sfjc,
sfhdlhd,
cjcs,
cjjg,
cjzt,
gdzt,
zzcjzt,
bz,
sffsswtzs,
zt,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
)
  VALUES ( 
[@uuid@],
[@tjrq@],
[@sjly@],
[@shxydm@],
[@nsrmc@],
[@nsrxydj@],
[@nsrzt@],
[@jkqx@],
[@sfzrr@],
[@fddbrsfzhm@],
[@fddbrxm@],
[@yzfsrq@],
[@qslb@],
[@jyqk@],
[@zgswksdm@],
[@zgswks@],
[@jdxzdm@],
[@jdxz@],
[@ssglydm@],
[@ssgly@],
[@qsye@],
[@wncq@],
[@bnxq@],
[@qsfxdj@],
[@qcnldf@],
[@djje@],
[@zzsldje@],
[@skcllx@],
[@kkjssfqr@],
[@sfxq@],
[@sfjc@],
[@sfhdlhd@],
[@cjcs@],
[@cjjg@],
[@cjzt@],
[@gdzt@],
[@zzcjzt@],
[@bz@],
[@sffsswtzs@],
[@zt@],
[@lrrdm@],
sysdate,
[@xgrdm@],
sysdate,
[@yxbz@]
)

更新数据：企业欠税管理-欠税清单表  QYQSGLQSQDB
30000062
UPDATE sjjs_bz.qyqsglqsqdb SET 
xgrq = sysdate
[,uuid = @uuid@]
[,tjrq = @tjrq@]
[,sjly = @sjly@]
[,shxydm = @shxydm@]
[,nsrmc = @nsrmc@]
[,nsrxydj = @nsrxydj@]
[,nsrzt = @nsrzt@]
[,jkqx = @jkqx@]
[,sfzrr = @sfzrr@]
[,fddbrsfzhm = @fddbrsfzhm@]
[,fddbrxm = @fddbrxm@]
[,yzfsrq = @yzfsrq@]
[,qslb = @qslb@]
[,jyqk = @jyqk@]
[,zgswksdm = @zgswksdm@]
[,zgswks = @zgswks@]
[,jdxzdm = @jdxzdm@]
[,jdxz = @jdxz@]
[,ssglydm = @ssglydm@]
[,ssgly = @ssgly@]
[,qsye = @qsye@]
[,wncq = @wncq@]
[,bnxq = @bnxq@]
[,qsfxdj = @qsfxdj@]
[,qcnldf = @qcnldf@]
[,djje = @djje@]
[,zzsldje = @zzsldje@]
[,skcllx = @skcllx@]
[,kkjssfqr = @kkjssfqr@]
[,sfxq = @sfxq@]
[,sfjc = @sfjc@]
[,sfhdlhd = @sfhdlhd@]
[,cjcs = @cjcs@]
[,cjjg = @cjjg@]
[,cjzt = @cjzt@]
[,gdzt = @gdzt@]
[,zzcjzt = @zzcjzt@]
[,bz = @bz@]
[,sffsswtzs = @sffsswtzs@]
[,zt = @zt@]
[,lrrdm = @lrrdm@]
[,lrrq = @lrrq@]
[,xgrdm = @xgrdm@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

数据查询-UUID：企业欠税管理-欠税清单表  QYQSGLQSQDB
30000063
select * from sjjs_bz.qyqsglqsqdb WHERE 1=1 
[ AND uuid = @uuid@]
[ AND tjrq = @tjrq@]
[ AND sjly = @sjly@]
[ AND shxydm = @shxydm@]
[ AND nsrmc = @nsrmc@]
[ AND nsrxydj = @nsrxydj@]
[ AND nsrzt = @nsrzt@]
[ AND jkqx = @jkqx@]
[ AND sfzrr = @sfzrr@]
[ AND fddbrsfzhm = @fddbrsfzhm@]
[ AND fddbrxm = @fddbrxm@]
[ AND yzfsrq = @yzfsrq@]
[ AND qslb = @qslb@]
[ AND jyqk = @jyqk@]
[ AND zgswksdm = @zgswksdm@]
[ AND jdxzdm = @jdxzdm@]
[ AND jdxz = @jdxz@]
[ AND ssglydm = @ssglydm@]
[ AND qsye = @qsye@]
[ AND wncq = @wncq@]
[ AND bnxq = @bnxq@]
[ AND qsfxdj = @qsfxdj@]
[ AND qcnldf = @qcnldf@]
[ AND djje = @djje@]
[ AND zzsldje = @zzsldje@]
[ AND skcllx = @skcllx@]
[ AND kkjssfqr = @kkjssfqr@]
[ AND sfxq = @sfxq@]
[ AND sfjc = @sfjc@]
[ AND sfhdlhd = @sfhdlhd@]
[ AND cjcs = @cjcs@]
[ AND cjjg = @cjjg@]
[ AND cjzt = @cjzt@]
[ AND gdzt = @gdzt@]
[ AND zzcjzt = @zzcjzt@]
[ AND bz = @bz@]
[ AND sffsswtzs = @sffsswtzs@]
[ AND zt = @zt@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]

[ AND ssgly LIKE '%'||@ssgly@||'%']
[ AND zgswks LIKE '%'||@zgswks@||'%']
ORDER BY xgrq DESC;


欠税统计：欠税管理-欠税统计
30000064
select [@bz@] fl, TO_CHAR(SUM(QSYE)) qsze, TO_CHAR(SUM(WNCQ)) cqze, TO_CHAR(SUM(BNXQ)) xqze from sjjs_bz.qyqsglqsqdb 
WHERE 1=1
[ AND yxbz = @yxbz@]
[ AND sfjc = @sfjc@]
[ AND sfhdlhd = @sfhdlhd@]

 

-----------------

新增数据：企业欠税管理-征期维护  ZQWH
30000065
INSERT INTO sjjs_bz.ZQWH (
uuid,
nd,
qqrq,
zzrq,
bz,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
)
VALUES ( 
[@uuid@], 
[@nd@], 
[TO_DATE(@qqrq@, 'YYYY-MM-DD HH24:MI:SS')], 
[TO_DATE(@zzrq@, 'YYYY-MM-DD HH24:MI:SS')], 
[@bz@], 
[@lrrdm@], 
sysdate, 
[@xgrdm@], 
sysdate, 
[@yxbz@]
)


更新数据：企业欠税管理-征期维护  ZQWH
30000066
UPDATE sjjs_bz.ZQWH SET 
xgrq = sysdate
[,nd = @nd@]
[,qqrq = TO_DATE(@qqrq@, 'YYYY-MM-DD HH24:MI:SS')]
[,zzrq = TO_DATE(@zzrq@, 'YYYY-MM-DD HH24:MI:SS')]
[,bz = @bz@]
[,xgrdm = @xgrdm@] 
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]


列表数据查询：企业欠税管理-征期维护  ZQWH
30000067
select * from sjjs_bz.ZQWH WHERE nd = [@nd@] AND yxbz = 'Y'
ORDER BY bz ;

-------

新增数据：企业欠税管理-欠税清单明细表 QYQSGLQSQDMXB
30000074
INSERT INTO sjjs_bz.qyqsglqsqdmxb ( 
uuid,
zbuuid,
nsrmc,
qszl,
qsje,
qsrq,
yzfsrq,
zsxm,
zspm,
xmmc,
ybtse,
skssqq,
skssqz,
jkqx,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES (
[@uuid@],
[@zbuuid@],
[@nsrmc@],
[@qszl@],
[@qsje@],
[@qsrq@],
[@yzfsrq@],
[@zsxm@],
[@zspm@],
[@xmmc@],
[@ybtse@],
[@skssqq@],
[@skssqz@],
[@jkqx@],
[@lrrdm@],
sysdate,
[@xgrdm@],
sysdate,
[@yxbz@]
 )

更新数据：企业欠税管理-欠税清单明细表 QYQSGLQSQDMXB
30000069
UPDATE sjjs_bz.qyqsglqsqdmxb SET 
xgrq = sysdate
[,uuid = @uuid@]
[,zbuuid = @zbuuid@]
[,nsrmc = @nsrmc@]
[,qszl = @qszl@]
[,qsje = @qsje@]
[,qsrq = @qsrq@]
[,yzfsrq = @yzfsrq@]
[,zsxm = @zsxm@]
[,zspm = @zspm@]
[,xmmc = @xmmc@]
[,ybtse = @ybtse@]
[,skssqq = @skssqq@]
[,skssqz = @skssqz@]
[,jkqx = @jkqx@]
[,xgrdm = @xgrdm@] 
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

列表数据查询：企业欠税管理-欠税清单明细表 QYQSGLQSQDMXB
30000070
select * from sjjs_bz.qyqsglqsqdmxb 
WHERE 1=1
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND nsrmc = @nsrmc@]
[ AND qszl = @qszl@]
[ AND qsje = @qsje@]
[ AND qsrq = @qsrq@]
[ AND yzfsrq = @yzfsrq@]
[ AND zsxm = @zsxm@]
[ AND zspm = @zspm@]
[ AND xmmc = @xmmc@]
[ AND ybtse = @ybtse@]
[ AND skssqq = @skssqq@]
[ AND skssqz = @skssqz@]
[ AND jkqx = @jkqx@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]
ORDER BY xgrq DESC;

-----------------


新增数据：企业欠税管理-追征任务清单表 QYQSGLZZRWQDB
30000071
INSERT INTO sjjs_bz.qyqsglzzrwqdb ( 
uuid,
zbuuid,
xfr,
xfrq,
xfbz,
tjrq,
shxydm,
nsrmc,
nsrzt,
jkqx,
sfzrr,
fddbrsfzhm,
fddbrxm,
qslb,
jyqk,
zgswksdm,
zgswks,
jdxzdm,
jdxz,
ssglydm,
ssgly,
qsye,
wncq,
bnxq,
sfjc,
sfhdlhd,
bz,
zzrwwcrq,
zzcs,
zzjg,
zzry,
zzrwzt,
fkr,
fkrq,
fkbz,
sfxsj,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz)
 VALUES (
[@uuid@],
[@zbuuid@],
[@xfr@],
[@xfrq@],
[@xfbz@],
[@tjrq@],
[@shxydm@],
[@nsrmc@],
[@nsrzt@],
[@jkqx@],
[@sfzrr@],
[@fddbrsfzhm@],
[@fddbrxm@],
[@qslb@],
[@jyqk@],
[@zgswksdm@],
[@zgswks@],
[@jdxzdm@],
[@jdxz@],
[@ssglydm@],
[@ssgly@],
[@qsye@],
[@wncq@],
[@bnxq@],
[@sfjc@],
[@sfhdlhd@],
[@bz@],
[@zzrwwcrq@],
[@zzcs@],
[@zzjg@],
[@zzry@],
[@zzrwzt@],
[@fkr@],
[@fkrq@],
[@fkbz@],
[@sfxsj@],
[@lrrdm@],
sysdate,
[@xgrDm@],
sysdate,
[@yxbz@] 
)

更新数据：企业欠税管理-追征任务清单表 QYQSGLZZRWQDB
30000072
UPDATE sjjs_bz.qyqsglzzrwqdb SET 
xgrq = sysdate
[,uuid = @uuid@]
[,zbuuid = @zbuuid@]
[,xfr = @xfr@]
[,xfrq = @xfrq@]
[,xfbz = @xfbz@]
[,tjrq = @tjrq@]
[,shxydm = @shxydm@]
[,nsrmc = @nsrmc@]
[,nsrzt = @nsrzt@]
[,jkqx = @jkqx@]
[,sfzrr = @sfzrr@]
[,fddbrsfzhm = @fddbrsfzhm@]
[,fddbrxm = @fddbrxm@]
[,qslb = @qslb@]
[,jyqk = @jyqk@]
[,zgswksdm = @zgswksdm@]
[,zgswks = @zgswks@]
[,jdxzdm = @jdxzdm@]
[,jdxz = @jdxz@]
[,ssglydm = @ssglydm@]
[,ssgly = @ssgly@]
[,qsye = @qsye@]
[,wncq = @wncq@]
[,bnxq = @bnxq@]
[,sfjc = @sfjc@]
[,sfhdlhd = @sfhdlhd@]
[,bz = @bz@]
[,zzrwwcrq = @zzrwwcrq@]
[,zzcs = @zzcs@]
[,zzjg = @zzjg@]
[,zzry = @zzry@]
[,zzrwzt = @zzrwzt@]
[,fkr = @fkr@]
[,fkrq = @fkrq@]
[,fkbz = @fkbz@]
[,sfxsj = @sfxsj@]
[,xgrdm = @xgrdm@] 
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]


数据查询：企业欠税管理-追征任务清单表 QYQSGLZZRWQDB
30000073
select * from sjjs_bz.qyqsglzzrwqdb WHERE 1 = 1
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND xfr = @xfr@]
[ AND xfrq = @xfrq@]
[ AND xfbz = @xfbz@]
[ AND tjrq = @tjrq@]
[ AND shxydm = @shxydm@]
[ AND nsrmc = @nsrmc@]
[ AND nsrzt = @nsrzt@]
[ AND jkqx = @jkqx@]
[ AND sfzrr = @sfzrr@]
[ AND fddbrsfzhm = @fddbrsfzhm@]
[ AND fddbrxm = @fddbrxm@]
[ AND qslb = @qslb@]
[ AND jyqk = @jyqk@]
[ AND zgswksdm = @zgswksdm@]
[ AND jdxzdm = @jdxzdm@]
[ AND jdxz = @jdxz@]
[ AND ssglydm = @ssglydm@]
[ AND qsye = @qsye@]
[ AND wncq = @wncq@]
[ AND bnxq = @bnxq@]
[ AND sfjc = @sfjc@]
[ AND sfhdlhd = @sfhdlhd@]
[ AND bz = @bz@]
[ AND zzrwwcrq = @zzrwwcrq@]
[ AND zzcs = @zzcs@]
[ AND zzjg = @zzjg@]
[ AND zzry = @zzry@]
[ AND zzrwzt = @zzrwzt@]
[ AND fkr = @fkr@]
[ AND fkrq = @fkrq@]
[ AND fkbz = @fkbz@]
[ AND sfxsj = @sfxsj@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]

[ AND ssgly LIKE '%'||@ssgly@||'%']
[ AND zgswks LIKE '%'||@zgswks@||'%']

ORDER BY xgrq DESC;


新增数据：企业欠税管理-追征任务清单-阻止出境上报及反馈表  QYQSGLZZRWQDZZCJSBJFKB
30000075
INSERT INTO SJJS_BZ.QYQSGLZZRWQDZZCJSBJFKB ( 
uuid,
zbuuid,
zzrwqduuid,
zzcjqysbr,
zzcjqysbrq,
zzcjqysbbz,
zzcjqyqrr,
zzcjqyqrrq,
zzcjqyqrbz,
zzcjfkr,
zzcjfkrq,
zzcjfkbz,
sfysb,
sfqrzzcj,
zzcjrwfkzt,
bz,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@zbuuid@],
[@zzrwqduuid@],
[@zzcjqysbr@],
[@zzcjqysbrq@],
[@zzcjqysbbz@],
[@zzcjqyqrr@],
[@zzcjqyqrrq@],
[@zzcjqyqrbz@],
[@zzcjfkr@],
[@zzcjfkrq@],
[@zzcjfkbz@],
[@sfysb@],
[@sfqrzzcj@],
[@zzcjrwfkzt@],
[@bz@],
[@lrrdm@],
sysdate,
[@xgrdm@],
sysdate,
[@yxbz@]
)


更新数据：企业欠税管理-追征任务清单-阻止出境上报及反馈表  QYQSGLZZRWQDZZCJSBJFKB
30000076
UPDATE SJJS_BZ.QYQSGLZZRWQDZZCJSBJFKB SET 
xgrq = sysdate 
[,zbuuid = @zbuuid@]
[,zzrwqduuid = @zzrwqduuid@]
[,zzcjqysbr = @zzcjqysbr@]
[,zzcjqysbrq = @zzcjqysbrq@]
[,zzcjqysbbz = @zzcjqysbbz@]
[,zzcjqyqrr = @zzcjqyqrr@]
[,zzcjqyqrrq = @zzcjqyqrrq@]
[,zzcjqyqrbz = @zzcjqyqrbz@]
[,zzcjfkr = @zzcjfkr@]
[,zzcjfkrq = @zzcjfkrq@]
[,zzcjfkbz = @zzcjfkbz@]
[,sfysb = @sfysb@]
[,sfqrzzcj = @sfqrzzcj@]
[,zzcjrwfkzt = @zzcjrwfkzt@]
[,bz = @bz@]
[,xgrdm = @xgrdm@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]


数据查询：企业欠税管理-追征任务清单-阻止出境上报及反馈表  QYQSGLZZRWQDZZCJSBJFKB
30000077
select * from SJJS_BZ.QYQSGLZZRWQDZZCJSBJFKB WHERE 1=1 
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND zzrwqduuid = @zzrwqduuid@]
[ AND zzcjqysbr = @zzcjqysbr@]
[ AND zzcjqysbrq = @zzcjqysbrq@]
[ AND zzcjqysbbz = @zzcjqysbbz@]
[ AND zzcjqyqrr = @zzcjqyqrr@]
[ AND zzcjqyqrrq = @zzcjqyqrrq@]
[ AND zzcjqyqrbz = @zzcjqyqrbz@]
[ AND zzcjfkr = @zzcjfkr@]
[ AND zzcjfkrq = @zzcjfkrq@]
[ AND zzcjfkbz = @zzcjfkbz@]
[ AND sfysb = @sfysb@]
[ AND sfqrzzcj = @sfqrzzcj@]
[ AND zzcjrwfkzt = @zzcjrwfkzt@]
[ AND bz = @bz@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]

ORDER BY xgrq DESC;


-----------

新增数据：企业欠税管理-大额欠税阻止出境申请表 QYQSGLDEQSZZCJSQB
30000078
INSERT INTO SJJS_BZ.QYQSGLDEQSZZCJSQB ( 
  uuid,
zbuuid,
zzcjsbjfkuuid,
zzcjtsr,
zzcjtsrrq,
zzcjryxm,
lxdh,
sfzh,
tsrq,
swgly,
glylxfs,
zzcjtsrbz,
zzcjzt,
zzcjksrq,
zzcjjsrq,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@zbuuid@],
[@zzcjsbjfkuuid@],
[@zzcjtsr@],
[@zzcjtsrrq@],
[@zzcjryxm@],
[@lxdh@],
[@sfzh@],
[@tsrq@],
[@swgly@],
[@glylxfs@],
[@zzcjtsrbz@],
[@zzcjzt@],
[@zzcjksrq@],
[@zzcjjsrq@],
[@lrrdm@],
 sysdate, [@xgrdm@], sysdate, [@yxbz@] )


更新数据：企业欠税管理-大额欠税阻止出境申请表 QYQSGLDEQSZZCJSQB
30000079
UPDATE SJJS_BZ.QYQSGLDEQSZZCJSQB SET 
xgrq = sysdate 
[,zbuuid = @zbuuid@]
[,zzcjsbjfkuuid = @zzcjsbjfkuuid@]
[,zzcjtsr = @zzcjtsr@]
[,zzcjtsrrq = @zzcjtsrrq@]
[,zzcjryxm = @zzcjryxm@]
[,lxdh = @lxdh@]
[,sfzh = @sfzh@]
[,tsrq = @tsrq@]
[,swgly = @swgly@]
[,glylxfs = @glylxfs@]
[,zzcjtsrbz = @zzcjtsrbz@]
[,zzcjzt = @zzcjzt@]
[,zzcjksrq = @zzcjksrq@]
[,zzcjjsrq = @zzcjjsrq@]
[,xgrdm = @xgrdm@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

数据查询：企业欠税管理-大额欠税阻止出境申请表 QYQSGLDEQSZZCJSQB
30000080
select * from SJJS_BZ.QYQSGLDEQSZZCJSQB WHERE 1=1 
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND zzcjsbjfkuuid = @zzcjsbjfkuuid@]
[ AND zzcjtsr = @zzcjtsr@]
[ AND zzcjtsrrq = @zzcjtsrrq@]
[ AND zzcjryxm = @zzcjryxm@]
[ AND lxdh = @lxdh@]
[ AND sfzh = @sfzh@]
[ AND tsrq = @tsrq@]
[ AND swgly = @swgly@]
[ AND glylxfs = @glylxfs@]
[ AND zzcjtsrbz = @zzcjtsrbz@]
[ AND zzcjzt = @zzcjzt@]
[ AND zzcjksrq = @zzcjksrq@]
[ AND zzcjjsrq = @zzcjjsrq@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]

ORDER BY xgrq DESC;



-----------

新增数据：企业欠税管理-解除大额欠税阻止出境申请表 QYQSGLJCDEQSZZCJSQB
30000081
INSERT INTO SJJS_BZ.QYQSGLJCDEQSZZCJSQB ( 
  uuid,
zbuuid,
zzcjsbjfkuuid,
jczzcjtsr,
jczzcjtsrrq,
jczzcjryxm,
lxdh,
sfzh,
tsrq,
swgly,
glylxfs,
jczzcjtsrbz,
jczzcjzt,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@zbuuid@],
[@zzcjsbjfkuuid@],
[@jczzcjtsr@],
[@jczzcjtsrrq@],
[@jczzcjryxm@],
[@lxdh@],
[@sfzh@],
[@tsrq@],
[@swgly@],
[@glylxfs@],
[@jczzcjtsrbz@],
[@jczzcjzt@],
[@lrrdm@],
 sysdate, [@xgrdm@], sysdate, [@yxbz@] )


更新数据：企业欠税管理-解除大额欠税阻止出境申请表 QYQSGLJCDEQSZZCJSQB
30000082
UPDATE SJJS_BZ.QYQSGLJCDEQSZZCJSQB SET 
xgrq = sysdate 
[,zbuuid = @zbuuid@]
[,zzcjsbjfkuuid = @zzcjsbjfkuuid@]
[,jczzcjtsr = @jczzcjtsr@]
[,jczzcjtsrrq = @jczzcjtsrrq@]
[,jczzcjryxm = @jczzcjryxm@]
[,lxdh = @lxdh@]
[,sfzh = @sfzh@]
[,tsrq = @tsrq@]
[,swgly = @swgly@]
[,glylxfs = @glylxfs@]
[,jczzcjtsrbz = @jczzcjtsrbz@]
[,jczzcjzt = @jczzcjzt@]
[,xgrdm = @xgrdm@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]

数据查询：企业欠税管理-解除大额欠税阻止出境申请表 QYQSGLJCDEQSZZCJSQB
30000083
select * from SJJS_BZ.QYQSGLJCDEQSZZCJSQB WHERE 1=1 
[ AND uuid = @uuid@]
[ AND zbuuid = @zbuuid@]
[ AND zzcjsbjfkuuid = @zzcjsbjfkuuid@]
[ AND jczzcjtsr = @jczzcjtsr@]
[ AND jczzcjtsrrq = @jczzcjtsrrq@]
[ AND jczzcjryxm = @jczzcjryxm@]
[ AND lxdh = @lxdh@]
[ AND sfzh = @sfzh@]
[ AND tsrq = @tsrq@]
[ AND swgly = @swgly@]
[ AND glylxfs = @glylxfs@]
[ AND jczzcjtsrbz = @jczzcjtsrbz@]
[ AND jczzcjzt = @jczzcjzt@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]


ORDER BY xgrq DESC;


-----------

新增数据：企业欠税管理-附件信息表 QYQSGLFJXXB
30000084
INSERT INTO SJJS_BZ.QYQSGLFJXXB ( 
uuid,
pzbh,
zbuuid,
wjzllx,
wjgs,
wjm,
wjlj,
wjurl,
wjlx,
wjdx,
wjnr,
nsrsfqz,
sfxyswqr,
swsfyqr,
swsfqz,
gdzt,
gldh,
zldjxh,
zlurlnw,
sfts,
lrrdm,
lrrq,
xgrdm,
xgrq,
yxbz
) 
VALUES ( 
[@uuid@],
[@pzbh@],
[@zbuuid@],
[@wjzllx@],
[@wjgs@],
[@wjm@],
[@wjlj@],
[@wjurl@],
[@wjlx@],
[@wjdx@],
[@wjnr@],
[@nsrsfqz@],
[@sfxyswqr@],
[@swsfyqr@],
[@swsfqz@],
[@gdzt@],
[@gldh@],
[@zldjxh@],
[@zlurlnw@],
[@sfts@],
[@lrrdm@],
 sysdate, [@xgrdm@], sysdate, [@yxbz@] )


更新数据：企业欠税管理-附件信息表 QYQSGLFJXXB
30000085
UPDATE SJJS_BZ.QYQSGLFJXXB SET 
xgrq = sysdate 
[,pzbh = @pzbh@]
[,zbuuid = @zbuuid@]
[,wjzllx = @wjzllx@]
[,wjgs = @wjgs@]
[,wjm = @wjm@]
[,wjlj = @wjlj@]
[,wjurl = @wjurl@]
[,wjlx = @wjlx@]
[,wjdx = @wjdx@]
[,wjnr = @wjnr@]
[,nsrsfqz = @nsrsfqz@]
[,sfxyswqr = @sfxyswqr@]
[,swsfyqr = @swsfyqr@]
[,swsfqz = @swsfqz@]
[,gdzt = @gdzt@]
[,gldh = @gldh@]
[,zldjxh = @zldjxh@]
[,zlurlnw = @zlurlnw@]
[,sfts = @sfts@]
[,xgrdm = @xgrdm@]
[,yxbz = @yxbz@]
WHERE uuid = [@uuid@]


数据查询：企业欠税管理-附件信息表 QYQSGLFJXXB
30000086
select * from SJJS_BZ.QYQSGLFJXXB WHERE 1=1 
[ AND uuid = @uuid@]
[ AND pzbh = @pzbh@]
[ AND zbuuid = @zbuuid@]
[ AND wjzllx = @wjzllx@]
[ AND wjgs = @wjgs@]
[ AND wjm = @wjm@]
[ AND wjlj = @wjlj@]
[ AND wjurl = @wjurl@]
[ AND wjlx = @wjlx@]
[ AND wjdx = @wjdx@]
[ AND wjnr = @wjnr@]
[ AND nsrsfqz = @nsrsfqz@]
[ AND sfxyswqr = @sfxyswqr@]
[ AND swsfyqr = @swsfyqr@]
[ AND swsfqz = @swsfqz@]
[ AND gdzt = @gdzt@]
[ AND gldh = @gldh@]
[ AND zldjxh = @zldjxh@]
[ AND zlurlnw = @zlurlnw@]
[ AND sfts = @sfts@]
[ AND lrrdm = @lrrdm@]
[ AND lrrq = @lrrq@]
[ AND xgrdm = @xgrdm@]
[ AND xgrq = @xgrq@]
[ AND yxbz = @yxbz@]

ORDER BY xgrq DESC;


数据查询-多表：大额欠税阻止出境企业上报-列表查询 & 大额欠税阻止出境-阻止出境企业名单-列表查询
30000087
select f.UUID, f.SFYSB, f.SFQRZZCJ, f.zzcjrwfkzt, z.zbuuid, z.uuid zzrwqduuid, z.xfr, z.xfrq, z.xfbz, z.tjrq, z.shxydm,
 z.nsrmc, z.nsrzt, z.qslb, z.jyqk, z.zgswksdm, z.zgswks, z.jdxzdm, z.jdxz, z.ssglydm, z.ssgly, z.qsye, z.wncq, z.bnxq,
 z.sfjc, z.sfhdlhd, z.bz, z.zzrwwcrq, z.zzcs, z.zzjg, z.zzry, z.zzrwzt, z.fkr, z.fkrq, z.fkbz, z.sfxsj,
 z.JKQX ,z.SFZRR ,z.FDDBRSFZHM ,z.FDDBRXM
FROM SJJS_BZ.qyqsglzzrwqdb z
LEFT JOIN SJJS_BZ.qyqsglzzrwqdzzcjsbjfkb f on z.UUID = f.zzrwqduuid
WHERE z.sfxsj='Y' and ((TO_NUMBER(z.qsye)>30000 and z.SFZRR='Y' ) or (TO_NUMBER(z.qsye)>=100000 and z.SFZRR='N') )
[ AND z.xfr = @xfr@]
[ AND z.xfrq = @xfrq@]
[ AND z.xfbz = @xfbz@]
[ AND z.tjrq = @tjrq@]
[ AND z.shxydm = @shxydm@]
[ AND z.nsrmc = @nsrmc@]
[ AND z.nsrzt = @nsrzt@]
[ AND z.jkqx = @jkqx@]
[ AND z.sfzrr = @sfzrr@]
[ AND z.fddbrsfzhm = @fddbrsfzhm@]
[ AND z.fddbrxm = @fddbrxm@]
[ AND z.qslb = @qslb@]
[ AND z.jyqk = @jyqk@]
[ AND z.zgswksdm = @zgswksdm@]
[ AND z.zgswks = @zgswks@]
[ AND z.jdxzdm = @jdxzdm@]
[ AND z.jdxz = @jdxz@]
[ AND z.ssglydm = @ssglydm@]
[ AND z.ssgly = @ssgly@]
[ AND z.wncq = @wncq@]
[ AND z.bnxq = @bnxq@]
[ AND z.sfjc = @sfjc@]
[ AND z.sfhdlhd = @sfhdlhd@]
[ AND z.bz = @bz@]
[ AND z.zzrwwcrq = @zzrwwcrq@]
[ AND z.zzcs = @zzcs@]
[ AND z.zzjg = @zzjg@]
[ AND z.zzry = @zzry@]
[ AND z.zzrwzt = @zzrwzt@]
[ AND z.fkr = @fkr@]
[ AND z.fkrq = @fkrq@]
[ AND z.fkbz = @fkbz@]
[ AND z.sfxsj = @sfxsj@]
[ AND z.yxbz = @zyxbz@]
[ AND f.zzrwqduuid = @zzrwqduuid@]
[ AND f.sfysb = @sfysb@]
[ AND f.sfqrzzcj = @sfqrzzcj@]
[ AND f.zzcjrwfkzt = @zzcjrwfkzt@]
[ AND f.yxbz = @fyxbz@]



数据查询-多表：大额欠税阻止出境-列表查询 & 阻止出境人员-列表查询
30000088
select f.UUID,f.ZBUUID,f.ZZCJSBJFKUUID,f.ZZCJZT,f.ZZCJRYXM,f.ZZCJKSRQ,f.ZZCJJSRQ,
 z.tjrq, z.sjly, z.shxydm, z.nsrmc, z.nsrxydj, z.nsrzt, z.yzfsrq, z.qslb, z.jyqk, z.zgswksdm, z.zgswks, z.jdxzdm,
 z.jdxz, z.ssglydm, z.ssgly, z.qsye, z.wncq, z.bnxq, z.qsfxdj, z.qcnldf, z.djje, z.zzsldje, z.skcllx, z.kkjssfqr,
 z.sfxq, z.sfjc, z.sfhdlhd, z.cjcs, z.cjjg, z.cjzt, z.gdzt, z.bz, z.sffsswtzs, z.zt,
 z.JKQX ,z.SFZRR ,z.FDDBRSFZHM ,z.FDDBRXM
FROM SJJS_BZ.qyqsglqsqdb z
JOIN SJJS_BZ.qyqsgldeqszzcjsqb f on z.UUID = f.ZBUUID
WHERE f.yxbz='Y'
[ AND z.tjrq = @tjrq@]
[ AND z.sjly = @sjly@]
[ AND z.shxydm = @shxydm@]
[ AND z.nsrmc = @nsrmc@]
[ AND z.nsrxydj = @nsrxydj@]
[ AND z.nsrzt = @nsrzt@]
[ AND z.jkqx = @jkqx@]
[ AND z.sfzrr = @sfzrr@]
[ AND z.fddbrsfzhm = @fddbrsfzhm@]
[ AND z.fddbrxm = @fddbrxm@]
[ AND z.yzfsrq = @yzfsrq@]
[ AND z.qslb = @qslb@]
[ AND z.jyqk = @jyqk@]
[ AND z.zgswksdm = @zgswksdm@]
[ AND z.zgswks = @zgswks@]
[ AND z.jdxzdm = @jdxzdm@]
[ AND z.jdxz = @jdxz@]
[ AND z.ssglydm = @ssglydm@]
[ AND z.ssgly = @ssgly@]
[ AND z.qsye = @qsye@]
[ AND z.wncq = @wncq@]
[ AND z.bnxq = @bnxq@]
[ AND z.qsfxdj = @qsfxdj@]
[ AND z.qcnldf = @qcnldf@]
[ AND z.djje = @djje@]
[ AND z.zzsldje = @zzsldje@]
[ AND z.skcllx = @skcllx@]
[ AND z.kkjssfqr = @kkjssfqr@]
[ AND z.sfxq = @sfxq@]
[ AND z.sfjc = @sfjc@]
[ AND z.sfhdlhd = @sfhdlhd@]
[ AND z.cjcs = @cjcs@]
[ AND z.yxbz = @zyxbz@]
[ AND f.zzcjsbjfkuuid = @zzcjsbjfkuuid@]
[ AND f.zzcjzt = @zzcjzt@]

