30000041
--插入 存量房信息采集企业 主表数据
INSERT
	INTO
	sjjs_bz.clfxxcjqy ( uuid,
	sqbh,
	sqzt,
	sqr,
	xsfdwzcmc,
	xsftyshxydm,
	xsffddbr,
	xsflxdz,
	xsflxdh,
	gmfdwzcmc,
	gmftyshxydm,
	gmffddbr,
	gmflxdz,
	gmflxdh,
	bhyy,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@sqbh@],
[@sqzt@],
[@sqr@],
[@xsfdwzcmc@],
[@xsftyshxydm@],
[@xsffddbr@],
[@xsflxdz@],
[@xsflxdh@],
[@gmfdwzcmc@],
[@gmftyshxydm@],
[@gmffddbr@],
[@gmflxdz@],
[@gmflxdh@],
[@bhyy@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@]);

30000042
--插入 存量房信息采集企业经办人表数据
INSERT
	INTO
	sjjs_bz.clfxxcjqyjbr ( uuid,
	zbuuid,
	jyjs,
	jbrxm,
	sfzh,
	lxdh,
	lxdz,
	wtrq,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@zbuuid@],
[@jyjs@],
[@jbrxm@],
[@sfzh@],
[@lxdh@],
[@lxdz@],
[@wtrq@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@] );

30000043
--插入 存量房信息采集企业房屋表数据
INSERT
	INTO
	sjjs_bz.clfxxcjqyfw ( uuid,
	zbuuid,
	fwlx,
	zldz,
	jzmj,
	cqzh,
	jzjg,
	sbdj,
	sbzj,
	fgslbh,
	fgtszt,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@zbuuid@],
[@fwlx@],
[@zldz@],
[@jzmj@],
[@cqzh@],
[@jzjg@],
[@sbdj@],
[@sbzj@],
[@fgslbh@],
[@fgtszt@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@]);

30000044
--插入 存量房信息采集企业附件
INSERT
	INTO
	sjjs_bz.clfxxcjqyfj ( uuid,
	pzbh,
	zbuuid,
	wjzllx,
	wjgs,
	wjm,
	wjlj,
	wjurl,
	wjlx,
	wjdx,
	wjnr,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz )
VALUES ( [@uuid@],
[@pzbh@],
[@zbuuid@],
[@wjzllx@],
[@wjgs@],
[@wjm@],
[@wjlj@],
[@wjurl@],
[@wjlx@],
[@wjdx@],
[@wjnr@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@]);

30000045
--1、查询 存量房信息采集企业审核 房屋列表
SELECT
	*
FROM
	sjjs_bz.clfxxcjqyfw
WHERE
	yxbz = 'Y' 
	[AND fgslbh = @fgslbh@]
	[AND cqzh = @cqzh@]
	[AND zldz LIKE '%@zldz@%']
ORDER BY
	lrrq DESC;

30000046
--1.1查询 房屋信息详情 根据房屋uuid获取房屋详情
SELECT * FROM sjjs_bz.clfxxcjqyfw WHERE  yxbz = 'Y' [AND uuid = @uuid@];

30000047
--1.2查询 主表详情 根据房屋uuid获取主表详情 使用1.1 获取的zbuuid 查询主表
SELECT * FROM sjjs_bz.clfxxcjqy WHERE yxbz = 'Y' [AND uuid=@zbuuid@] ;

30000048
--1.3查询经办人详情 根据房屋uuid获取经办人详情 使用1.1 获取的zbuuid查询经办人表
SELECT * FROM sjjs_bz.clfxxcjqyjbr WHERE yxbz = 'Y' [AND zbuuid = @zbuuid@] [AND uuid = @uuid@];

30000049
--1.4查询附件详情 根据房屋uuid获取附件详情 使用1.1获取的zbuuid查询附件表  wjgs (文件归属 1：纳税人，2:房管页面；3:税务端；4:公安局)
SELECT
	*
FROM
	sjjs_bz.clfxxcjqyfj
WHERE
	yxbz = 'Y'
	[AND zbuuid = @zbuuid@]
	[AND wjgs IN (@wjgs@)]
	[AND wjzllx = @wjzllx@]
	[AND uuid=@uuid@];

30000050
--主表审核数据更新，审核时必须按一批一批的审核，不能通过一部分驳回一部分，按申请编号或uuid更新主表状态，驳回时需填写驳回原因
	UPDATE sjjs_bz.clfxxcjqy SET sqzt = [@sqzt@] [,bhyy = @bhyy@] [,xgrdm=@xgrdm@] [,xgrq=TO_DATE(@xgrq@,'YYYY-MM-DD HH24:MI:SS')] 
	WHERE yxbz = 'Y' [AND sqbh=@sqbh@] ;

--20230319 开始 ----
-- 附件表新增以下字段 ----

ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD NSRSFQZ VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.NSRSFQZ IS '纳税人是否签字';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD SFXYSWQR VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.SFXYSWQR IS '是否需要税务确认';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD SWSFYQR VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.SWSFYQR IS '税务是否已确认';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD SWSFQZ VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.SWSFQZ IS '税务是否签字/签章';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD GDZT VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.GDZT IS '归档状态';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD GLDH VARCHAR2(100) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.GLDH IS '电子档案系统关联单号';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD ZLDJXH VARCHAR2(100) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.ZLDJXH IS '电子档案系统资料登记序号';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD ZLURLNW VARCHAR2(100) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.ZLURLNW IS '电子档案系统资料url';
ALTER TABLE SJJS_BZ.CLFXXCJQYFJ ADD SFTS VARCHAR2(10) NULL;
COMMENT ON COLUMN CLFXXCJQYFJ.SFTS IS '是否推送';

--插入 存量房信息采集企业附件 (修改)  30000044
INSERT
	INTO
	SJJS_BZ.clfxxcjqyfj ( uuid,
	pzbh,
	zbuuid,
	wjzllx,
	wjgs,
	wjm,
	wjlj,
	wjurl,
	wjlx,
	wjdx,
	wjnr,
	lrrq,
	xgrq,
	lrrdm,
	xgrdm,
	yxbz,
	nsrsfqz,
	sfxyswqr,
	swsfyqr,
	swsfqz,
	gdzt,
	gldh,
	zldjxh,
	zlurlnw,
	sfts)
VALUES ( [@uuid@],
[@pzbh@],
[@zbuuid@],
[@wjzllx@],
[@wjgs@],
[@wjm@],
[@wjlj@],
[@wjurl@],
[@wjlx@],
[@wjdx@],
[@wjnr@],
[TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')],
[TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')],
[@lrrdm@],
[@xgrdm@],
[@yxbz@],
[@nsrsfqz@],
[@sfxyswqr@],
[@swsfyqr@],
[@swsfqz@],
[@gdzt@],
[@gldh@],
[@zldjxh@],
[@zlurlnw@],
[@sfts@]);

--增加事务控制更新 主表 (新增) 30000051
UPDATE
	sjjs_bz.clfxxcjqy
SET
	xgrq = sysdate
	[,sqbh = @sqbh@]
	[,sqzt = @sqzt@]
	[,sqr = @sqr@]
	[,xsfdwzcmc = @xsfdwzcmc@]
	[,xsftyshxydm = @xsftyshxydm@]
	[,xsffddbr = @xsffddbr@]
	[,xsflxdz = @xsflxdz@]
	[,xsflxdh = @xsflxdh@]
	[,gmfdwzcmc = @gmfdwzcmc@]
	[,gmftyshxydm = @gmftyshxydm@]
	[,gmffddbr = @gmffddbr@]
	[,gmflxdz = @gmflxdz@]
	[,gmflxdh = @gmflxdh@]
	[,bhyy = @bhyy@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 经办人表（新增） 30000052
UPDATE
	sjjs_bz.clfxxcjqyjbr
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,jyjs = @jyjs@]
	[,jbrxm = @jbrxm@]
	[,sfzh = @sfzh@]
	[,lxdh = @lxdh@]
	[,lxdz = @lxdz@]
	[,wtrq = @wtrq@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 房屋信息表 （新增） 30000053
UPDATE
	sjjs_bz.clfxxcjqyfw
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,fwlx = @fwlx@]
	[,zldz = @zldz@]
	[,jzmj = @jzmj@]
	[,cqzh = @cqzh@]
	[,jzjg = @jzjg@]
	[,sbdj = @sbdj@]
	[,sbzj = @sbzj@]
	[,fgslbh = @fgslbh@]
	[,fgtszt = @fgtszt@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 附件信息表 （新增） 30000054
UPDATE
	sjjs_bz.clfxxcjqyfj
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,pzbh = @pzbh@]
	[,wjzllx = @wjzllx@]
	[,wjgs = @wjgs@]
	[,wjm = @wjm@]
	[,wjlj = @wjlj@]
	[,wjurl = @wjurl@]
	[,wjlx = @wjlx@]
	[,wjdx = @wjdx@]
	[,wjnr = @wjnr@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
	[,nsrsfqz = @nsrsfqz@]
	[,sfxyswqr = @sfxyswqr@]
	[,swsfyqr = @swsfyqr@]
	[,swsfqz = @swsfqz@]
	[,gdzt = @gdzt@]
	[,gldh = @gldh@]
	[,zldjxh = @zldjxh@]
	[,zlurlnw = @zlurlnw@]
	[,sfts = @sfts@]
WHERE
	[uuid = @uuid@]

--20250319 结束 ----

--20250321 开始--
--增加事务控制更新 主表 (修改) 30000051
UPDATE
	sjjs_bz.clfxxcjqy
SET
	xgrq = sysdate
	[,sqbh = @sqbh@]
	[,sqzt = @sqzt@]
	[,sqr = @sqr@]
	[,xsfdwzcmc = @xsfdwzcmc@]
	[,xsftyshxydm = @xsftyshxydm@]
	[,xsffddbr = @xsffddbr@]
	[,xsflxdz = @xsflxdz@]
	[,xsflxdh = @xsflxdh@]
	[,gmfdwzcmc = @gmfdwzcmc@]
	[,gmftyshxydm = @gmftyshxydm@]
	[,gmffddbr = @gmffddbr@]
	[,gmflxdz = @gmflxdz@]
	[,gmflxdh = @gmflxdh@]
	[,bhyy = @bhyy@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 经办人表（修改） 30000052
UPDATE
	sjjs_bz.clfxxcjqyjbr
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,jyjs = @jyjs@]
	[,jbrxm = @jbrxm@]
	[,sfzh = @sfzh@]
	[,lxdh = @lxdh@]
	[,lxdz = @lxdz@]
	[,wtrq = @wtrq@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 房屋信息表 （修改） 30000053
UPDATE
	sjjs_bz.clfxxcjqyfw
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,fwlx = @fwlx@]
	[,zldz = @zldz@]
	[,jzmj = @jzmj@]
	[,cqzh = @cqzh@]
	[,jzjg = @jzjg@]
	[,sbdj = @sbdj@]
	[,sbzj = @sbzj@]
	[,fgslbh = @fgslbh@]
	[,fgtszt = @fgtszt@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
WHERE
	[uuid = @uuid@]

--增加事务控制更新 附件信息表 （修改） 30000054
UPDATE
	sjjs_bz.clfxxcjqyfj
SET
	xgrq = sysdate
	[,zbuuid = @zbuuid@]
	[,pzbh = @pzbh@]
	[,wjzllx = @wjzllx@]
	[,wjgs = @wjgs@]
	[,wjm = @wjm@]
	[,wjlj = @wjlj@]
	[,wjurl = @wjurl@]
	[,wjlx = @wjlx@]
	[,wjdx = @wjdx@]
	[,wjnr = @wjnr@]
	[,lrrq = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,lrrdm = @lrrdm@]
	[,xgrdm = @xgrdm@]
	[,yxbz = @yxbz@]
	[,nsrsfqz = @nsrsfqz@]
	[,sfxyswqr = @sfxyswqr@]
	[,swsfyqr = @swsfyqr@]
	[,swsfqz = @swsfqz@]
	[,gdzt = @gdzt@]
	[,gldh = @gldh@]
	[,zldjxh = @zldjxh@]
	[,zlurlnw = @zlurlnw@]
	[,sfts = @sfts@]
WHERE
	[uuid = @uuid@]

-- 	20250321 结束 ---

-- 	20250322 结束 ---
--查询存量房企业附件详情 修改 30000049
SELECT
	*
FROM
	sjjs_bz.clfxxcjqyfj
WHERE
	yxbz = 'Y'
	[AND zbuuid = @zbuuid@]
	[AND wjgs IN (@wjgs@)]
	[AND wjzllx = @wjzllx@]
	[AND uuid=@uuid@]
	[AND gdzt=@gdzt@];

-- 	20250322 结束 ---

-- 	20250324 开始 ---
--查询经办人详情 修改 30000048
SELECT * FROM sjjs_bz.clfxxcjqyjbr WHERE yxbz = 'Y' [AND zbuuid = @zbuuid@] [AND uuid = @uuid@];

-- 	20250324 结束 ---