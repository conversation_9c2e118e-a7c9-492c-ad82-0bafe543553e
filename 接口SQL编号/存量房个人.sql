存量房税费审核列表 30000008
SELECT i.id, i.apply_num, i.apply_time, i.apply_status, mt.acceptance_num, tmp.real_estate_unit_num ,tmp.real_estate_name ,tmp.house_location, i.house_type ,tmp.residential_type ,tmp.building_area ,tmp.total_price, i.applicant, i.id_num,  bi.name, bi.id_num id_num_2 , i.update_time
FROM sjjs_bz.real_estate_collection_info i 
left join sjjs_bz.real_estate_buyer_info bi on i.id = bi.reciid and bi.deleted = 0 and bi.sort=1
left join sjjs_bz.real_estate_rcmt mt on i.id = mt.reciid and mt.deleted = 0
left join (
	select h.reciid ,h.real_estate_unit_num ,h.real_estate_name ,h.house_location ,h.residential_type ,h.building_area ,h.total_price from sjjs_bz.real_estate_chi h WHERE deleted = 0 
	UNION ALL
	select b.reciid ,b.real_estate_unit_num ,'' real_estate_name ,b.house_location ,'' residential_type ,b.building_area ,b.total_price from sjjs_bz.real_estate_cbi b WHERE deleted = 0 
	UNION ALL
	select a.reciid ,a.real_estate_unit_num ,'' real_estate_name ,a.house_location ,'' residential_type ,a.building_area ,a.total_price from sjjs_bz.real_estate_cai a WHERE deleted = 0 
	UNION ALL
	select o.reciid ,o.real_estate_unit_num ,'' real_estate_name ,o.house_location ,'' residential_type ,o.building_area ,o.total_price from sjjs_bz.real_estate_coi o WHERE deleted = 0 
	UNION ALL
	select p.reciid ,p.real_estate_unit_num ,'' real_estate_name ,p.house_location ,'' residential_type ,p.building_area ,p.total_price from sjjs_bz.real_estate_cpi p WHERE deleted = 0 
) tmp on i.id = tmp.reciid 
where i.deleted = 0 
[and mt.acceptance_num = @acceptanceNum@]
[and tmp.real_estate_unit_num = @realEstateUnitNum@]
[and i.applicant = @applicant@]
[and bi.name = @name@]
[and i.phone = @phone@]
[and bi.phone = @buyerPhone@]
ORDER BY i.create_time DESC

详情页

	主表详情  30000009
	SELECT * FROM real_estate_collection_info WHERE deleted = 0 [AND id=@id@];

	卖方信息列表  30000010
	SELECT * FROM real_estate_seller_info WHERE deleted = 0 [AND reciid=@reciid@];

	买方信息列表  30000011
	SELECT * FROM sjjs_bz.real_estate_buyer_info WHERE deleted = 0 [AND reciid=@reciid@];
	
	个人住宅详情  30000012
	SELECT * FROM sjjs_bz.real_estate_chi WHERE deleted = 0 [AND reciid=@reciid@];
	商品用房详情 30000013
	SELECT * FROM sjjs_bz.real_estate_cbi WHERE deleted = 0 [AND reciid=@reciid@];
	办公用房详情 30000014
	SELECT * FROM sjjs_bz.real_estate_coi WHERE deleted = 0 [AND reciid=@reciid@];
	公寓详情 30000015
	SELECT * FROM sjjs_bz.real_estate_cai 
	WHERE deleted = 0 [AND reciid=@reciid@];
	停车位详情 30000016
	SELECT * FROM sjjs_bz.real_estate_cpi WHERE deleted = 0 [AND reciid=@reciid@];
	附件列表 30000017
	SELECT * FROM sjjs_bz.real_estate_file 
	WHERE deleted = 0 [AND ownership IN (@ownership@)] [AND reciid=@reciid@] [ AND document_type=@documentType@] [AND id=@id@];


房管材料XXXXXX
	主表详情 30000018
	SELECT id, reciid, hall_window_num, acceptance_num, real_estate_unit_num, seller_name, seller_id_num, buyer_name, buyer_id_num, push_status, remark, create_time, update_time, creator, updater, deleted FROM sjjs_bz.real_estate_rcmt 
	WHERE deleted = 0 [AND id=@id@] [AND reciid=@reciid@]

	
----------------------------表添加语句--------------------------------------------------
real_estate_buyer _info[买方信息表] - 写入
30000020
INSERT INTO sjjs_bz.REAL_ESTATE_BUYER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, OBLIGEE_NATURE, HOLDING_RATIO,  SHARE_SITUATION,  SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) 
VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@obligeeNature@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);


real_estate_cai[存量商业用房评估信息采集表-公寓] - 写入
30000021
INSERT INTO sjjs_bz.REAL_ESTATE_CAI(ID, RECIID, HOUSE_TYPE, EVALUATE_PARTITION, HOUSE_LOCATION, PM_NAME, REAL_ESTATE_CERTIFICATE_NUM, REAL_ESTATE_UNIT_NUM, PROPERTY_RIGHT_OWNER, ID_TYPE, ID_NUM, PHONE, BUILDING_AREA, FLOOR, BUILDING_STRUCTURE, LAND_USE_RIGHT_TYPE, LAND_USE_RIGHT_END_DATE, REMAINING_LAND_USE_ERM, FLOOR_HEIGHT, YEAR_BUILT, BUILDING_IMPLEMENT, TRANSACTION_DATE, TOTAL_PRICE, UNIT_PRICE, HOUSE_OTHER_DESCRIPTION, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@houseType@], [@evaluatePartition@], [@houseLocation@], [@pmName@], [@realEstateCertificateNum@], [@realEstateUnitNum@], [@propertyRightOwner@], [@idType@], [@idNum@], [@phone@], [@buildingArea@], [@floor@], [@buildingStructure@], [@landUseRightType@], [@landUseRightEndDate@], [@remainingLandUseErm@], [@floorHeight@], [@yearBuilt@], [@buildingImplement@], [@transactionDate@], [@declareTransactionTotalPrice@], [@declareTransactionUnitPrice@], [@houseOtherDescription@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);


real_estate_cbi[存量商业用房评估信息采集表-商服用房] - 写入real_estate_collection_business_info
30000022
INSERT INTO sjjs_bz.REAL_ESTATE_CBI(ID, RECIID, HOUSE_TYPE, EVALUATE_PARTITION, HOUSE_LOCATION, PM_NAME, REAL_ESTATE_CERTIFICATE_NUM, REAL_ESTATE_UNIT_NUM, PROPERTY_RIGHT_OWNER, ID_TYPE, ID_NUM, PHONE, BUILDING_AREA, FLOOR, BUILDING_STRUCTURE, LAND_USE_RIGHT_TYPE, LAND_USE_RIGHT_END_DATE, REMAINING_LAND_USE_ERM, HOUSE_SHAPE, TRANSACTION_DATE, TOTAL_PRICE, UNIT_PRICE, HOUSE_OTHER_DESCRIPTION, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@houseType@], [@evaluatePartition@], [@houseLocation@], [@pmName@], [@realEstateCertificateNum@], [@realEstateUnitNum@], [@propertyRightOwner@], [@idType@], [@idNum@], [@phone@], [@buildingArea@], [@floor@], [@buildingStructure@], [@landUseRightType@], [@landUseRightEndDate@], [@remainingLandUseErm@], [@houseShape@], [@transactionDate@], [@declareTransactionTotalPrice@], [@declareTransactionUnitPrice@], [@houseOtherDescription@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);


real_estate_chi[存量住房信息采集表] - 写入real_estate_collection_house_info
30000023
INSERT INTO sjjs_bz.REAL_ESTATE_CHI(ID, RECIID, HOUSE_LOCATION, REAL_ESTATE_CERTIFICATE_NUM, REAL_ESTATE_UNIT_NUM, BUILDING_AREA, HOUSE_PURPOSE, CERTIFICATE_TYPE, PAYMENT_METHOD, REAL_ESTATE_NAME, SUITE_TYPE, HOUSE_ROOM, HOUSE_HALL, HOUSE_BATHROOM, HOUSE_KITCHEN, RESIDENTIAL_TYPE, GROUND_FLOORS, UNDERGROUND_FLOORS, TOWARD, PM_LEVEL, SUPPORTING_FACILITY, BUILDING_COMPLETION_YEAR, TOTAL_PRICE, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@houseLocation@], [@realEstateCertificateNum@], [@realEstateUnitNum@], [@buildingArea@], [@housePurpose@], [@certificateType@], [@paymentMethod@], [@realEstateName@], [@suiteType@], [@houseRoom@], [@houseHall@], [@houseBathroom@], [@houseKitchen@], [@residentialType@], [@groundFloors@], [@undergroundFloors@], [@toward@], [@pmLevel@], [@supportingFacility@], [@buildingCompletionYear@], [@declareTransactionTotalPrice@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);

real_estate_collection_info[存量房交易信息采集信息表] - 写入real_estate_collection_info	
30000024
INSERT INTO sjjs_bz.REAL_ESTATE_COLLECTION_INFO(ID, APPLY_NUM, APPLICANT, APPLY_TIME, ID_TYPE, ID_NUM, PHONE, REAL_ESTATE_UNIT_NUM, APPLY_STATUS, SELLER_STATUS, BUYER_STATUS, SHARE_SITUATION, HOUSE_TYPE, REMARK, APPLY_REMARK,HOUSE_PURPOSE,CERTIFICATE_TYPE,PAYMENT_METHOD, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@applyNum@], [@applicant@], [TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')], [@idType@], [@idNum@], [@phone@], [@realEstateUnitNum@], [@applyStatus@], [@sellerStatus@], [@buyerStatus@], [@shareSituation@], [@houseType@], [@remark@], [@applyRemark@], [@housePurpose@], [@certificateType@], [@paymentMethod@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);

real_estate_coi[存量商业用房评估信息采集表-办公用房] - 写入	real_estate_collection_office_info
30000025
INSERT INTO sjjs_bz.REAL_ESTATE_COI(ID, RECIID, HOUSE_TYPE, EVALUATE_PARTITION, HOUSE_LOCATION, PM_NAME, REAL_ESTATE_CERTIFICATE_NUM, REAL_ESTATE_UNIT_NUM, PROPERTY_RIGHT_OWNER, ID_TYPE, ID_NUM, PHONE, BUILDING_AREA, FLOOR, BUILDING_STRUCTURE, LAND_USE_RIGHT_TYPE, LAND_USE_RIGHT_END_DATE, REMAINING_LAND_USE_ERM, YEAR_BUILT, TRANSACTION_DATE, TOTAL_PRICE, UNIT_PRICE, HOUSE_OTHER_DESCRIPTION, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@houseType@], [@evaluatePartition@], [@houseLocation@], [@pmName@], [@realEstateCertificateNum@], [@realEstateUnitNum@], [@propertyRightOwner@], [@idType@], [@idNum@], [@phone@], [@buildingArea@], [@floor@], [@buildingStructure@], [@landUseRightType@], [@landUseRightEndDate@], [@remainingLandUseErm@], [@yearBuilt@], [@transactionDate@], [@declareTransactionTotalPrice@], [@declareTransactionUnitPrice@], [@houseOtherDescription@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@])

real_estate_cpi[存量商业用房评估信息采集表-车位] - 写入real_estate_collection_parking_info
30000026
INSERT INTO sjjs_bz.REAL_ESTATE_CPI(ID, RECIID, HOUSE_TYPE, EVALUATE_PARTITION, HOUSE_LOCATION, PM_NAME, REAL_ESTATE_CERTIFICATE_NUM, REAL_ESTATE_UNIT_NUM, PROPERTY_RIGHT_OWNER, ID_TYPE, ID_NUM, PHONE, BUILDING_AREA, FLOOR, INTERNAL_AREA, LAND_USE_RIGHT_TYPE, LAND_USE_RIGHT_END_DATE, REMAINING_LAND_USE_ERM, PARKING_SPACE_TYPE, TRANSACTION_DATE, TOTAL_PRICE, UNIT_PRICE, HOUSE_OTHER_DESCRIPTION, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@houseType@], [@evaluatePartition@], [@houseLocation@], [@pmName@], [@realEstateCertificateNum@], [@realEstateUnitNum@], [@propertyRightOwner@], [@idType@], [@idNum@], [@phone@], [@buildingArea@], [@floor@], [@internalArea@], [@landUseRightType@], [@landUseRightEndDate@], [@remainingLandUseErm@], [@parkingSpaceType@], [@transactionDate@], [@declareTransactionTotalPrice@], [@declareTransactionUnitPrice@], [@houseOtherDescription@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@])


real_estate_file[存量房交易-文件表] - 写入real_estate_file
30000027
INSERT INTO sjjs_bz.REAL_ESTATE_FILE(ID, CONFIG_ID, RECIID, DOCUMENT_TYPE, OWNERSHIP, NAME, PATH, URL, TYPE, FILE_SIZE, CONTENT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@configId@], [@reciid@], [@documentType@], [@ownership@], [@name@], [@path@], [@url@], [@type@], [@size@], [@content@], [@creator@],[TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')],  [@deleted@])


real_estate_rcmt[存量房交易-不动产登记中心材料传递] - 写入real_estate_registration_center_material_transmission
30000028
INSERT INTO sjjs_bz.REAL_ESTATE_RCMT(ID, RECIID, HALL_WINDOW_NUM, ACCEPTANCE_NUM, REAL_ESTATE_UNIT_NUM, SELLER_NAME, SELLER_ID_NUM, BUYER_NAME, BUYER_ID_NUM, PUSH_STATUS, REMARK, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@hallWindowNum@], [@acceptanceNum@], [@realEstateUnitNum@], [@sellerName@], [@sellerIdNum@], [@buyerName@], [@buyerIdNum@], [@pushStatus@], [@remark@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@] );


real_estate_seller_info[卖方信息表 - 写入real_estate_seller_info
30000029
INSERT INTO sjjs_bz.REAL_ESTATE_SELLER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, HOLDING_RATIO,SHARE_SITUATION, SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@]);

--------------------------------------表更新语句------------------------------------------------------------------

1、附件信息更新 30000030
update sjjs_bz.REAL_ESTATE_FILE set UPDATE_TIME=sysdate [,CONFIG_ID = @configId@][,RECIID = @reciid@][,DOCUMENT_TYPE = @documentType@][,OWNERSHIP = @ownership@][,NAME = @name@][,PATH = @path@]
[,URL = @url@][,TYPE = @type@][,FILE_SIZE = @size@][,CONTENT = @content@][,UPDATER = @updater@][,DELETED = @deleted@]
where id=[@id@]

2、主表审核更新 30000019
UPDATE sjjs_bz.real_estate_collection_info SET apply_status = [@applyStatus@] [,apply_remark = @applyRemark@] [, UPDATER = @updater@] [, UPDATE_TIME = TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')] WHERE deleted = 0 AND [id=@id@]

--------------------------------------新增接口更新表数据------------------------------------------------------------------
添加Delete语句用于实现事物
30000031 主表详情 
--delete sjjs_bz.real_estate_collection_info WHERE [id=@id@]
update sjjs_bz.REAL_ESTATE_COLLECTION_INFO set UPDATE_TIME=sysdate [,APPLY_NUM = @applyNum@][,APPLICANT = @applicant@][,APPLY_TIME = TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')][,ID_TYPE = @idType@][,ID_NUM = @idNum@]
[,PHONE = @phone@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,APPLY_STATUS = @applyStatus@][,SELLER_STATUS = @sellerStatus@][,BUYER_STATUS = @buyerStatus@][,SHARE_SITUATION = @shareSituation@][,HOUSE_TYPE = @houseType@]
[,REMARK = @remark@][,APPLY_REMARK = @applyRemark@][,HOUSE_PURPOSE = @housePurpose@][,CERTIFICATE_TYPE = @certificateType@][,PAYMENT_METHOD = @paymentMethod@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]


30000032 卖方信息列表 通过id或reciid删除关联表数据
--delete sjjs_bz.real_estate_seller_info WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_SELLER_INFO set UPDATE_TIME=sysdate [,RECIID = @reciid@][,OBLIGEE_TYPE = @obligeeType@][,NAME = @name@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,MARITAL_STATUS = @maritalStatus@]
[,HOUSE_NUMBER = @houseNumber@][,HOLDING_RATIO = @holdingRatio@][,SHARE_SITUATION = @shareSituation@][,SORT = @sort@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]


30000033 买方信息列表
--delete sjjs_bz.real_estate_buyer_info WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_BUYER_INFO set UPDATE_TIME=sysdate [,RECIID = @reciid@][,OBLIGEE_TYPE = @obligeeType@][,NAME = @name@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,MARITAL_STATUS = @maritalStatus@]
[,HOUSE_NUMBER = @houseNumber@][,OBLIGEE_NATURE = @obligeeNature@][,HOLDING_RATIO = @holdingRatio@][, SHARE_SITUATION = @shareSituation@][, SORT = @sort@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]

30000034 个人住宅详情
--delete sjjs_bz.real_estate_chi WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_CHI set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HOUSE_LOCATION = @houseLocation@][,REAL_ESTATE_CERTIFICATE_NUM = @realEstateCertificateNum@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@]
[,BUILDING_AREA = @buildingArea@][,HOUSE_PURPOSE = @housePurpose@][,CERTIFICATE_TYPE = @certificateType@][,PAYMENT_METHOD = @paymentMethod@][,REAL_ESTATE_NAME = @realEstateName@][,SUITE_TYPE = @suiteType@]
[,HOUSE_ROOM = @houseRoom@][,HOUSE_HALL = @houseHall@][,HOUSE_BATHROOM = @houseBathroom@][,HOUSE_KITCHEN = @houseKitchen@][,RESIDENTIAL_TYPE = @residentialType@][,GROUND_FLOORS = @groundFloors@]
[,UNDERGROUND_FLOORS = @undergroundFloors@][,TOWARD = @toward@][,PM_LEVEL = @pmLevel@][,SUPPORTING_FACILITY = @supportingFacility@][,BUILDING_COMPLETION_YEAR = @buildingCompletionYear@][,TOTAL_PRICE = @declareTransactionTotalPrice@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]

30000035 商品用房详情
--delete sjjs_bz.real_estate_cbi WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_CBI set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HOUSE_TYPE = @houseType@][,EVALUATE_PARTITION = @evaluatePartition@][,HOUSE_LOCATION = @houseLocation@][,PM_NAME = @pmName@][,REAL_ESTATE_CERTIFICATE_NUM = @realEstateCertificateNum@]
[,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,PROPERTY_RIGHT_OWNER = @propertyRightOwner@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,BUILDING_AREA = @buildingArea@][,FLOOR = @floor@][,BUILDING_STRUCTURE = @buildingStructure@][,LAND_USE_RIGHT_TYPE = @landUseRightType@]
[,LAND_USE_RIGHT_END_DATE = @landUseRightEndDate@][,REMAINING_LAND_USE_ERM = @remainingLandUseErm@][,HOUSE_SHAPE = @houseShape@][,TRANSACTION_DATE = @transactionDate@][,TOTAL_PRICE = @declareTransactionTotalPrice@][,UNIT_PRICE = @declareTransactionUnitPrice@][,HOUSE_OTHER_DESCRIPTION = @houseOtherDescription@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]

30000036 办公用房详情
--delete sjjs_bz.real_estate_coi WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_COI set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HOUSE_TYPE = @houseType@][,EVALUATE_PARTITION = @evaluatePartition@][,HOUSE_LOCATION = @houseLocation@][,PM_NAME = @pmName@][,REAL_ESTATE_CERTIFICATE_NUM = @realEstateCertificateNum@]
[,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,PROPERTY_RIGHT_OWNER = @@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,BUILDING_AREA = @buildingArea@][,FLOOR = @floor@][,BUILDING_STRUCTURE = @buildingStructure@]
[,LAND_USE_RIGHT_TYPE = @landUseRightType@][,LAND_USE_RIGHT_END_DATE = @landUseRightEndDate@][,REMAINING_LAND_USE_ERM = @remainingLandUseErm@][,YEAR_BUILT = @yearBuilt@][,TRANSACTION_DATE = @transactionDate@][,TOTAL_PRICE = @declareTransactionTotalPrice@]
[,UNIT_PRICE = @declareTransactionUnitPrice@][,HOUSE_OTHER_DESCRIPTION = @houseOtherDescription@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]

30000037 公寓详情
--delete sjjs_bz.real_estate_cai WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_CAI set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HOUSE_TYPE = @houseType@][,EVALUATE_PARTITION = @evaluatePartition@][,HOUSE_LOCATION = @houseLocation@][,PM_NAME = @pmName@]
[,REAL_ESTATE_CERTIFICATE_NUM = @realEstateCertificateNum@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,PROPERTY_RIGHT_OWNER = @propertyRightOwneridType@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@]
[,BUILDING_AREA = @buildingArea@][,FLOOR = @floor@][,BUILDING_STRUCTURE = @buildingStructure@][,LAND_USE_RIGHT_TYPE = @landUseRightType@][,LAND_USE_RIGHT_END_DATE = @landUseRightEndDate@][,REMAINING_LAND_USE_ERM = @remainingLandUseErm@]
[,FLOOR_HEIGHT = @floorHeight@][,YEAR_BUILT = @yearBuilt@][,BUILDING_IMPLEMENT = @buildingImplement@][,TRANSACTION_DATE = @transactionDate@][,TOTAL_PRICE = @declareTransactionTotalPrice@][,UNIT_PRICE = @declareTransactionUnitPrice@][,HOUSE_OTHER_DESCRIPTION = @houseOtherDescription@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]


30000038 停车位详情
--delete sjjs_bz.real_estate_cpi WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_CPI set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HOUSE_TYPE = @houseType@][,EVALUATE_PARTITION = @evaluatePartition@][,HOUSE_LOCATION = @houseLocation@][,PM_NAME = @pmName@]
[,REAL_ESTATE_CERTIFICATE_NUM = @realEstateCertificateNum@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,PROPERTY_RIGHT_OWNER = @propertyRightOwner@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@]
[,BUILDING_AREA = @buildingArea@][,FLOOR = @floor@][,INTERNAL_AREA = @internalArea@][,LAND_USE_RIGHT_TYPE = @landUseRightType@][,LAND_USE_RIGHT_END_DATE = @landUseRightEndDate@][,REMAINING_LAND_USE_ERM = @remainingLandUseErm@]
[,PARKING_SPACE_TYPE = @parkingSpaceType@][,TRANSACTION_DATE = @transactionDate@][,TOTAL_PRICE = @declareTransactionTotalPrice@][,UNIT_PRICE = @declareTransactionUnitPrice@][,HOUSE_OTHER_DESCRIPTION = @houseOtherDescription@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]


30000039 附件列表
--delete sjjs_bz.real_estate_file WHERE [id=@id@] [reciid=@reciid@]

30000040 房管主表详情
--delete sjjs_bz.real_estate_rcmt WHERE [id=@id@] [reciid=@reciid@]
update sjjs_bz.REAL_ESTATE_RCMT set UPDATE_TIME=sysdate [,RECIID = @reciid@][,HALL_WINDOW_NUM = @hallWindowNum@][,ACCEPTANCE_NUM = @acceptanceNum@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,SELLER_NAME = @sellerName@]
[,SELLER_ID_NUM = @sellerIdNum@][,BUYER_NAME = @buyerName@][,BUYER_ID_NUM = @buyerIdNum@][,PUSH_STATUS = @pushStatus@][,REMARK = @remark@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@] WHERE [id=@id@]


---------------------附件表添加字段---------------------------
sjjs_bz.real_estate_file 这个表添加字段
nsrsfqz VARCHAR(20) 纳税人是否已签字,
sfxyswqr VARCHAR(20) 是否需要税务确认,
swsfyqr VARCHAR(20) 税务是否确认,
swsfqz VARCHAR(20) 税务是否签字/签章,
gdzt VARCHAR(20) 归档状态,
gldh VARCHAR(50) 关联单号,
zldjxh VARCHAR(50) 资料登记序号,
zlurlnw  VARCHAR(200) 资料URL
sfts VARCHAR(20) 是否推送,

----------------更新接口-----------------------------------
real_estate_file[存量房交易-文件表] - 写入real_estate_file
30000027
INSERT INTO sjjs_bz.REAL_ESTATE_FILE(ID, CONFIG_ID, RECIID, DOCUMENT_TYPE, OWNERSHIP, NAME, PATH, URL, TYPE, FILE_SIZE, CONTENT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED) 
VALUES ([@id@], [@configId@], [@reciid@], [@documentType@], [@ownership@], [@name@], [@path@], [@url@], [@type@], [@size@], [@content@], [@creator@],[TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')],  [@deleted@])

附件信息更新 30000030
update sjjs_bz.REAL_ESTATE_FILE set UPDATE_TIME=sysdate [,CONFIG_ID = @configId@][,RECIID = @reciid@][,DOCUMENT_TYPE = @documentType@][,OWNERSHIP = @ownership@][,NAME = @name@][,PATH = @path@]
[,URL = @url@][,TYPE = @type@][,FILE_SIZE = @size@][,CONTENT = @content@][,UPDATER = @updater@][,DELETED = @deleted@]
[,nsrsfqz = @nsrsfqz@][,sfxyswqr = @sfxyswqr@][,swsfyqr = @swsfyqr@][,swsfqz = @swsfqz@][,gdzt = @gdzt@][,gldh = @gldh@][,zldjxh = @zldjxh@][,zlurlnw = @zlurlnw@][,sfts = @sfts@] where id=[@id@]

--20250322 查询存量房个人附件列表 修改 30000017
	SELECT * FROM sjjs_bz.real_estate_file
	WHERE deleted = 0
	[AND ownership IN (@ownership@)]
	[AND reciid=@reciid@]
	[AND document_type=@documentType@]
	[AND id=@id@]
	[AND gdzt=@gdzt@];

--20250707 新增房管中心办理人字段
ALTER TABLE SJJS_BZ.REAL_ESTATE_RCMT ADD FGZXBLR VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_RCMT.FGZXBLR IS '房管中心办理人';

房管材料数据插入 30000028
INSERT INTO sjjs_bz.REAL_ESTATE_RCMT(ID, RECIID, HALL_WINDOW_NUM, ACCEPTANCE_NUM, REAL_ESTATE_UNIT_NUM, SELLER_NAME, SELLER_ID_NUM, BUYER_NAME, BUYER_ID_NUM, PUSH_STATUS, REMARK, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,FGZXBLR) VALUES ([@id@], [@reciid@], [@hallWindowNum@], [@acceptanceNum@], [@realEstateUnitNum@], [@sellerName@], [@sellerIdNum@], [@buyerName@], [@buyerIdNum@], [@pushStatus@], [@remark@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@fgzxblr@] );

房管材料详情查询 30000018
SELECT * FROM sjjs_bz.real_estate_rcmt WHERE deleted = 0 [AND id=@id@] [AND reciid=@reciid@]

存量房税费审核列表 30000008
SELECT i.id, i.apply_num, i.apply_time, i.apply_status, mt.acceptance_num,mt.fgzxblr, tmp.real_estate_unit_num ,
tmp.real_estate_name ,tmp.house_location, i.house_type ,tmp.residential_type ,tmp.building_area ,
tmp.total_price, i.applicant, i.id_num,  bi.name, bi.id_num id_num_2 , i.update_time
FROM sjjs_bz.real_estate_collection_info i
left join sjjs_bz.real_estate_buyer_info bi on i.id = bi.reciid and bi.deleted = 0 and bi.sort=1
left join sjjs_bz.real_estate_rcmt mt on i.id = mt.reciid and mt.deleted = 0
left join (
	select h.reciid ,h.real_estate_unit_num ,h.real_estate_name ,h.house_location ,h.residential_type ,h.building_area ,h.total_price from sjjs_bz.real_estate_chi h WHERE deleted = 0
	UNION ALL
	select b.reciid ,b.real_estate_unit_num ,'' real_estate_name ,b.house_location ,'' residential_type ,b.building_area ,b.total_price from sjjs_bz.real_estate_cbi b WHERE deleted = 0
	UNION ALL
	select a.reciid ,a.real_estate_unit_num ,'' real_estate_name ,a.house_location ,'' residential_type ,a.building_area ,a.total_price from sjjs_bz.real_estate_cai a WHERE deleted = 0
	UNION ALL
	select o.reciid ,o.real_estate_unit_num ,'' real_estate_name ,o.house_location ,'' residential_type ,o.building_area ,o.total_price from sjjs_bz.real_estate_coi o WHERE deleted = 0
	UNION ALL
	select p.reciid ,p.real_estate_unit_num ,'' real_estate_name ,p.house_location ,'' residential_type ,p.building_area ,p.total_price from sjjs_bz.real_estate_cpi p WHERE deleted = 0
) tmp on i.id = tmp.reciid
where i.deleted = 0
[and mt.acceptance_num = @acceptanceNum@]
[and tmp.real_estate_unit_num = @realEstateUnitNum@]
[and i.applicant LIKE '%@applicant@%']
[and bi.name LIKE '%@name@%']
[and i.phone = @phone@]
[and bi.phone = @buyerPhone@]
ORDER BY i.create_time DESC

---------20250710调整--------------------
--卖方增加交易比例、是否直系亲属转让、是否委托办理字段
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD JYBL VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.JYBL IS '交易比例';
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD SFZXQSZR VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.SFZXQSZR IS '是否直系亲属转让';
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD SFWTBL VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.SFWTBL IS '是否委托办理';
--卖方信息表写入 30000029
INSERT INTO sjjs_bz.REAL_ESTATE_SELLER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, HOLDING_RATIO,SHARE_SITUATION, SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,JYBL,SFZXQSZR,SFWTBL)
VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@jybl@], [@sfzxqszr@], [@sfwtbl@]);

--买方增加是否委托办理字段
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD SFWTBL VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.SFWTBL IS '是否委托办理';
--买方信息表写入 30000020
INSERT INTO sjjs_bz.REAL_ESTATE_BUYER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, OBLIGEE_NATURE, HOLDING_RATIO,  SHARE_SITUATION,  SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,SFWTBL)
VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@obligeeNature@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@sfwtbl@]);

--增加初审人员、复审人员信息保存
ALTER TABLE SJJS_BZ.REAL_ESTATE_COLLECTION_INFO ADD CSRYBM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.CSRYBM IS '初审人员编码';
ALTER TABLE SJJS_BZ.REAL_ESTATE_COLLECTION_INFO ADD CSRYMC VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.CSRYMC IS '初审人员名称';
ALTER TABLE SJJS_BZ.REAL_ESTATE_COLLECTION_INFO ADD FSRYBM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.FSRYBM IS '复审人员编码';
ALTER TABLE SJJS_BZ.REAL_ESTATE_COLLECTION_INFO ADD FSRYMC VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.FSRYMC IS '复审人员名称';

--新增房管中心办理人编码字段
ALTER TABLE SJJS_BZ.REAL_ESTATE_RCMT ADD FGZXBLRBM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_RCMT.FGZXBLRBM IS '房管中心办理人编码';

--房管材料数据插入 30000028
INSERT INTO sjjs_bz.REAL_ESTATE_RCMT(ID, RECIID, HALL_WINDOW_NUM, ACCEPTANCE_NUM, REAL_ESTATE_UNIT_NUM, SELLER_NAME, SELLER_ID_NUM, BUYER_NAME, BUYER_ID_NUM, PUSH_STATUS, REMARK, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,FGZXBLR,FGZXBLRBM)
VALUES ([@id@], [@reciid@], [@hallWindowNum@], [@acceptanceNum@], [@realEstateUnitNum@], [@sellerName@], [@sellerIdNum@], [@buyerName@], [@buyerIdNum@], [@pushStatus@], [@remark@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@fgzxblr@], [@fgzxblrbm@] );

--存量房税费审核列表 30000008
SELECT i.id, i.apply_num, i.apply_time, i.apply_status,i.csrymc,i.fsrymc,i.house_type,i.applicant, i.id_num,i.update_time,
tmp.real_estate_unit_num ,tmp.real_estate_name ,tmp.house_location, tmp.residential_type ,tmp.building_area,
tmp.total_price, bi.name, bi.id_num id_num_2 ,mt.acceptance_num,mt.fgzxblr
FROM sjjs_bz.real_estate_collection_info i
left join sjjs_bz.real_estate_buyer_info bi on i.id = bi.reciid and bi.deleted = 0 and bi.sort=1
left join sjjs_bz.real_estate_rcmt mt on i.id = mt.reciid and mt.deleted = 0
left join (
	select h.reciid ,h.real_estate_unit_num ,h.real_estate_name ,h.house_location ,h.residential_type ,h.building_area ,h.total_price from sjjs_bz.real_estate_chi h WHERE deleted = 0
	UNION ALL
	select b.reciid ,b.real_estate_unit_num ,'' real_estate_name ,b.house_location ,'' residential_type ,b.building_area ,b.total_price from sjjs_bz.real_estate_cbi b WHERE deleted = 0
	UNION ALL
	select a.reciid ,a.real_estate_unit_num ,'' real_estate_name ,a.house_location ,'' residential_type ,a.building_area ,a.total_price from sjjs_bz.real_estate_cai a WHERE deleted = 0
	UNION ALL
	select o.reciid ,o.real_estate_unit_num ,'' real_estate_name ,o.house_location ,'' residential_type ,o.building_area ,o.total_price from sjjs_bz.real_estate_coi o WHERE deleted = 0
	UNION ALL
	select p.reciid ,p.real_estate_unit_num ,'' real_estate_name ,p.house_location ,'' residential_type ,p.building_area ,p.total_price from sjjs_bz.real_estate_cpi p WHERE deleted = 0
) tmp on i.id = tmp.reciid
where i.deleted = 0
[and mt.acceptance_num = @acceptanceNum@]
[and tmp.real_estate_unit_num = @realEstateUnitNum@]
[and i.applicant LIKE '%@applicant@%']
[and bi.name LIKE '%@name@%']
[and i.phone = @phone@]
[and bi.phone = @buyerPhone@]
ORDER BY i.create_time DESC

--主表审核更新 30000019
UPDATE sjjs_bz.real_estate_collection_info SET apply_status = [@applyStatus@] [,apply_remark = @applyRemark@] [, UPDATER = @updater@] [, UPDATE_TIME = TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')]
[, CSRYBM = @csrybm@] [, CSRYMC = @csrymc@] [, FSRYBM = @fsrybm@] [, FSRYMC = @fsrymc@]
WHERE deleted = 0 AND [id=@id@]

---------20250714调整--------------------
--卖方增加是否享受个税减免、家庭成员姓名关系、家庭成员身份证号
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD SFXSGSJM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.SFXSGSJM IS '是否享受个税减免';
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD JTCYXMGX VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.JTCYXMGX IS '家庭成员姓名关系';
ALTER TABLE SJJS_BZ.REAL_ESTATE_SELLER_INFO ADD JTCYSFZH VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_SELLER_INFO.JTCYSFZH IS '家庭成员身份证号';
--写入卖方信息表 30000029
INSERT INTO sjjs_bz.REAL_ESTATE_SELLER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, HOLDING_RATIO,SHARE_SITUATION, SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,JYBL,SFZXQSZR,SFWTBL,SFXSGSJM,JTCYXMGX,JTCYSFZH)
VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@jybl@], [@sfzxqszr@], [@sfwtbl@], [@sfxsgsjm@], [@jtcyxmgx@], [@jtcysfzh@]);

--买方增加是否享受契税减免、配偶姓名/身份证号、未成年子女姓名/身份证号1、未成年子女姓名/身份证号2、契税减免项
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD SFXSQSJM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.SFXSQSJM IS '是否享受契税减免';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD POXM VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.POXM IS '配偶姓名';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD POSFZH VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.POSFZH IS '配偶身份证号';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD WCNZNXM1 VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.WCNZNXM1 IS '未成年子女姓名1';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD WCNZNSFZH1 VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.WCNZNSFZH1 IS '未成年子女身份证号1';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD WCNZNXM2 VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.WCNZNXM2 IS '未成年子女姓名2';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD WCNZNSFZH2 VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.WCNZNSFZH2 IS '未成年子女身份证号2';
ALTER TABLE SJJS_BZ.REAL_ESTATE_BUYER_INFO ADD QSJMX VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_BUYER_INFO.QSJMX IS '契税减免项';
--写入买方信息表 30000020
INSERT INTO sjjs_bz.REAL_ESTATE_BUYER_INFO(ID, RECIID, OBLIGEE_TYPE, NAME, ID_TYPE, ID_NUM, PHONE, MARITAL_STATUS, HOUSE_NUMBER, OBLIGEE_NATURE, HOLDING_RATIO,  SHARE_SITUATION,  SORT, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED,SFWTBL,SFXSQSJM,POXM,POSFZH,WCNZNXM1,WCNZNSFZH1,WCNZNXM2,WCNZNSFZH2,QSJMX)
VALUES ([@id@], [@reciid@], [@obligeeType@], [@name@], [@idType@], [@idNum@], [@phone@], [@maritalStatus@], [@houseNumber@], [@obligeeNature@], [@holdingRatio@], [@shareSituation@], [@sort@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@sfwtbl@], [@sfxsqsjm@], [@poxm@], [@posfzh@], [@wcnznxm1@], [@wcnznsfzh1@], [@wcnznxm2@], [@wcnznsfzh2@], [@qsjmx@]);

--主表增加退回目标字段
ALTER TABLE SJJS_BZ.REAL_ESTATE_COLLECTION_INFO ADD REJECTTARGET VARCHAR2(100) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.REJECTTARGET IS '退回目标（卖方、买方、房管）';
--写入存量房交易信息采集信息表主表 30000024
INSERT INTO sjjs_bz.REAL_ESTATE_COLLECTION_INFO(ID, APPLY_NUM, APPLICANT, APPLY_TIME, ID_TYPE, ID_NUM, PHONE, REAL_ESTATE_UNIT_NUM, APPLY_STATUS, SELLER_STATUS, BUYER_STATUS, SHARE_SITUATION, HOUSE_TYPE, REMARK, APPLY_REMARK,HOUSE_PURPOSE,CERTIFICATE_TYPE,PAYMENT_METHOD, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED, REJECTTARGET)
VALUES ([@id@], [@applyNum@], [@applicant@], [TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')], [@idType@], [@idNum@], [@phone@], [@realEstateUnitNum@], [@applyStatus@], [@sellerStatus@], [@buyerStatus@], [@shareSituation@], [@houseType@], [@remark@], [@applyRemark@], [@housePurpose@], [@certificateType@], [@paymentMethod@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@rejectTarget@]);

---------20250716调整字段大小--------------------
ALTER TABLE REAL_ESTATE_COLLECTION_INFO MODIFY APPLY_STATUS VARCHAR2(255);
ALTER TABLE REAL_ESTATE_COLLECTION_INFO MODIFY SELLER_STATUS VARCHAR2(255) DEFAULT '0';
ALTER TABLE REAL_ESTATE_COLLECTION_INFO MODIFY BUYER_STATUS VARCHAR2(255) DEFAULT '0';

--主表字段更新 30000031
update sjjs_bz.REAL_ESTATE_COLLECTION_INFO set UPDATE_TIME=sysdate [,APPLY_NUM = @applyNum@][,APPLICANT = @applicant@][,APPLY_TIME = TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')][,ID_TYPE = @idType@][,ID_NUM = @idNum@]
[,PHONE = @phone@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,APPLY_STATUS = @applyStatus@][,SELLER_STATUS = @sellerStatus@][,BUYER_STATUS = @buyerStatus@][,SHARE_SITUATION = @shareSituation@][,HOUSE_TYPE = @houseType@]
[,REMARK = @remark@][,APPLY_REMARK = @applyRemark@][,HOUSE_PURPOSE = @housePurpose@][,CERTIFICATE_TYPE = @certificateType@][,PAYMENT_METHOD = @paymentMethod@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@][,REJECTTARGET = @rejectTarget@] WHERE [id=@id@]

--主表审核更新 30000019
UPDATE sjjs_bz.real_estate_collection_info SET apply_status = [@applyStatus@] [,apply_remark = @applyRemark@] [, UPDATER = @updater@] [, UPDATE_TIME = TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')]
[, CSRYBM = @csrybm@] [, CSRYMC = @csrymc@] [, FSRYBM = @fsrybm@] [, FSRYMC = @fsrymc@] [,REJECTTARGET = @rejectTarget@]
WHERE deleted = 0 AND [id=@id@]

--卖方字段更新 30000032
update sjjs_bz.REAL_ESTATE_SELLER_INFO set UPDATE_TIME=sysdate [,RECIID = @reciid@][,OBLIGEE_TYPE = @obligeeType@][,NAME = @name@]
[,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,MARITAL_STATUS = @maritalStatus@]
[,HOUSE_NUMBER = @houseNumber@][,HOLDING_RATIO = @holdingRatio@][,SHARE_SITUATION = @shareSituation@][,SORT = @sort@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@]
[,JYBL = @jybl@][,SFZXQSZR = @sfzxqszr@][,SFWTBL = @sfwtbl@][,SFXSGSJM = @sfxsgsjm@][,JTCYXMGX = @jtcyxmgx@][,JTCYSFZH = @jtcysfzh@] WHERE [id=@id@]


--买方字段更新 30000033
update sjjs_bz.REAL_ESTATE_BUYER_INFO set UPDATE_TIME=sysdate [,RECIID = @reciid@][,OBLIGEE_TYPE = @obligeeType@]
[,NAME = @name@][,ID_TYPE = @idType@][,ID_NUM = @idNum@][,PHONE = @phone@][,MARITAL_STATUS = @maritalStatus@]
[,HOUSE_NUMBER = @houseNumber@][,OBLIGEE_NATURE = @obligeeNature@][,HOLDING_RATIO = @holdingRatio@][, SHARE_SITUATION = @shareSituation@][, SORT = @sort@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@]
[,SFWTBL = @sfwtbl@][,SFXSQSJM = @sfxsqsjm@][,POXM = @poxm@][,POSFZH = @posfzh@][,WCNZNXM1 = @wcnznxm1@][,WCNZNSFZH1 = @wcnznsfzh1@][,WCNZNXM2 = @wcnznxm2@][,WCNZNSFZH2 = @wcnznsfzh2@][,QSJMX = @qsjmx@] WHERE [id=@id@]

---------20250804新增表、新增表字段--------------------
CREATE TABLE CLFXXCJGRJTCY(
    UUID VARCHAR2(50) NOT NULL,
    BUYER_ID VARCHAR2(50) NOT NULL,
    XM VARCHAR2(255),
    GX VARCHAR2(255),
    SFZH VARCHAR2(255),
    LRRDM VARCHAR2(64),
    LRRQ DATE DEFAULT SYSDATE NOT NULL,
    XGRDM VARCHAR2(64),
    XGRQ DATE DEFAULT SYSDATE NOT NULL,
    YXBZ VARCHAR2(1) DEFAULT  'Y' NOT NULL,
    PRIMARY KEY (UUID)
);

COMMENT ON TABLE CLFXXCJGRJTCY IS '存量房信息采集个人家庭成员（买方）';
COMMENT ON COLUMN CLFXXCJGRJTCY.UUID IS 'uuid';
COMMENT ON COLUMN CLFXXCJGRJTCY.BUYER_ID IS '买方id';
COMMENT ON COLUMN CLFXXCJGRJTCY.XM IS '姓名';
COMMENT ON COLUMN CLFXXCJGRJTCY.GX IS '关系;配偶、未成年子女';
COMMENT ON COLUMN CLFXXCJGRJTCY.SFZH IS '身份证号';
COMMENT ON COLUMN CLFXXCJGRJTCY.LRRDM IS '录入人代码';
COMMENT ON COLUMN CLFXXCJGRJTCY.LRRQ IS '录入日期';
COMMENT ON COLUMN CLFXXCJGRJTCY.XGRDM IS '修改人代码';
COMMENT ON COLUMN CLFXXCJGRJTCY.XGRQ IS '修改日期';
COMMENT ON COLUMN CLFXXCJGRJTCY.YXBZ IS '有效标志;Y、N';

--家庭成员表插入 （61000453）
INSERT INTO sjjs_bz.CLFXXCJGRJTCY(UUID, BUYER_ID, XM, GX, SFZH, LRRDM, LRRQ, XGRDM, XGRQ, YXBZ)
VALUES ([@uuid@], [@buyerId@], [@xm@], [@gx@], [@sfzh@], [@lrrdm@], [TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')], [@xgrdm@], [TO_DATE(@xgrq@, 'YYYY-MM-DD HH24:MI:SS')], [@yxbz@]);

--家庭成员表更新 (61000454)
UPDATE
	sjjs_bz.CLFXXCJGRJTCY
SET
	XGRQ = sysdate
	[,BUYER_ID = @buyerId@]
	[,XM = @xm@]
	[,GX = @gx@]
	[,SFZH = @sfzh@]
	[,LRRQ = TO_DATE(@lrrq@, 'YYYY-MM-DD HH24:MI:SS')]
	[,LRRDM = @lrrdm@]
	[,XGRDM = @xgrdm@]
	[,YXBZ = @yxbz@]
WHERE
	[uuid = @uuid@]

--家庭成员表查询 （61000455）
SELECT * FROM sjjs_bz.CLFXXCJGRJTCY WHERE yxbz = 'Y' [AND BUYER_ID=@buyerId@];

--存量房个人主表新增表字段
ALTER TABLE REAL_ESTATE_COLLECTION_INFO ADD XXGMK VARCHAR2(500) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.XXGMK IS '需修改模块';
ALTER TABLE REAL_ESTATE_COLLECTION_INFO ADD XCQWJ VARCHAR2(500) NULL;
COMMENT ON COLUMN REAL_ESTATE_COLLECTION_INFO.XCQWJ IS '需重签文件';

--写入存量房交易信息采集信息表主表 30000024
INSERT INTO sjjs_bz.REAL_ESTATE_COLLECTION_INFO(ID, APPLY_NUM, APPLICANT, APPLY_TIME, ID_TYPE, ID_NUM, PHONE, REAL_ESTATE_UNIT_NUM, APPLY_STATUS, SELLER_STATUS, BUYER_STATUS, SHARE_SITUATION, HOUSE_TYPE, REMARK, APPLY_REMARK,HOUSE_PURPOSE,CERTIFICATE_TYPE,PAYMENT_METHOD, CREATOR, CREATE_TIME, UPDATER, UPDATE_TIME, DELETED, REJECTTARGET,XXGMK,XCQWJ)
VALUES ([@id@], [@applyNum@], [@applicant@], [TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')], [@idType@], [@idNum@], [@phone@], [@realEstateUnitNum@], [@applyStatus@], [@sellerStatus@], [@buyerStatus@], [@shareSituation@], [@houseType@], [@remark@], [@applyRemark@], [@housePurpose@], [@certificateType@], [@paymentMethod@], [@creator@], [TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')], [@updater@], [TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')], [@deleted@], [@rejectTarget@], [@xxgmk@], [@xcqwj@]);

--主表字段更新 30000031
update sjjs_bz.REAL_ESTATE_COLLECTION_INFO set UPDATE_TIME=sysdate [,APPLY_NUM = @applyNum@][,APPLICANT = @applicant@][,APPLY_TIME = TO_DATE(@applyTime@, 'YYYY-MM-DD HH24:MI:SS')][,ID_TYPE = @idType@][,ID_NUM = @idNum@]
[,PHONE = @phone@][,REAL_ESTATE_UNIT_NUM = @realEstateUnitNum@][,APPLY_STATUS = @applyStatus@][,SELLER_STATUS = @sellerStatus@][,BUYER_STATUS = @buyerStatus@][,SHARE_SITUATION = @shareSituation@][,HOUSE_TYPE = @houseType@]
[,REMARK = @remark@][,APPLY_REMARK = @applyRemark@][,HOUSE_PURPOSE = @housePurpose@][,CERTIFICATE_TYPE = @certificateType@][,PAYMENT_METHOD = @paymentMethod@]
[,CREATOR = @creator@][,CREATE_TIME = TO_DATE(@createTime@, 'YYYY-MM-DD HH24:MI:SS')][,UPDATER = @updater@][,DELETED = @deleted@][,REJECTTARGET = @rejectTarget@][,XXGMK = @xxgmk@][,XCQWJ = @xcqwj@] WHERE [id=@id@]

--主表审核更新 30000019
UPDATE sjjs_bz.real_estate_collection_info SET apply_status = [@applyStatus@] [,apply_remark = @applyRemark@] [, UPDATER = @updater@] [, UPDATE_TIME = TO_DATE(@updateTime@, 'YYYY-MM-DD HH24:MI:SS')]
[, CSRYBM = @csrybm@] [, CSRYMC = @csrymc@] [, FSRYBM = @fsrybm@] [, FSRYMC = @fsrymc@] [,REJECTTARGET = @rejectTarget@][,XXGMK = @xxgmk@][,XCQWJ = @xcqwj@]
WHERE deleted = 0 AND [id=@id@]